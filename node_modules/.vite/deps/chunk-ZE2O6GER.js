import {
  allowance,
  approve
} from "./chunk-QVAFZEUG.js";
import {
  resolvePromisedValue
} from "./chunk-EGKW4L7U.js";
import {
  getContract
} from "./chunk-7RWWVHOG.js";
import {
  getAddress
} from "./chunk-FFXQ6EIY.js";

// node_modules/thirdweb/dist/esm/extensions/erc20/write/getApprovalForTransaction.js
async function getApprovalForTransaction(options) {
  const { transaction, account } = options;
  if (!account) {
    return null;
  }
  const erc20Value = await resolvePromisedValue(transaction.erc20Value);
  if (erc20Value) {
    const target = await resolvePromisedValue(transaction.to);
    if (!target || !erc20Value.tokenAddress || getAddress(target) === getAddress(erc20Value.tokenAddress)) {
      return null;
    }
    const contract = getContract({
      address: erc20Value.tokenAddress,
      chain: transaction.chain,
      client: transaction.client
    });
    const approvedValue = await allowance({
      contract,
      owner: account.address,
      spender: target
    });
    if (approvedValue > erc20Value.amountWei) {
      return null;
    }
    return approve({
      contract,
      value: erc20Value.amountWei,
      spender: target
    });
  }
  return null;
}

export {
  getApprovalForTransaction
};
//# sourceMappingURL=chunk-ZE2O6GER.js.map
