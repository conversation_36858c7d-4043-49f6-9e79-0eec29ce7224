{"version": 3, "sources": ["../../thirdweb/src/extensions/erc20/write/getApprovalForTransaction.ts"], "sourcesContent": ["import { getContract } from \"../../../contract/contract.js\";\nimport type { PreparedTransaction } from \"../../../transaction/prepare-transaction.js\";\nimport { getAddress } from \"../../../utils/address.js\";\nimport { resolvePromisedValue } from \"../../../utils/promise/resolve-promised-value.js\";\nimport type { Account } from \"../../../wallets/interfaces/wallet.js\";\nimport { allowance } from \"../__generated__/IERC20/read/allowance.js\";\nimport { approve } from \"../__generated__/IERC20/write/approve.js\";\n\nexport type GetApprovalForTransactionParams = {\n  /**\n   * The transaction that involves the ERC20 token\n   */\n  transaction: PreparedTransaction;\n  /**\n   * The caller's account\n   */\n  account: Account;\n};\n\n/**\n * When dealing with transactions that involve ERC20 tokens (Airdropping ERC20, buy NFTs with ERC20, etc.)\n * you often have to do a pre-check to see if the targeted contract has the sufficient allowance to \"take\" the ERC20 tokens from the caller's wallet.\n *\n * This extension is a handy method that checks for the allowance and requests to approve for more if current allowance is insufficient\n *\n * @param options GetApprovalForTransactionParams\n * @returns a PreparedTransaction\n *\n * @example\n * ```ts\n * import { getApprovalForTransaction } from \"thirdweb/extensions/erc20\";\n * import { sendAndConfirmTransaction } from \"thirdweb\";\n *\n * async function buyNFT() {\n *   const buyTransaction = ... // could be a marketplacev3's buyFromListing\n *\n *   // Check if you need to approve spending for the involved ERC20 contract\n *   const approveTx = await getApprovalForTransaction({\n *     transaction: buyTransaction,\n *     account, // the connected account\n *   });\n *   if (approveTx) {\n *     await sendAndConfirmTransaction({\n *       transaction: approveTx,\n *       account,\n *     })\n *   }\n *   // Once approved, you can finally perform the buy transaction\n *   await sendAndConfirmTransaction({\n *     transaction: buyTransaction,\n *     account,\n *   });\n * }\n * ```\n *\n * @transaction\n */\nexport async function getApprovalForTransaction(\n  options: GetApprovalForTransactionParams,\n): Promise<PreparedTransaction | null> {\n  const { transaction, account } = options;\n  if (!account) {\n    return null;\n  }\n\n  const erc20Value = await resolvePromisedValue(transaction.erc20Value);\n  if (erc20Value) {\n    const target = await resolvePromisedValue(transaction.to);\n\n    if (\n      !target ||\n      !erc20Value.tokenAddress ||\n      getAddress(target) === getAddress(erc20Value.tokenAddress)\n    ) {\n      return null;\n    }\n\n    const contract = getContract({\n      address: erc20Value.tokenAddress,\n      chain: transaction.chain,\n      client: transaction.client,\n    });\n\n    const approvedValue = await allowance({\n      contract,\n      owner: account.address,\n      spender: target,\n    });\n\n    if (approvedValue > erc20Value.amountWei) {\n      return null;\n    }\n\n    return approve({\n      contract,\n      value: erc20Value.amountWei,\n      spender: target,\n    });\n  }\n\n  return null;\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAyDA,eAAsB,0BACpB,SAAwC;AAExC,QAAM,EAAE,aAAa,QAAO,IAAK;AACjC,MAAI,CAAC,SAAS;AACZ,WAAO;EACT;AAEA,QAAM,aAAa,MAAM,qBAAqB,YAAY,UAAU;AACpE,MAAI,YAAY;AACd,UAAM,SAAS,MAAM,qBAAqB,YAAY,EAAE;AAExD,QACE,CAAC,UACD,CAAC,WAAW,gBACZ,WAAW,MAAM,MAAM,WAAW,WAAW,YAAY,GACzD;AACA,aAAO;IACT;AAEA,UAAM,WAAW,YAAY;MAC3B,SAAS,WAAW;MACpB,OAAO,YAAY;MACnB,QAAQ,YAAY;KACrB;AAED,UAAM,gBAAgB,MAAM,UAAU;MACpC;MACA,OAAO,QAAQ;MACf,SAAS;KACV;AAED,QAAI,gBAAgB,WAAW,WAAW;AACxC,aAAO;IACT;AAEA,WAAO,QAAQ;MACb;MACA,OAAO,WAAW;MAClB,SAAS;KACV;EACH;AAEA,SAAO;AACT;", "names": []}