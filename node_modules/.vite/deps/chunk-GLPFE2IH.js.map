{"version": 3, "sources": ["../../thirdweb/src/utils/bytecode/is-contract-deployed.ts"], "sourcesContent": ["import { getBytecode } from \"../../contract/actions/get-bytecode.js\";\nimport type { ThirdwebContract } from \"../../contract/contract.js\";\n\n// we use a weak set to cache *if* a contract *is* deployed\n// aka: we add it to this set if it's deployed, and only if it is deployed\n// instead of using a map with: true only (because we only want to cache if it's deployed)\nconst cache = new WeakSet<ThirdwebContract>();\n\n/**\n * Checks if a contract is deployed by verifying its bytecode.\n * @param contract - The contract to check.\n * @returns A promise that resolves to a boolean indicating whether the contract is deployed or not.\n * @example\n * ```ts\n * import { getContract } from \"thirdweb/contract\";\n * import { isContractDeployed } from \"thirdweb/contract/utils\";\n *\n * const contract = getContract({ ... });\n * const isDeployed = await isContractDeployed(contract);\n * console.log(isDeployed);\n * ```\n * @contract\n */\nexport async function isContractDeployed(\n  contract: ThirdwebContract,\n): Promise<boolean> {\n  if (cache.has(contract)) {\n    // it's only in there if it's deployed\n    return true;\n  }\n  // this already dedupes requests for the same contract\n  const bytecode = await getBytecode(contract);\n  const isDeployed = bytecode !== \"0x\";\n  // if it's deployed, we add it to the cache\n  if (isDeployed) {\n    cache.add(contract);\n  }\n  return isDeployed;\n}\n"], "mappings": ";;;;;AAMA,IAAM,QAAQ,oBAAI,QAAO;AAiBzB,eAAsB,mBACpB,UAA0B;AAE1B,MAAI,MAAM,IAAI,QAAQ,GAAG;AAEvB,WAAO;EACT;AAEA,QAAM,WAAW,MAAM,YAAY,QAAQ;AAC3C,QAAM,aAAa,aAAa;AAEhC,MAAI,YAAY;AACd,UAAM,IAAI,QAAQ;EACpB;AACA,SAAO;AACT;", "names": []}