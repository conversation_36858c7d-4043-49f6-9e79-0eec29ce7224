{"version": 3, "sources": ["../../thirdweb/src/extensions/erc721/__generated__/IDrop/write/claim.ts", "../../thirdweb/src/extensions/erc721/drops/write/claimTo.ts"], "sourcesContent": ["import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport type {\n  BaseTransactionOptions,\n  WithOverrides,\n} from \"../../../../../transaction/types.js\";\nimport { prepareContractCall } from \"../../../../../transaction/prepare-contract-call.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { once } from \"../../../../../utils/promise/once.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"claim\" function.\n */\nexport type ClaimParams = WithOverrides<{\n  receiver: AbiParameterToPrimitiveType<{ type: \"address\"; name: \"receiver\" }>;\n  quantity: AbiParameterToPrimitiveType<{ type: \"uint256\"; name: \"quantity\" }>;\n  currency: AbiParameterToPrimitiveType<{ type: \"address\"; name: \"currency\" }>;\n  pricePerToken: AbiParameterToPrimitiveType<{\n    type: \"uint256\";\n    name: \"pricePerToken\";\n  }>;\n  allowlistProof: AbiParameterToPrimitiveType<{\n    type: \"tuple\";\n    name: \"allowlistProof\";\n    components: [\n      { type: \"bytes32[]\"; name: \"proof\" },\n      { type: \"uint256\"; name: \"quantityLimitPerWallet\" },\n      { type: \"uint256\"; name: \"pricePerToken\" },\n      { type: \"address\"; name: \"currency\" },\n    ];\n  }>;\n  data: AbiParameterToPrimitiveType<{ type: \"bytes\"; name: \"data\" }>;\n}>;\n\nexport const FN_SELECTOR = \"0x84bb1e42\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"address\",\n    name: \"receiver\",\n  },\n  {\n    type: \"uint256\",\n    name: \"quantity\",\n  },\n  {\n    type: \"address\",\n    name: \"currency\",\n  },\n  {\n    type: \"uint256\",\n    name: \"pricePerToken\",\n  },\n  {\n    type: \"tuple\",\n    name: \"allowlistProof\",\n    components: [\n      {\n        type: \"bytes32[]\",\n        name: \"proof\",\n      },\n      {\n        type: \"uint256\",\n        name: \"quantityLimitPerWallet\",\n      },\n      {\n        type: \"uint256\",\n        name: \"pricePerToken\",\n      },\n      {\n        type: \"address\",\n        name: \"currency\",\n      },\n    ],\n  },\n  {\n    type: \"bytes\",\n    name: \"data\",\n  },\n] as const;\nconst FN_OUTPUTS = [] as const;\n\n/**\n * Checks if the `claim` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `claim` method is supported.\n * @extension ERC721\n * @example\n * ```ts\n * import { isClaimSupported } from \"thirdweb/extensions/erc721\";\n *\n * const supported = isClaimSupported([\"0x...\"]);\n * ```\n */\nexport function isClaimSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"claim\" function.\n * @param options - The options for the claim function.\n * @returns The encoded ABI parameters.\n * @extension ERC721\n * @example\n * ```ts\n * import { encodeClaimParams } from \"thirdweb/extensions/erc721\";\n * const result = encodeClaimParams({\n *  receiver: ...,\n *  quantity: ...,\n *  currency: ...,\n *  pricePerToken: ...,\n *  allowlistProof: ...,\n *  data: ...,\n * });\n * ```\n */\nexport function encodeClaimParams(options: ClaimParams) {\n  return encodeAbiParameters(FN_INPUTS, [\n    options.receiver,\n    options.quantity,\n    options.currency,\n    options.pricePerToken,\n    options.allowlistProof,\n    options.data,\n  ]);\n}\n\n/**\n * Encodes the \"claim\" function into a Hex string with its parameters.\n * @param options - The options for the claim function.\n * @returns The encoded hexadecimal string.\n * @extension ERC721\n * @example\n * ```ts\n * import { encodeClaim } from \"thirdweb/extensions/erc721\";\n * const result = encodeClaim({\n *  receiver: ...,\n *  quantity: ...,\n *  currency: ...,\n *  pricePerToken: ...,\n *  allowlistProof: ...,\n *  data: ...,\n * });\n * ```\n */\nexport function encodeClaim(options: ClaimParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeClaimParams(options).slice(2)) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Prepares a transaction to call the \"claim\" function on the contract.\n * @param options - The options for the \"claim\" function.\n * @returns A prepared transaction object.\n * @extension ERC721\n * @example\n * ```ts\n * import { sendTransaction } from \"thirdweb\";\n * import { claim } from \"thirdweb/extensions/erc721\";\n *\n * const transaction = claim({\n *  contract,\n *  receiver: ...,\n *  quantity: ...,\n *  currency: ...,\n *  pricePerToken: ...,\n *  allowlistProof: ...,\n *  data: ...,\n *  overrides: {\n *    ...\n *  }\n * });\n *\n * // Send the transaction\n * await sendTransaction({ transaction, account });\n * ```\n */\nexport function claim(\n  options: BaseTransactionOptions<\n    | ClaimParams\n    | {\n        asyncParams: () => Promise<ClaimParams>;\n      }\n  >,\n) {\n  const asyncOptions = once(async () => {\n    return \"asyncParams\" in options ? await options.asyncParams() : options;\n  });\n\n  return prepareContractCall({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: async () => {\n      const resolvedOptions = await asyncOptions();\n      return [\n        resolvedOptions.receiver,\n        resolvedOptions.quantity,\n        resolvedOptions.currency,\n        resolvedOptions.pricePerToken,\n        resolvedOptions.allowlistProof,\n        resolvedOptions.data,\n      ] as const;\n    },\n    value: async () => (await asyncOptions()).overrides?.value,\n    accessList: async () => (await asyncOptions()).overrides?.accessList,\n    gas: async () => (await asyncOptions()).overrides?.gas,\n    gasPrice: async () => (await asyncOptions()).overrides?.gasPrice,\n    maxFeePerGas: async () => (await asyncOptions()).overrides?.maxFeePerGas,\n    maxPriorityFeePerGas: async () =>\n      (await asyncOptions()).overrides?.maxPriorityFeePerGas,\n    nonce: async () => (await asyncOptions()).overrides?.nonce,\n    extraGas: async () => (await asyncOptions()).overrides?.extraGas,\n    erc20Value: async () => (await asyncOptions()).overrides?.erc20Value,\n    authorizationList: async () =>\n      (await asyncOptions()).overrides?.authorizationList,\n  });\n}\n", "import type { Address } from \"abitype\";\nimport type { BaseTransactionOptions } from \"../../../../transaction/types.js\";\nimport { getClaimParams } from \"../../../../utils/extensions/drops/get-claim-params.js\";\nimport { isGetContractMetadataSupported } from \"../../../common/read/getContractMetadata.js\";\nimport {\n  claim,\n  isClaimSupported,\n} from \"../../__generated__/IDrop/write/claim.js\";\nimport { isClaimConditionSupported } from \"../../__generated__/IDropSinglePhase/read/claimCondition.js\";\nimport { isGetActiveClaimConditionSupported } from \"../read/getActiveClaimCondition.js\";\n\n/**\n * Represents the parameters for claiming an ERC721 token.\n * @extension ERC721\n */\nexport type ClaimToParams = {\n  to: Address;\n  quantity: bigint;\n  from?: Address;\n  singlePhaseDrop?: boolean;\n};\n\n/**\n * Claim ERC721 NFTs to a specified address\n * This method is only available on the `DropERC721` contract.\n * @param options - The options for the transaction\n * @extension ERC721\n * @example\n *\n * ### Basic usage\n * ```ts\n * import { claimTo } from \"thirdweb/extensions/erc721\";\n * import { sendTransaction } from \"thirdweb\";\n *\n * const transaction = claimTo({\n *   contract,\n *   to: \"0x...\",\n *   quantity: 1n,\n * });\n *\n * await sendTransaction({ transaction, account });\n * ```\n *\n * ### For Drops with allowlists\n * You need to specify the claimer address as the `from` param to avoid any issue with the allowlist\n * ```ts\n * const transaction = claimTo({\n *   contract,\n *   to: \"0x...\",\n *   quantity: 1n,\n *   from: \"0x...\", // address of the one claiming\n * });\n * ```\n * @throws If no claim condition is set\n * @returns A promise that resolves with the submitted transaction hash.\n */\nexport function claimTo(options: BaseTransactionOptions<ClaimToParams>) {\n  return claim({\n    contract: options.contract,\n    asyncParams: () =>\n      getClaimParams({\n        type: \"erc721\",\n        contract: options.contract,\n        to: options.to,\n        quantity: options.quantity,\n        from: options.from,\n        singlePhaseDrop: options.singlePhaseDrop,\n      }),\n  });\n}\n\n/**\n * Checks if the `claimTo` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `claimTo` method is supported.\n * @extension ERC721\n * @example\n * ```ts\n * import { isClaimToSupported } from \"thirdweb/extensions/erc721\";\n *\n * const supported = isClaimToSupported([\"0x...\"]);\n * ```\n */\nexport function isClaimToSupported(availableSelectors: string[]) {\n  return (\n    isClaimSupported(availableSelectors) &&\n    // requires contractMetadata for claimer proofs\n    isGetContractMetadataSupported(availableSelectors) &&\n    // required to check if the contract supports the getActiveClaimCondition method\n    (isGetActiveClaimConditionSupported(availableSelectors) ||\n      isClaimConditionSupported(availableSelectors))\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCO,IAAM,cAAc;AAC3B,IAAM,YAAY;EAChB;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;IACN,YAAY;MACV;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;;;EAIZ;IACE,MAAM;IACN,MAAM;;;AAGV,IAAM,aAAa,CAAA;AAcb,SAAU,iBAAiB,oBAA4B;AAC3D,SAAO,aAAa;IAClB;IACA,QAAQ,CAAC,aAAa,WAAW,UAAU;GAC5C;AACH;AAmFM,SAAU,MACd,SAKC;AAED,QAAM,eAAe,KAAK,YAAW;AACnC,WAAO,iBAAiB,UAAU,MAAM,QAAQ,YAAW,IAAK;EAClE,CAAC;AAED,SAAO,oBAAoB;IACzB,UAAU,QAAQ;IAClB,QAAQ,CAAC,aAAa,WAAW,UAAU;IAC3C,QAAQ,YAAW;AACjB,YAAM,kBAAkB,MAAM,aAAY;AAC1C,aAAO;QACL,gBAAgB;QAChB,gBAAgB;QAChB,gBAAgB;QAChB,gBAAgB;QAChB,gBAAgB;QAChB,gBAAgB;;IAEpB;IACA,OAAO,YAAS;AA1MpB;AA0MwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,YAAY,YAAS;AA3MzB;AA2M6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,KAAK,YAAS;AA5MlB;AA4MsB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACnD,UAAU,YAAS;AA7MvB;AA6M2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,cAAc,YAAS;AA9M3B;AA8M+B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC5D,sBAAsB,YAAS;AA/MnC;AAgNO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACpC,OAAO,YAAS;AAjNpB;AAiNwB,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACrD,UAAU,YAAS;AAlNvB;AAkN2B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IACxD,YAAY,YAAS;AAnNzB;AAmN6B,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;IAC1D,mBAAmB,YAAS;AApNhC;AAqNO,0BAAM,aAAY,GAAI,cAAtB,mBAAiC;;GACrC;AACH;;;ACpKM,SAAU,QAAQ,SAA8C;AACpE,SAAO,MAAM;IACX,UAAU,QAAQ;IAClB,aAAa,MACX,eAAe;MACb,MAAM;MACN,UAAU,QAAQ;MAClB,IAAI,QAAQ;MACZ,UAAU,QAAQ;MAClB,MAAM,QAAQ;MACd,iBAAiB,QAAQ;KAC1B;GACJ;AACH;AAcM,SAAU,mBAAmB,oBAA4B;AAC7D,SACE,iBAAiB,kBAAkB;EAEnC,uBAA+B,kBAAkB;GAEhD,mCAAmC,kBAAkB,KACpD,0BAA0B,kBAAkB;AAElD;", "names": []}