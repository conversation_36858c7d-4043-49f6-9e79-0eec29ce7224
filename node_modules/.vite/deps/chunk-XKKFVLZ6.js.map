{"version": 3, "sources": ["../../thirdweb/src/auth/constants.ts", "../../thirdweb/src/auth/serialize-erc6492-signature.ts", "../../ox/erc6492/WrappedSignature.ts", "../../thirdweb/src/extensions/erc1271/__generated__/isValidSignature/read/isValidSignature.ts", "../../thirdweb/src/utils/encoding/from-bytes.ts", "../../thirdweb/src/auth/verify-hash.ts", "../../thirdweb/src/utils/hashing/hashMessage.ts", "../../thirdweb/src/utils/hashing/hashTypedData.ts", "../../thirdweb/src/wallets/smart/lib/signing.ts"], "sourcesContent": ["export const ERC_6492_MAGIC_VALUE =\n  \"0x6492649264926492649264926492649264926492649264926492649264926492\" as const;\n", "import { encodeAbiParameters } from \"../utils/abi/encodeAbiParameters.js\";\nimport { concatHex } from \"../utils/encoding/helpers/concat-hex.js\";\nimport type { Hex } from \"../utils/encoding/hex.js\";\nimport { ERC_6492_MAGIC_VALUE } from \"./constants.js\";\nimport type { Erc6492Signature } from \"./types.js\";\n\n/**\n * Serializes a signature for use with [ERC-6492](https://eips.ethereum.org/EIPS/eip-6492). The signature must be generated by a signer for an [ERC-4337](https://eips.ethereum.org/EIPS/eip-4337) Account Factory account with counterfactual deployment addresses.\n *\n * @param {@link Erc6492Signature} signature  The signature object to serialize into Hex format\n * @param {string} signature.address The ERC-4337 Account Factory address\n * @param {Hex} signature.data Account deployment calldata (if not deployed) for counterfactual verification\n * @param {Hex} signature.signature The original signature\n *\n * @returns {Hex} The serialized signature\n *\n * @example\n * ```ts\n * import { serializeErc6492Signature } from 'thirdweb/auth';\n *\n * const serializedSignature = serializeErc6492Signature({\n *  address: '0x...',\n *  data: '0x...',\n *  signature: '0x...',\n * });\n * // 0x000000000000000000000000cafebabecafebabecafebabecafebabecafebabe000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000000000a00000000000000000000000000000000000000000000000000000000000000004deadbeef000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000041a461f509887bd19e312c0c58467ce8ff8e300d3c1a90b608a760c5b80318eaf15fe57c96f9175d6cd4daad4663763baa7e78836e067d0163e9a2ccf2ff753f5b1b000000000000000000000000000000000000000000000000000000000000006492649264926492649264926492649264926492649264926492649264926492\n * ```\n * @auth\n */\nexport function serializeErc6492Signature({\n  address,\n  data,\n  signature,\n}: Erc6492Signature): Hex {\n  return concatHex([\n    encodeAbiParameters(\n      [{ type: \"address\" }, { type: \"bytes\" }, { type: \"bytes\" }],\n      [address, data, signature],\n    ),\n    ERC_6492_MAGIC_VALUE,\n  ]);\n}\n", "import type * as Abi from '../core/Abi.js'\nimport * as AbiParameters from '../core/AbiParameters.js'\nimport type * as Address from '../core/Address.js'\nimport * as Errors from '../core/Errors.js'\nimport * as Hex from '../core/Hex.js'\nimport * as Signature from '../core/Signature.js'\n\n/** ERC-6492 Wrapped Signature. */\nexport type WrappedSignature = {\n  /** Calldata to pass to the target address for counterfactual verification. */\n  data: Hex.Hex\n  /** The original signature. */\n  signature: Signature.Signature\n  /** The target address to use for counterfactual verification. */\n  to: Address.Address\n}\n\n/**\n * Magic bytes used to identify ERC-6492 wrapped signatures.\n */\nexport const magicBytes =\n  '0x6492649264926492649264926492649264926492649264926492649264926492' as const\n\n/**\n * Deployless ERC-6492 signature verification bytecode.\n */\nexport const universalSignatureValidatorBytecode =\n  '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'\n\n/**\n * ABI for the ERC-6492 universal deployless signature validator contract.\n *\n * Constructor return value is `0x1` (valid) or `0x0` (invalid).\n */\nexport const universalSignatureValidatorAbi = [\n  {\n    inputs: [\n      {\n        name: '_signer',\n        type: 'address',\n      },\n      {\n        name: '_hash',\n        type: 'bytes32',\n      },\n      {\n        name: '_signature',\n        type: 'bytes',\n      },\n    ],\n    stateMutability: 'nonpayable',\n    type: 'constructor',\n  },\n  {\n    inputs: [\n      {\n        name: '_signer',\n        type: 'address',\n      },\n      {\n        name: '_hash',\n        type: 'bytes32',\n      },\n      {\n        name: '_signature',\n        type: 'bytes',\n      },\n    ],\n    outputs: [\n      {\n        type: 'bool',\n      },\n    ],\n    stateMutability: 'nonpayable',\n    type: 'function',\n    name: 'isValidSig',\n  },\n] as const satisfies Abi.Abi\n\n/**\n * Asserts that the wrapped signature is valid.\n *\n * @example\n * ```ts twoslash\n * import { WrappedSignature } from 'ox/erc6492'\n *\n * WrappedSignature.assert('0xdeadbeef')\n * // @error: InvalidWrappedSignatureError: Value `0xdeadbeef` is an invalid ERC-6492 wrapped signature.\n * ```\n *\n * @param wrapped - The wrapped signature to assert.\n */\nexport function assert(wrapped: Hex.Hex) {\n  if (Hex.slice(wrapped, -32) !== magicBytes)\n    throw new InvalidWrappedSignatureError(wrapped)\n}\n\nexport declare namespace assert {\n  type ErrorType =\n    | InvalidWrappedSignatureError\n    | Hex.slice.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Parses an [ERC-6492 wrapped signature](https://eips.ethereum.org/EIPS/eip-6492#specification) into its constituent parts.\n *\n * @example\n * ```ts twoslash\n * // @noErrors\n * import { Secp256k1 } from 'ox'\n * import { WrappedSignature } from 'ox/erc6492' // [!code focus]\n *\n * const signature = Secp256k1.sign({\n *   payload: '0x...',\n *   privateKey: '0x...',\n * })\n *\n * // Instantiate from serialized format. // [!code focus]\n * const wrapped = WrappedSignature.from('0x...') // [!code focus]\n * // @log: { data: '0x...', signature: { ... }, to: '0x...', } // [!code focus]\n *\n * // Instantiate from constituent parts. // [!code focus]\n * const wrapped = WrappedSignature.from({ // [!code focus]\n *   data: '0x...', // [!code focus]\n *   signature, // [!code focus]\n *   to: '0x...', // [!code focus]\n * })\n * // @log: { data: '0x...', signature: { ... }, to: '0x...', }\n * ```\n *\n * @param wrapped - Wrapped signature to parse.\n * @returns Wrapped signature.\n */\nexport function from(wrapped: WrappedSignature | Hex.Hex): WrappedSignature {\n  if (typeof wrapped === 'string') return fromHex(wrapped)\n  return wrapped\n}\n\nexport declare namespace from {\n  type ReturnType = WrappedSignature\n\n  type ErrorType =\n    | AbiParameters.from.ErrorType\n    | AbiParameters.decode.ErrorType\n    | Signature.fromHex.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Parses an [ERC-6492 wrapped signature](https://eips.ethereum.org/EIPS/eip-6492#specification) into its constituent parts.\n *\n * @example\n * ```ts twoslash\n * import { WrappedSignature } from 'ox/erc6492'\n *\n * const { data, signature, to } = WrappedSignature.fromHex('0x...')\n * ```\n *\n * @param wrapped - Wrapped signature to parse.\n * @returns Wrapped signature.\n */\nexport function fromHex(wrapped: Hex.Hex): WrappedSignature {\n  assert(wrapped)\n\n  const [to, data, signature_hex] = AbiParameters.decode(\n    AbiParameters.from('address, bytes, bytes'),\n    wrapped,\n  )\n\n  const signature = Signature.fromHex(signature_hex)\n\n  return { data, signature, to }\n}\n\nexport declare namespace fromHex {\n  type ErrorType =\n    | AbiParameters.from.ErrorType\n    | AbiParameters.decode.ErrorType\n    | Signature.fromHex.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Serializes an [ERC-6492 wrapped signature](https://eips.ethereum.org/EIPS/eip-6492#specification).\n *\n * @example\n * ```ts twoslash\n * import { Secp256k1 } from 'ox'\n * import { WrappedSignature } from 'ox/erc6492' // [!code focus]\n *\n * const signature = Secp256k1.sign({\n *   payload: '0x...',\n *   privateKey: '0x...',\n * })\n *\n * const wrapped = WrappedSignature.toHex({ // [!code focus]\n *   data: '0xdeadbeef', // [!code focus]\n *   signature, // [!code focus]\n *   to: '******************************************', // [!code focus]\n * }) // [!code focus]\n * ```\n *\n * @param value - Wrapped signature to serialize.\n * @returns Serialized wrapped signature.\n */\nexport function toHex(value: WrappedSignature): Hex.Hex {\n  const { data, signature, to } = value\n\n  return Hex.concat(\n    AbiParameters.encode(AbiParameters.from('address, bytes, bytes'), [\n      to,\n      data,\n      Signature.toHex(signature),\n    ]),\n    magicBytes,\n  )\n}\n\nexport declare namespace toHex {\n  type ErrorType =\n    | AbiParameters.encode.ErrorType\n    | Hex.concat.ErrorType\n    | Signature.toHex.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Validates a wrapped signature. Returns `true` if the wrapped signature is valid, `false` otherwise.\n *\n * @example\n * ```ts twoslash\n * import { WrappedSignature } from 'ox/erc6492'\n *\n * const valid = WrappedSignature.validate('0xdeadbeef')\n * // @log: false\n * ```\n *\n * @param wrapped - The wrapped signature to validate.\n * @returns `true` if the wrapped signature is valid, `false` otherwise.\n */\nexport function validate(wrapped: Hex.Hex): boolean {\n  try {\n    assert(wrapped)\n    return true\n  } catch {\n    return false\n  }\n}\n\nexport declare namespace validate {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/** Thrown when the ERC-6492 wrapped signature is invalid. */\nexport class InvalidWrappedSignatureError extends Errors.BaseError {\n  override readonly name = 'WrappedSignature.InvalidWrappedSignatureError'\n\n  constructor(wrapped: Hex.Hex) {\n    super(`Value \\`${wrapped}\\` is an invalid ERC-6492 wrapped signature.`)\n  }\n}\n", "import type { AbiParameterToPrimitiveType } from \"abitype\";\nimport { readContract } from \"../../../../../transaction/read-contract.js\";\nimport type { BaseTransactionOptions } from \"../../../../../transaction/types.js\";\nimport { encodeAbiParameters } from \"../../../../../utils/abi/encodeAbiParameters.js\";\nimport { decodeAbiParameters } from \"viem\";\nimport type { Hex } from \"../../../../../utils/encoding/hex.js\";\nimport { detectMethod } from \"../../../../../utils/bytecode/detectExtension.js\";\n\n/**\n * Represents the parameters for the \"isValidSignature\" function.\n */\nexport type IsValidSignatureParams = {\n  hash: AbiParameterToPrimitiveType<{ type: \"bytes32\"; name: \"hash\" }>;\n  signature: AbiParameterToPrimitiveType<{ type: \"bytes\"; name: \"signature\" }>;\n};\n\nexport const FN_SELECTOR = \"0x1626ba7e\" as const;\nconst FN_INPUTS = [\n  {\n    type: \"bytes32\",\n    name: \"hash\",\n  },\n  {\n    type: \"bytes\",\n    name: \"signature\",\n  },\n] as const;\nconst FN_OUTPUTS = [\n  {\n    type: \"bytes4\",\n  },\n] as const;\n\n/**\n * Checks if the `isValidSignature` method is supported by the given contract.\n * @param availableSelectors An array of 4byte function selectors of the contract. You can get this in various ways, such as using \"whatsabi\" or if you have the ABI of the contract available you can use it to generate the selectors.\n * @returns A boolean indicating if the `isValidSignature` method is supported.\n * @extension ERC1271\n * @example\n * ```ts\n * import { isIsValidSignatureSupported } from \"thirdweb/extensions/erc1271\";\n * const supported = isIsValidSignatureSupported([\"0x...\"]);\n * ```\n */\nexport function isIsValidSignatureSupported(availableSelectors: string[]) {\n  return detectMethod({\n    availableSelectors,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n  });\n}\n\n/**\n * Encodes the parameters for the \"isValidSignature\" function.\n * @param options - The options for the isValidSignature function.\n * @returns The encoded ABI parameters.\n * @extension ERC1271\n * @example\n * ```ts\n * import { encodeIsValidSignatureParams } from \"thirdweb/extensions/erc1271\";\n * const result = encodeIsValidSignatureParams({\n *  hash: ...,\n *  signature: ...,\n * });\n * ```\n */\nexport function encodeIsValidSignatureParams(options: IsValidSignatureParams) {\n  return encodeAbiParameters(FN_INPUTS, [options.hash, options.signature]);\n}\n\n/**\n * Encodes the \"isValidSignature\" function into a Hex string with its parameters.\n * @param options - The options for the isValidSignature function.\n * @returns The encoded hexadecimal string.\n * @extension ERC1271\n * @example\n * ```ts\n * import { encodeIsValidSignature } from \"thirdweb/extensions/erc1271\";\n * const result = encodeIsValidSignature({\n *  hash: ...,\n *  signature: ...,\n * });\n * ```\n */\nexport function encodeIsValidSignature(options: IsValidSignatureParams) {\n  // we do a \"manual\" concat here to avoid the overhead of the \"concatHex\" function\n  // we can do this because we know the specific formats of the values\n  return (FN_SELECTOR +\n    encodeIsValidSignatureParams(options).slice(\n      2,\n    )) as `${typeof FN_SELECTOR}${string}`;\n}\n\n/**\n * Decodes the result of the isValidSignature function call.\n * @param result - The hexadecimal result to decode.\n * @returns The decoded result as per the FN_OUTPUTS definition.\n * @extension ERC1271\n * @example\n * ```ts\n * import { decodeIsValidSignatureResult } from \"thirdweb/extensions/erc1271\";\n * const result = decodeIsValidSignatureResultResult(\"...\");\n * ```\n */\nexport function decodeIsValidSignatureResult(result: Hex) {\n  return decodeAbiParameters(FN_OUTPUTS, result)[0];\n}\n\n/**\n * Calls the \"isValidSignature\" function on the contract.\n * @param options - The options for the isValidSignature function.\n * @returns The parsed result of the function call.\n * @extension ERC1271\n * @example\n * ```ts\n * import { isValidSignature } from \"thirdweb/extensions/erc1271\";\n *\n * const result = await isValidSignature({\n *  contract,\n *  hash: ...,\n *  signature: ...,\n * });\n *\n * ```\n */\nexport async function isValidSignature(\n  options: BaseTransactionOptions<IsValidSignatureParams>,\n) {\n  return readContract({\n    contract: options.contract,\n    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS] as const,\n    params: [options.hash, options.signature],\n  });\n}\n", "import * as ox__Bytes from \"ox/Bytes\";\nimport { type Hex, uint8ArrayToHex } from \"./hex.js\";\n\nexport type FromBytesParameters<\n  TTo extends \"string\" | \"hex\" | \"bigint\" | \"number\" | \"boolean\",\n> =\n  | TTo\n  | {\n      /** Size of the bytes. */\n      size?: number;\n      /** Type to convert to. */\n      to: TTo;\n    };\n\nexport type FromBytesReturnType<TTo> = TTo extends \"string\"\n  ? string\n  : TTo extends \"hex\"\n    ? Hex\n    : TTo extends \"bigint\"\n      ? bigint\n      : TTo extends \"number\"\n        ? number\n        : TTo extends \"boolean\"\n          ? boolean\n          : never;\n\n/**\n * Converts a Uint8Array to the specified type.\n * @param bytes - The Uint8Array to convert.\n * @param toOrOpts - The target type or conversion options.\n * @returns The converted value of the specified type.\n * @example\n * ```ts\n * import { fromBytes } from \"thirdweb/utils\";\n * const bytes = new Uint8Array([1, 164]);\n * const number = fromBytes(bytes, \"number\");\n * console.log(number); // 420\n * ```\n * @utils\n */\nexport function fromBytes<\n  TTo extends \"string\" | \"hex\" | \"bigint\" | \"number\" | \"boolean\",\n>(\n  bytes: Uint8Array,\n  toOrOpts: FromBytesParameters<TTo>,\n): FromBytesReturnType<TTo> {\n  const opts = typeof toOrOpts === \"string\" ? { to: toOrOpts } : toOrOpts;\n  switch (opts.to) {\n    case \"number\":\n      return bytesToNumber(bytes, opts) as FromBytesReturnType<TTo>;\n    case \"bigint\":\n      return bytesToBigInt(bytes, opts) as FromBytesReturnType<TTo>;\n    case \"boolean\":\n      return bytesToBool(bytes, opts) as FromBytesReturnType<TTo>;\n    case \"string\":\n      return bytesToString(bytes, opts) as FromBytesReturnType<TTo>;\n    default:\n      return uint8ArrayToHex(bytes, opts) as FromBytesReturnType<TTo>;\n  }\n}\n\nexport type BytesToBigIntOpts = {\n  /** Whether or not the number of a signed representation. */\n  signed?: boolean;\n  /** Size of the bytes. */\n  size?: number;\n};\n\n/**\n * Converts a Uint8Array of bytes to a bigint.\n * @param bytes - The Uint8Array of bytes to convert.\n * @param opts - Optional parameters for the conversion.\n * @returns The converted bigint.\n * @example\n * ```ts\n * import { bytesToBigInt } from \"thirdweb/utils\";\n * const bytes = new Uint8Array([1, 164]);\n * const bigInt = bytesToBigInt(bytes);\n * console.log(bigInt); // 420n\n * ```\n * @utils\n */\nexport function bytesToBigInt(\n  bytes: Uint8Array,\n  opts: BytesToBigIntOpts = {},\n): bigint {\n  return ox__Bytes.toBigInt(bytes, opts);\n}\n\nexport type BytesToBoolOpts = ox__Bytes.toBoolean.Options;\n\n/**\n * Converts a byte array to a boolean value.\n * @param bytes_ - The byte array to convert.\n * @param opts - Optional parameters for the conversion.\n * @returns The boolean value converted from the byte array.\n * @throws Error if the byte array is invalid or the boolean representation is invalid.\n * @example\n * ```ts\n * import { bytesToBool } from \"thirdweb/utils\";\n * const bytes = new Uint8Array([1]);\n * const bool = bytesToBool(bytes);\n * console.log(bool); // true\n * ```\n * @utils\n */\nexport function bytesToBool(\n  bytes_: Uint8Array,\n  opts: BytesToBoolOpts = {},\n): boolean {\n  return ox__Bytes.toBoolean(bytes_, opts);\n}\n\nexport type BytesToNumberOpts = BytesToBigIntOpts;\n\n/**\n * Converts a Uint8Array of bytes to a number.\n * @param bytes - The Uint8Array of bytes to convert.\n * @param opts - Optional configuration options.\n * @returns The converted number.\n * @example\n * ```ts\n * import { bytesToNumber } from \"thirdweb/utils\";\n * const bytes = new Uint8Array([1, 164]);\n * const number = bytesToNumber(bytes);\n * console.log(number); // 420\n * ```\n * @utils\n */\nexport function bytesToNumber(\n  bytes: Uint8Array,\n  opts: BytesToNumberOpts = {},\n): number {\n  return ox__Bytes.toNumber(bytes, opts);\n}\n\nexport type BytesToStringOpts = ox__Bytes.toString.Options;\n\n/**\n * Converts an array of bytes to a string using UTF-8 encoding.\n * @param bytes_ - The array of bytes to convert.\n * @param opts - Optional parameters for the conversion.\n * @returns The resulting string.\n * @example\n * ```ts\n * import { bytesToString } from \"thirdweb/utils\";\n * const bytes = new Uint8Array([72, 101, 108, 108, 111]);\n * const string = bytesToString(bytes);\n * console.log(string); // \"Hello\"\n * ```\n * @utils\n */\nexport function bytesToString(\n  bytes_: Uint8Array,\n  opts: BytesToStringOpts = {},\n): string {\n  return ox__Bytes.toString(bytes_, opts);\n}\n", "import * as ox__<PERSON>bi from \"ox/Abi\";\nimport * as ox__AbiConstructor from \"ox/AbiConstructor\";\nimport * as ox__AbiFunction from \"ox/AbiFunction\";\nimport * as ox__Signature from \"ox/Signature\";\nimport { WrappedSignature as ox__WrappedSignature } from \"ox/erc6492\";\nimport type { Chain } from \"../chains/types.js\";\nimport type { ThirdwebClient } from \"../client/client.js\";\nimport { type ThirdwebContract, getContract } from \"../contract/contract.js\";\nimport { isValidSignature } from \"../extensions/erc1271/__generated__/isValidSignature/read/isValidSignature.js\";\nimport { eth_call } from \"../rpc/actions/eth_call.js\";\nimport { getRpcClient } from \"../rpc/rpc.js\";\nimport type { Address } from \"../utils/address.js\";\nimport { isZkSyncChain } from \"../utils/any-evm/zksync/isZkSyncChain.js\";\nimport { isContractDeployed } from \"../utils/bytecode/is-contract-deployed.js\";\nimport { fromBytes } from \"../utils/encoding/from-bytes.js\";\nimport { type Hex, hexToBool, isHex } from \"../utils/encoding/hex.js\";\nimport { serializeErc6492Signature } from \"./serialize-erc6492-signature.js\";\n\nexport type VerifyHashParams = {\n  hash: Hex;\n  signature: string | Uint8Array | ox__Signature.Signature;\n  address: string;\n  client: ThirdwebClient;\n  chain: Chain;\n  accountFactory?: {\n    address: string;\n    verificationCalldata: Hex;\n  };\n};\n\nconst ZKSYNC_VALIDATOR_ADDRESS: Address =\n  \"******************************************\";\n\n/**\n * Verify that an address created the provided signature for a given hash using [ERC-6492](https://eips.ethereum.org/EIPS/eip-6492). This function is interoperable with all wallet types, including EOAs.\n * This function should rarely be used directly, instead use @see {import(\"./verify-signature.js\")} and @see {import(\"./verify-typed-data.js\")}}\n *\n * @param {Hex} options.hash The hash that was signed\n * @param {string | Uint8Array | Signature} options.signature The signature that was signed\n * @param {string} options.address The address that signed the hash\n * @param {ThirdwebClient} options.client The Thirdweb client\n * @param {Chain} options.chain The chain that the address is on. For an EOA, this can be any chain.\n * @param {string} [options.accountFactory.address] The address of the account factory that created the account if using a smart account with a custom account factory\n * @param {Hex} [options.accountFactory.verificationCalldata] The calldata that was used to create the account if using a smart account with a custom account factory\n *\n * @returns {Promise<boolean>} A promise that resolves to `true` if the signature is valid, or `false` otherwise.\n *\n * @example\n * ```ts\n * import { verifyHash } from \"thirdweb/utils\";\n * const isValid = await verifyHash({\n *   hash: \"0x1234\",\n *   signature: \"0x1234\",\n *   address: \"0x1234\",\n *   client,\n *   chain,\n * });\n * ```\n *\n * @auth\n */\nexport async function verifyHash({\n  hash,\n  signature,\n  address,\n  client,\n  chain,\n  accountFactory,\n}: VerifyHashParams): Promise<boolean> {\n  const signatureHex = (() => {\n    if (isHex(signature)) return signature;\n    if (typeof signature === \"object\" && \"r\" in signature && \"s\" in signature)\n      return ox__Signature.toHex(signature);\n    if (signature instanceof Uint8Array) return fromBytes(signature, \"hex\");\n    // We should never hit this but TS doesn't know that\n    throw new Error(\n      `Invalid signature type for signature ${signature}: ${typeof signature}`,\n    );\n  })();\n\n  const isDeployed = await isContractDeployed(\n    getContract({\n      address,\n      client,\n      chain,\n    }),\n  );\n\n  if (isDeployed) {\n    const validEip1271 = await verifyEip1271Signature({\n      hash,\n      signature: signatureHex,\n      contract: getContract({\n        chain,\n        address,\n        client,\n      }),\n    }).catch((err) => {\n      console.error(\"Error verifying EIP-1271 signature\", err);\n      return false;\n    });\n    if (validEip1271) {\n      return true;\n    }\n  }\n\n  // contract not deployed, use erc6492 validator to verify signature\n  const wrappedSignature: Hex = await (async () => {\n    // If no factory is provided, we have to assume its already deployed or is an EOA\n    // TODO: Figure out how to automatically tell if our default factory was used\n    if (!accountFactory) return signatureHex;\n\n    // If this sigature was already wrapped for ERC-6492, carry on\n    if (ox__WrappedSignature.validate(signatureHex)) return signatureHex;\n\n    // Otherwise, serialize the signature for ERC-6492 validation\n    return serializeErc6492Signature({\n      address: accountFactory.address,\n      data: accountFactory.verificationCalldata,\n      signature: signatureHex,\n    });\n  })();\n\n  let verificationData: {\n    to?: Address;\n    data: Hex;\n  };\n\n  const zkSyncChain = await isZkSyncChain(chain);\n  const abi = ox__Abi.from(ox__WrappedSignature.universalSignatureValidatorAbi);\n  if (zkSyncChain) {\n    // zksync chains dont support deploying code with eth_call\n    // need to call a deployed contract instead\n    verificationData = {\n      to: ZKSYNC_VALIDATOR_ADDRESS,\n      data: ox__AbiFunction.encodeData(\n        ox__AbiFunction.fromAbi(abi, \"isValidSig\"),\n        [address, hash, wrappedSignature],\n      ),\n    };\n  } else {\n    const validatorConstructor = ox__AbiConstructor.fromAbi(abi);\n    verificationData = {\n      data: ox__AbiConstructor.encode(validatorConstructor, {\n        args: [address, hash, wrappedSignature],\n        bytecode: ox__WrappedSignature.universalSignatureValidatorBytecode,\n      }),\n    };\n  }\n\n  const rpcRequest = getRpcClient({\n    chain,\n    client,\n  });\n\n  try {\n    const result = await eth_call(rpcRequest, verificationData);\n    return hexToBool(result);\n  } catch {\n    // Some chains do not support the eth_call simulation and will fail, so we fall back to regular EIP1271 validation\n    const validEip1271 = await verifyEip1271Signature({\n      hash,\n      signature: signatureHex,\n      contract: getContract({\n        chain,\n        address,\n        client,\n      }),\n    }).catch((err) => {\n      console.error(\"Error verifying EIP-1271 signature\", err);\n      return false;\n    });\n    if (validEip1271) {\n      return true;\n    }\n    // TODO: Improve overall RPC error handling so we can tell if this was an actual verification failure or some other error\n    // Verification failed somehow\n    return false;\n  }\n}\n\nconst EIP_1271_MAGIC_VALUE = \"0x1626ba7e\";\nexport async function verifyEip1271Signature({\n  hash,\n  signature,\n  contract,\n}: {\n  hash: Hex;\n  signature: Hex;\n  contract: ThirdwebContract;\n}): Promise<boolean> {\n  try {\n    const result = await isValidSignature({\n      hash,\n      signature,\n      contract,\n    });\n    return result === EIP_1271_MAGIC_VALUE;\n  } catch (err) {\n    console.error(\"Error verifying EIP-1271 signature\", err);\n    return false;\n  }\n}\n", "import * as ox__Bytes from \"ox/Bytes\";\nimport type { Hex } from \"../encoding/hex.js\";\nimport { stringToBytes, toBytes } from \"../encoding/to-bytes.js\";\nimport type { SignableMessage } from \"../types.js\";\nimport { keccak256 } from \"./keccak256.js\";\n\nconst presignMessagePrefix = \"\\x19Ethereum Signed Message:\\n\";\ntype To = \"hex\" | \"bytes\";\n\ntype HashMessage<TTo extends To> =\n  | (TTo extends \"bytes\" ? ox__Bytes.Bytes : never)\n  | (TTo extends \"hex\" ? Hex : never);\n\n/**\n * Ethereum Signed Message hashing\n * @param message - The message to hash, either as a string, a Uint8Array, or an object with a `raw` property containing a Uint8Array.\n * @param to_ - The desired output format of the hash (optional). Defaults to 'hex'.\n * @example\n * ```ts\n * import { hashMessage } from \"thirdweb/utils\";\n * const hash = hashMessage(\"hello world\");\n * ```\n * @returns The Ethereum Signed Message hash of the message in the specified format.\n * @utils\n */\nexport function hashMessage<TTo extends To = \"hex\">(\n  message: SignableMessage,\n  to_?: TTo,\n): HashMessage<TTo> {\n  const messageBytes = (() => {\n    if (typeof message === \"string\") {\n      return stringToBytes(message);\n    }\n    if (message.raw instanceof Uint8Array) {\n      return message.raw;\n    }\n    return toBytes(message.raw);\n  })();\n  const prefixBytes = stringToBytes(\n    `${presignMessagePrefix}${messageBytes.length}`,\n  );\n  return keccak256(ox__Bytes.concat(prefixBytes, messageBytes), to_);\n}\n", "import type * as ox__AbiParameters from \"ox/AbiParameters\";\nimport * as ox__Bytes from \"ox/Bytes\";\nimport * as ox__TypedData from \"ox/TypedData\";\nimport { encodeAbiParameters } from \"../abi/encodeAbiParameters.js\";\nimport { type Hex, toHex } from \"../encoding/hex.js\";\nimport { keccak256 } from \"./keccak256.js\";\n\ntype MessageTypeProperty = {\n  name: string;\n  type: string;\n};\n\nexport type HashTypedDataParams<\n  typedData extends\n    | ox__TypedData.TypedData\n    | Record<string, unknown> = ox__TypedData.TypedData,\n  primaryType extends keyof typedData | \"EIP712Domain\" = keyof typedData,\n> = ox__TypedData.Definition<typedData, primaryType>;\n\n/**\n * @internal\n */\nexport function hashTypedData<\n  const typedData extends ox__TypedData.TypedData | Record<string, unknown>,\n  primaryType extends keyof typedData | \"EIP712Domain\",\n>(parameters: HashTypedDataParams<typedData, primaryType>): Hex {\n  const {\n    domain = {},\n    message,\n    primaryType,\n  } = parameters as HashTypedDataParams;\n  const types = {\n    EIP712Domain: ox__TypedData.extractEip712DomainTypes(domain),\n    ...parameters.types,\n  };\n\n  // Need to do a runtime validation check on addresses, byte ranges, integer ranges, etc\n  // as we can't statically check this with TypeScript.\n  ox__TypedData.validate({\n    domain,\n    message,\n    primaryType,\n    types,\n  });\n\n  const parts: Hex[] = [\"0x1901\"];\n  if (domain)\n    parts.push(\n      ox__TypedData.hashDomain({\n        domain,\n        types: types as Record<string, MessageTypeProperty[]>,\n      }),\n    );\n\n  if (primaryType !== \"EIP712Domain\") {\n    const hashedStruct = (() => {\n      const encoded = encodeData({\n        data: message,\n        primaryType,\n        types: types as Record<string, MessageTypeProperty[]>,\n      });\n      return keccak256(encoded);\n    })();\n\n    parts.push(hashedStruct);\n  }\n\n  return keccak256(ox__Bytes.concat(...parts.map((p) => ox__Bytes.fromHex(p))));\n}\n\nfunction encodeData({\n  data,\n  primaryType,\n  types,\n}: {\n  data: Record<string, unknown>;\n  primaryType: string;\n  types: Record<string, MessageTypeProperty[]>;\n}) {\n  const encodedTypes: ox__AbiParameters.Parameter[] = [{ type: \"bytes32\" }];\n  const encodedValues: unknown[] = [hashType({ primaryType, types })];\n\n  if (!types[primaryType]) throw new Error(\"Invalid types\");\n  for (const field of types[primaryType]) {\n    const [type, value] = encodeField({\n      types,\n      name: field.name,\n      type: field.type,\n      value: data[field.name],\n    });\n    encodedTypes.push(type);\n    encodedValues.push(value);\n  }\n\n  return encodeAbiParameters(encodedTypes, encodedValues);\n}\n\nfunction hashType({\n  primaryType,\n  types,\n}: {\n  primaryType: string;\n  types: Record<string, MessageTypeProperty[]>;\n}) {\n  const encodedHashType = toHex(encodeType({ primaryType, types }));\n  return keccak256(encodedHashType);\n}\n\nfunction encodeType({\n  primaryType,\n  types,\n}: {\n  primaryType: string;\n  types: Record<string, MessageTypeProperty[]>;\n}) {\n  let result = \"\";\n  const unsortedDeps = findTypeDependencies({ primaryType, types });\n  unsortedDeps.delete(primaryType);\n\n  const deps = [primaryType, ...Array.from(unsortedDeps).sort()];\n  for (const type of deps) {\n    if (!types[type]) throw new Error(\"Invalid types\");\n    result += `${type}(${types[type]\n      .map(({ name, type: t }) => `${t} ${name}`)\n      .join(\",\")})`;\n  }\n\n  return result;\n}\n\nfunction findTypeDependencies(\n  {\n    primaryType: primaryType_,\n    types,\n  }: {\n    primaryType: string;\n    types: Record<string, MessageTypeProperty[]>;\n  },\n  results: Set<string> = new Set(),\n): Set<string> {\n  const match = primaryType_.match(/^\\w*/u);\n  const primaryType = match?.[0] as string;\n  if (results.has(primaryType) || types[primaryType] === undefined) {\n    return results;\n  }\n\n  results.add(primaryType);\n\n  for (const field of types[primaryType]) {\n    findTypeDependencies({ primaryType: field.type, types }, results);\n  }\n  return results;\n}\n\nfunction encodeField({\n  types,\n  name,\n  type,\n  value,\n}: {\n  types: Record<string, MessageTypeProperty[]>;\n  name: string;\n  type: string;\n  // biome-ignore lint/suspicious/noExplicitAny: Can't anticipate types of nested values\n  value: any;\n  // biome-ignore lint/suspicious/noExplicitAny: Can't anticipate types of nested values\n}): [type: ox__AbiParameters.Parameter, value: any] {\n  if (types[type] !== undefined) {\n    return [\n      { type: \"bytes32\" },\n      keccak256(encodeData({ data: value, primaryType: type, types })),\n    ];\n  }\n\n  if (type === \"bytes\") {\n    const prepend = value.length % 2 ? \"0\" : \"\";\n    value = `0x${prepend + value.slice(2)}`;\n    return [{ type: \"bytes32\" }, keccak256(value)];\n  }\n\n  if (type === \"string\") return [{ type: \"bytes32\" }, keccak256(toHex(value))];\n\n  if (type.lastIndexOf(\"]\") === type.length - 1) {\n    const parsedType = type.slice(0, type.lastIndexOf(\"[\"));\n    const typeValuePairs =\n      // biome-ignore lint/suspicious/noExplicitAny: Can't anticipate types of nested values\n      (value as [ox__AbiParameters.Parameter, any][]).map((item) =>\n        encodeField({\n          name,\n          type: parsedType,\n          types,\n          value: item,\n        }),\n      );\n    return [\n      { type: \"bytes32\" },\n      keccak256(\n        encodeAbiParameters(\n          typeValuePairs.map(([t]) => t),\n          typeValuePairs.map(([, v]) => v),\n        ),\n      ),\n    ];\n  }\n\n  return [{ type }, value];\n}\n", "import type * as ox__TypedData from \"ox/TypedData\";\nimport { serializeErc6492Signature } from \"../../../auth/serialize-erc6492-signature.js\";\nimport {\n  verifyEip1271Signature,\n  verifyHash,\n} from \"../../../auth/verify-hash.js\";\nimport type { Chain } from \"../../../chains/types.js\";\nimport type { ThirdwebClient } from \"../../../client/client.js\";\nimport {\n  type ThirdwebContract,\n  getContract,\n} from \"../../../contract/contract.js\";\nimport { encode } from \"../../../transaction/actions/encode.js\";\nimport { readContract } from \"../../../transaction/read-contract.js\";\nimport { encodeAbiParameters } from \"../../../utils/abi/encodeAbiParameters.js\";\nimport { isContractDeployed } from \"../../../utils/bytecode/is-contract-deployed.js\";\nimport type { Hex } from \"../../../utils/encoding/hex.js\";\nimport { hashMessage } from \"../../../utils/hashing/hashMessage.js\";\nimport { hashTypedData } from \"../../../utils/hashing/hashTypedData.js\";\nimport type { SignableMessage } from \"../../../utils/types.js\";\nimport type { Account } from \"../../../wallets/interfaces/wallet.js\";\nimport type { SmartAccountOptions } from \"../types.js\";\nimport { prepareCreateAccount } from \"./calls.js\";\n\n/**\n * If the account is already deployed, generate an ERC-1271 signature.\n * If the account is not deployed, generate an ERC-6492 signature unless otherwise specified.\n *\n * @internal\n */\nexport async function smartAccountSignMessage({\n  accountContract,\n  factoryContract,\n  options,\n  message,\n}: {\n  accountContract: ThirdwebContract;\n  factoryContract: ThirdwebContract;\n  options: SmartAccountOptions;\n  message: SignableMessage;\n}) {\n  const originalMsgHash = hashMessage(message);\n  const is712Factory = await checkFor712Factory({\n    factoryContract,\n    accountContract,\n    originalMsgHash,\n  });\n\n  let sig: `0x${string}`;\n  if (is712Factory) {\n    const wrappedMessageHash = encodeAbiParameters(\n      [{ type: \"bytes32\" }],\n      [originalMsgHash],\n    );\n\n    sig = await options.personalAccount.signTypedData({\n      domain: {\n        name: \"Account\",\n        version: \"1\",\n        chainId: options.chain.id,\n        verifyingContract: accountContract.address,\n      },\n      primaryType: \"AccountMessage\",\n      types: { AccountMessage: [{ name: \"message\", type: \"bytes\" }] },\n      message: { message: wrappedMessageHash },\n    });\n  } else {\n    sig = await options.personalAccount.signMessage({ message });\n  }\n\n  const isDeployed = await isContractDeployed(accountContract);\n  if (isDeployed) {\n    const isValid = await verifyEip1271Signature({\n      hash: originalMsgHash,\n      signature: sig,\n      contract: accountContract,\n    });\n    if (isValid) {\n      return sig;\n    }\n    throw new Error(\"Failed to verify signature\");\n  } else {\n    const deployTx = prepareCreateAccount({\n      factoryContract,\n      adminAddress: options.personalAccount.address,\n      accountSalt: options.overrides?.accountSalt,\n      createAccountOverride: options.overrides?.createAccount,\n    });\n    if (!deployTx) {\n      throw new Error(\"Create account override not provided\");\n    }\n    const initCode = await encode(deployTx);\n    const erc6492Sig = serializeErc6492Signature({\n      address: factoryContract.address,\n      data: initCode,\n      signature: sig,\n    });\n\n    // check if the signature is valid\n    const isValid = await verifyHash({\n      hash: originalMsgHash,\n      signature: erc6492Sig,\n      address: accountContract.address,\n      chain: accountContract.chain,\n      client: accountContract.client,\n    });\n\n    if (isValid) {\n      return erc6492Sig;\n    }\n    throw new Error(\"Unable to verify ERC-6492 signature after signing.\");\n  }\n}\n\nexport async function smartAccountSignTypedData<\n  const typedData extends ox__TypedData.TypedData | Record<string, unknown>,\n  primaryType extends keyof typedData | \"EIP712Domain\" = keyof typedData,\n>({\n  accountContract,\n  factoryContract,\n  options,\n  typedData,\n}: {\n  accountContract: ThirdwebContract;\n  factoryContract: ThirdwebContract;\n  options: SmartAccountOptions;\n  typedData: ox__TypedData.Definition<typedData, primaryType>;\n}) {\n  const isSelfVerifyingContract =\n    (\n      typedData.domain as ox__TypedData.Domain\n    )?.verifyingContract?.toLowerCase() ===\n    accountContract.address?.toLowerCase();\n\n  if (isSelfVerifyingContract) {\n    // if the contract is self-verifying, we can just sign the message with the EOA (ie. adding a session key)\n    return options.personalAccount.signTypedData(typedData);\n  }\n\n  const originalMsgHash = hashTypedData(typedData);\n  // check if the account contract supports EIP721 domain separator based signing\n  const is712Factory = await checkFor712Factory({\n    factoryContract,\n    accountContract,\n    originalMsgHash,\n  });\n\n  let sig: `0x${string}`;\n  if (is712Factory) {\n    const wrappedMessageHash = encodeAbiParameters(\n      [{ type: \"bytes32\" }],\n      [originalMsgHash],\n    );\n    sig = await options.personalAccount.signTypedData({\n      domain: {\n        name: \"Account\",\n        version: \"1\",\n        chainId: options.chain.id,\n        verifyingContract: accountContract.address,\n      },\n      primaryType: \"AccountMessage\",\n      types: { AccountMessage: [{ name: \"message\", type: \"bytes\" }] },\n      message: { message: wrappedMessageHash },\n    });\n  } else {\n    sig = await options.personalAccount.signTypedData(typedData);\n  }\n\n  const isDeployed = await isContractDeployed(accountContract);\n  if (isDeployed) {\n    const isValid = await verifyEip1271Signature({\n      hash: originalMsgHash,\n      signature: sig,\n      contract: accountContract,\n    });\n    if (isValid) {\n      return sig;\n    }\n    throw new Error(\"Failed to verify signature\");\n  } else {\n    const deployTx = prepareCreateAccount({\n      factoryContract,\n      adminAddress: options.personalAccount.address,\n      accountSalt: options.overrides?.accountSalt,\n      createAccountOverride: options.overrides?.createAccount,\n    });\n    if (!deployTx) {\n      throw new Error(\"Create account override not provided\");\n    }\n    const initCode = await encode(deployTx);\n    const erc6492Sig = serializeErc6492Signature({\n      address: factoryContract.address,\n      data: initCode,\n      signature: sig,\n    });\n\n    // check if the signature is valid\n    const isValid = await verifyHash({\n      hash: originalMsgHash,\n      signature: erc6492Sig,\n      address: accountContract.address,\n      chain: accountContract.chain,\n      client: accountContract.client,\n    });\n\n    if (isValid) {\n      return erc6492Sig;\n    }\n    throw new Error(\n      \"Unable to verify signature on smart account, please make sure the admin wallet has permissions and the signature is valid.\",\n    );\n  }\n}\n\nexport async function confirmContractDeployment(args: {\n  accountContract: ThirdwebContract;\n}) {\n  const { accountContract } = args;\n  const startTime = Date.now();\n  const timeout = 60000; // wait 1 minute max\n  const { isContractDeployed } = await import(\n    \"../../../utils/bytecode/is-contract-deployed.js\"\n  );\n  let isDeployed = await isContractDeployed(accountContract);\n  while (!isDeployed) {\n    if (Date.now() - startTime > timeout) {\n      throw new Error(\n        \"Timeout: Smart account deployment not confirmed after 1 minute\",\n      );\n    }\n    await new Promise((resolve) => setTimeout(resolve, 500));\n    isDeployed = await isContractDeployed(accountContract);\n  }\n}\n\nasync function checkFor712Factory({\n  factoryContract,\n  accountContract,\n  originalMsgHash,\n}: {\n  factoryContract: ThirdwebContract;\n  accountContract: ThirdwebContract;\n  originalMsgHash: Hex;\n}) {\n  try {\n    const implementationAccount = await readContract({\n      contract: factoryContract,\n      method: \"function accountImplementation() public view returns (address)\",\n    });\n    // check if the account contract supports EIP721 domain separator or modular based signing\n    const is712Factory = await readContract({\n      contract: getContract({\n        address: implementationAccount,\n        chain: accountContract.chain,\n        client: accountContract.client,\n      }),\n      method:\n        \"function getMessageHash(bytes32 _hash) public view returns (bytes32)\",\n      params: [originalMsgHash],\n    })\n      .then((res) => res !== \"0x\")\n      .catch(() => false);\n\n    return is712Factory;\n  } catch {\n    return false;\n  }\n}\n\n/**\n * Deployes a smart account via a dummy transaction. If the account is already deployed, this will do nothing.\n *\n * @param args - Arguments for the deployment.\n * @param args.smartAccount - The smart account to deploy.\n * @param args.chain - The chain to deploy on.\n * @param args.client - The client to use for the deployment.\n * @param args.accountContract - The account contract to deploy.\n *\n * @example\n * ```ts\n * import { deploySmartAccount } from \"thirdweb\";\n *\n * const account = await deploySmartAccount({\n *   smartAccount,\n *   chain,\n *   client,\n *   accountContract,\n * });\n * ```\n *\n * @wallet\n */\nexport async function deploySmartAccount(args: {\n  smartAccount: Account;\n  chain: Chain;\n  client: ThirdwebClient;\n  accountContract: ThirdwebContract;\n}) {\n  const { chain, client, smartAccount, accountContract } = args;\n  const isDeployed = await isContractDeployed(accountContract);\n  if (isDeployed) {\n    return;\n  }\n\n  const [{ sendTransaction }, { prepareTransaction }] = await Promise.all([\n    import(\"../../../transaction/actions/send-transaction.js\"),\n    import(\"../../../transaction/prepare-transaction.js\"),\n  ]);\n  const dummyTx = prepareTransaction({\n    client: client,\n    chain: chain,\n    to: accountContract.address,\n    value: 0n,\n    gas: 50000n, // force gas to avoid simulation error\n  });\n  const deployResult = await sendTransaction({\n    transaction: dummyTx,\n    account: smartAccount,\n  });\n\n  await confirmContractDeployment({\n    accountContract,\n  });\n\n  return deployResult;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAO,IAAM,uBACX;;;AC4BI,SAAU,0BAA0B,EACxC,SACA,MACA,UAAS,GACQ;AACjB,SAAO,UAAU;IACf,oBACE,CAAC,EAAE,MAAM,UAAS,GAAI,EAAE,MAAM,QAAO,GAAI,EAAE,MAAM,QAAO,CAAE,GAC1D,CAAC,SAAS,MAAM,SAAS,CAAC;IAE5B;GACD;AACH;;;ACxCA;;;;cAAAA;EAAA,eAAAC;EAAA;eAAAC;EAAA;;kBAAAC;;AAmBO,IAAM,aACX;AAKK,IAAM,sCACX;AAOK,IAAM,iCAAiC;EAC5C;IACE,QAAQ;MACN;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;;IAGV,iBAAiB;IACjB,MAAM;;EAER;IACE,QAAQ;MACN;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;MAER;QACE,MAAM;QACN,MAAM;;;IAGV,SAAS;MACP;QACE,MAAM;;;IAGV,iBAAiB;IACjB,MAAM;IACN,MAAM;;;AAiBJ,SAAU,OAAO,SAAgB;AACrC,MAAQ,MAAM,SAAS,GAAG,MAAM;AAC9B,UAAM,IAAI,6BAA6B,OAAO;AAClD;AAuCM,SAAUC,MAAK,SAAmC;AACtD,MAAI,OAAO,YAAY;AAAU,WAAOC,SAAQ,OAAO;AACvD,SAAO;AACT;AAyBM,SAAUA,SAAQ,SAAgB;AACtC,SAAO,OAAO;AAEd,QAAM,CAAC,IAAI,MAAM,aAAa,IAAkB,OAChCD,MAAK,uBAAuB,GAC1C,OAAO;AAGT,QAAM,YAAsBC,SAAQ,aAAa;AAEjD,SAAO,EAAE,MAAM,WAAW,GAAE;AAC9B;AAiCM,SAAUC,OAAM,OAAuB;AAC3C,QAAM,EAAE,MAAM,WAAW,GAAE,IAAK;AAEhC,SAAWC,QACKC,QAAqBJ,MAAK,uBAAuB,GAAG;IAChE;IACA;IACUE,OAAM,SAAS;GAC1B,GACD,UAAU;AAEd;AAwBM,SAAUG,UAAS,SAAgB;AACvC,MAAI;AACF,WAAO,OAAO;AACd,WAAO;EACT,QAAQ;AACN,WAAO;EACT;AACF;AAOM,IAAO,+BAAP,cAAmD,UAAS;EAGhE,YAAY,SAAgB;AAC1B,UAAM,WAAW,OAAO,8CAA8C;AAHtD,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAIzB;;;;ACpPK,IAAM,cAAc;AAC3B,IAAM,YAAY;EAChB;IACE,MAAM;IACN,MAAM;;EAER;IACE,MAAM;IACN,MAAM;;;AAGV,IAAM,aAAa;EACjB;IACE,MAAM;;;AA+FV,eAAsB,iBACpB,SAAuD;AAEvD,SAAO,aAAa;IAClB,UAAU,QAAQ;IAClB,QAAQ,CAAC,aAAa,WAAW,UAAU;IAC3C,QAAQ,CAAC,QAAQ,MAAM,QAAQ,SAAS;GACzC;AACH;;;AC5FM,SAAU,UAGd,OACA,UAAkC;AAElC,QAAM,OAAO,OAAO,aAAa,WAAW,EAAE,IAAI,SAAQ,IAAK;AAC/D,UAAQ,KAAK,IAAI;IACf,KAAK;AACH,aAAO,cAAc,OAAO,IAAI;IAClC,KAAK;AACH,aAAO,cAAc,OAAO,IAAI;IAClC,KAAK;AACH,aAAO,YAAY,OAAO,IAAI;IAChC,KAAK;AACH,aAAO,cAAc,OAAO,IAAI;IAClC;AACE,aAAO,gBAAgB,OAAO,IAAI;EACtC;AACF;AAuBM,SAAU,cACd,OACA,OAA0B,CAAA,GAAE;AAE5B,SAAiB,SAAS,OAAO,IAAI;AACvC;AAmBM,SAAU,YACd,QACA,OAAwB,CAAA,GAAE;AAE1B,SAAiB,UAAU,QAAQ,IAAI;AACzC;AAkBM,SAAU,cACd,OACA,OAA0B,CAAA,GAAE;AAE5B,SAAiB,SAAS,OAAO,IAAI;AACvC;AAkBM,SAAU,cACd,QACA,OAA0B,CAAA,GAAE;AAE5B,SAAiB,SAAS,QAAQ,IAAI;AACxC;;;AC/HA,IAAM,2BACJ;AA8BF,eAAsB,WAAW,EAC/B,MACA,WACA,SACA,QACA,OACA,eAAc,GACG;AACjB,QAAM,gBAAgB,MAAK;AACzB,QAAI,MAAM,SAAS;AAAG,aAAO;AAC7B,QAAI,OAAO,cAAc,YAAY,OAAO,aAAa,OAAO;AAC9D,aAAqBC,OAAM,SAAS;AACtC,QAAI,qBAAqB;AAAY,aAAO,UAAU,WAAW,KAAK;AAEtE,UAAM,IAAI,MACR,wCAAwC,SAAS,KAAK,OAAO,SAAS,EAAE;EAE5E,GAAE;AAEF,QAAM,aAAa,MAAM,mBACvB,YAAY;IACV;IACA;IACA;GACD,CAAC;AAGJ,MAAI,YAAY;AACd,UAAM,eAAe,MAAM,uBAAuB;MAChD;MACA,WAAW;MACX,UAAU,YAAY;QACpB;QACA;QACA;OACD;KACF,EAAE,MAAM,CAAC,QAAO;AACf,cAAQ,MAAM,sCAAsC,GAAG;AACvD,aAAO;IACT,CAAC;AACD,QAAI,cAAc;AAChB,aAAO;IACT;EACF;AAGA,QAAM,mBAAwB,OAAO,YAAW;AAG9C,QAAI,CAAC;AAAgB,aAAO;AAG5B,QAAI,yBAAqB,SAAS,YAAY;AAAG,aAAO;AAGxD,WAAO,0BAA0B;MAC/B,SAAS,eAAe;MACxB,MAAM,eAAe;MACrB,WAAW;KACZ;EACH,GAAE;AAEF,MAAI;AAKJ,QAAM,cAAc,MAAM,cAAc,KAAK;AAC7C,QAAM,MAAc,KAAK,yBAAqB,8BAA8B;AAC5E,MAAI,aAAa;AAGf,uBAAmB;MACjB,IAAI;MACJ,MAAsB,WACJC,SAAQ,KAAK,YAAY,GACzC,CAAC,SAAS,MAAM,gBAAgB,CAAC;;EAGvC,OAAO;AACL,UAAM,uBAA0C,QAAQ,GAAG;AAC3D,uBAAmB;MACjB,MAAyBC,QAAO,sBAAsB;QACpD,MAAM,CAAC,SAAS,MAAM,gBAAgB;QACtC,UAAU,yBAAqB;OAChC;;EAEL;AAEA,QAAM,aAAa,aAAa;IAC9B;IACA;GACD;AAED,MAAI;AACF,UAAM,SAAS,MAAM,SAAS,YAAY,gBAAgB;AAC1D,WAAO,UAAU,MAAM;EACzB,QAAQ;AAEN,UAAM,eAAe,MAAM,uBAAuB;MAChD;MACA,WAAW;MACX,UAAU,YAAY;QACpB;QACA;QACA;OACD;KACF,EAAE,MAAM,CAAC,QAAO;AACf,cAAQ,MAAM,sCAAsC,GAAG;AACvD,aAAO;IACT,CAAC;AACD,QAAI,cAAc;AAChB,aAAO;IACT;AAGA,WAAO;EACT;AACF;AAEA,IAAM,uBAAuB;AAC7B,eAAsB,uBAAuB,EAC3C,MACA,WACA,SAAQ,GAKT;AACC,MAAI;AACF,UAAM,SAAS,MAAM,iBAAiB;MACpC;MACA;MACA;KACD;AACD,WAAO,WAAW;EACpB,SAAS,KAAK;AACZ,YAAQ,MAAM,sCAAsC,GAAG;AACvD,WAAO;EACT;AACF;;;ACpMA,IAAM,uBAAuB;AAmBvB,SAAU,YACd,SACA,KAAS;AAET,QAAM,gBAAgB,MAAK;AACzB,QAAI,OAAO,YAAY,UAAU;AAC/B,aAAO,cAAc,OAAO;IAC9B;AACA,QAAI,QAAQ,eAAe,YAAY;AACrC,aAAO,QAAQ;IACjB;AACA,WAAO,QAAQ,QAAQ,GAAG;EAC5B,GAAE;AACF,QAAM,cAAc,cAClB,GAAG,oBAAoB,GAAG,aAAa,MAAM,EAAE;AAEjD,SAAO,UAAoB,OAAO,aAAa,YAAY,GAAG,GAAG;AACnE;;;ACpBM,SAAU,cAGd,YAAuD;AACvD,QAAM,EACJ,SAAS,CAAA,GACT,SACA,YAAW,IACT;AACJ,QAAM,QAAQ;IACZ,cAA4B,yBAAyB,MAAM;IAC3D,GAAG,WAAW;;AAKhB,EAAc,SAAS;IACrB;IACA;IACA;IACA;GACD;AAED,QAAM,QAAe,CAAC,QAAQ;AAC9B,MAAI;AACF,UAAM,KACU,WAAW;MACvB;MACA;KACD,CAAC;AAGN,MAAI,gBAAgB,gBAAgB;AAClC,UAAM,gBAAgB,MAAK;AACzB,YAAM,UAAUC,YAAW;QACzB,MAAM;QACN;QACA;OACD;AACD,aAAO,UAAU,OAAO;IAC1B,GAAE;AAEF,UAAM,KAAK,YAAY;EACzB;AAEA,SAAO,UAAoB,OAAO,GAAG,MAAM,IAAI,CAAC,MAAgB,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC9E;AAEA,SAASA,YAAW,EAClB,MACA,aACA,MAAK,GAKN;AACC,QAAM,eAA8C,CAAC,EAAE,MAAM,UAAS,CAAE;AACxE,QAAM,gBAA2B,CAAC,SAAS,EAAE,aAAa,MAAK,CAAE,CAAC;AAElE,MAAI,CAAC,MAAM,WAAW;AAAG,UAAM,IAAI,MAAM,eAAe;AACxD,aAAW,SAAS,MAAM,WAAW,GAAG;AACtC,UAAM,CAAC,MAAM,KAAK,IAAI,YAAY;MAChC;MACA,MAAM,MAAM;MACZ,MAAM,MAAM;MACZ,OAAO,KAAK,MAAM,IAAI;KACvB;AACD,iBAAa,KAAK,IAAI;AACtB,kBAAc,KAAK,KAAK;EAC1B;AAEA,SAAO,oBAAoB,cAAc,aAAa;AACxD;AAEA,SAAS,SAAS,EAChB,aACA,MAAK,GAIN;AACC,QAAM,kBAAkB,MAAM,WAAW,EAAE,aAAa,MAAK,CAAE,CAAC;AAChE,SAAO,UAAU,eAAe;AAClC;AAEA,SAAS,WAAW,EAClB,aACA,MAAK,GAIN;AACC,MAAI,SAAS;AACb,QAAM,eAAe,qBAAqB,EAAE,aAAa,MAAK,CAAE;AAChE,eAAa,OAAO,WAAW;AAE/B,QAAM,OAAO,CAAC,aAAa,GAAG,MAAM,KAAK,YAAY,EAAE,KAAI,CAAE;AAC7D,aAAW,QAAQ,MAAM;AACvB,QAAI,CAAC,MAAM,IAAI;AAAG,YAAM,IAAI,MAAM,eAAe;AACjD,cAAU,GAAG,IAAI,IAAI,MAAM,IAAI,EAC5B,IAAI,CAAC,EAAE,MAAM,MAAM,EAAC,MAAO,GAAG,CAAC,IAAI,IAAI,EAAE,EACzC,KAAK,GAAG,CAAC;EACd;AAEA,SAAO;AACT;AAEA,SAAS,qBACP,EACE,aAAa,cACb,MAAK,GAKP,UAAuB,oBAAI,IAAG,GAAE;AAEhC,QAAM,QAAQ,aAAa,MAAM,OAAO;AACxC,QAAM,cAAc,+BAAQ;AAC5B,MAAI,QAAQ,IAAI,WAAW,KAAK,MAAM,WAAW,MAAM,QAAW;AAChE,WAAO;EACT;AAEA,UAAQ,IAAI,WAAW;AAEvB,aAAW,SAAS,MAAM,WAAW,GAAG;AACtC,yBAAqB,EAAE,aAAa,MAAM,MAAM,MAAK,GAAI,OAAO;EAClE;AACA,SAAO;AACT;AAEA,SAAS,YAAY,EACnB,OACA,MACA,MACA,MAAK,GAQN;AACC,MAAI,MAAM,IAAI,MAAM,QAAW;AAC7B,WAAO;MACL,EAAE,MAAM,UAAS;MACjB,UAAUA,YAAW,EAAE,MAAM,OAAO,aAAa,MAAM,MAAK,CAAE,CAAC;;EAEnE;AAEA,MAAI,SAAS,SAAS;AACpB,UAAM,UAAU,MAAM,SAAS,IAAI,MAAM;AACzC,YAAQ,KAAK,UAAU,MAAM,MAAM,CAAC,CAAC;AACrC,WAAO,CAAC,EAAE,MAAM,UAAS,GAAI,UAAU,KAAK,CAAC;EAC/C;AAEA,MAAI,SAAS;AAAU,WAAO,CAAC,EAAE,MAAM,UAAS,GAAI,UAAU,MAAM,KAAK,CAAC,CAAC;AAE3E,MAAI,KAAK,YAAY,GAAG,MAAM,KAAK,SAAS,GAAG;AAC7C,UAAM,aAAa,KAAK,MAAM,GAAG,KAAK,YAAY,GAAG,CAAC;AACtD,UAAM;;MAEH,MAA+C,IAAI,CAAC,SACnD,YAAY;QACV;QACA,MAAM;QACN;QACA,OAAO;OACR,CAAC;;AAEN,WAAO;MACL,EAAE,MAAM,UAAS;MACjB,UACE,oBACE,eAAe,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,GAC7B,eAAe,IAAI,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CACjC;;EAGP;AAEA,SAAO,CAAC,EAAE,KAAI,GAAI,KAAK;AACzB;;;AChLA,eAAsB,wBAAwB,EAC5C,iBACA,iBACA,SACA,QAAO,GAMR;AAvCD;AAwCE,QAAM,kBAAkB,YAAY,OAAO;AAC3C,QAAM,eAAe,MAAM,mBAAmB;IAC5C;IACA;IACA;GACD;AAED,MAAI;AACJ,MAAI,cAAc;AAChB,UAAM,qBAAqB,oBACzB,CAAC,EAAE,MAAM,UAAS,CAAE,GACpB,CAAC,eAAe,CAAC;AAGnB,UAAM,MAAM,QAAQ,gBAAgB,cAAc;MAChD,QAAQ;QACN,MAAM;QACN,SAAS;QACT,SAAS,QAAQ,MAAM;QACvB,mBAAmB,gBAAgB;;MAErC,aAAa;MACb,OAAO,EAAE,gBAAgB,CAAC,EAAE,MAAM,WAAW,MAAM,QAAO,CAAE,EAAC;MAC7D,SAAS,EAAE,SAAS,mBAAkB;KACvC;EACH,OAAO;AACL,UAAM,MAAM,QAAQ,gBAAgB,YAAY,EAAE,QAAO,CAAE;EAC7D;AAEA,QAAM,aAAa,MAAM,mBAAmB,eAAe;AAC3D,MAAI,YAAY;AACd,UAAM,UAAU,MAAM,uBAAuB;MAC3C,MAAM;MACN,WAAW;MACX,UAAU;KACX;AACD,QAAI,SAAS;AACX,aAAO;IACT;AACA,UAAM,IAAI,MAAM,4BAA4B;EAC9C,OAAO;AACL,UAAM,WAAW,qBAAqB;MACpC;MACA,cAAc,QAAQ,gBAAgB;MACtC,cAAa,aAAQ,cAAR,mBAAmB;MAChC,wBAAuB,aAAQ,cAAR,mBAAmB;KAC3C;AACD,QAAI,CAAC,UAAU;AACb,YAAM,IAAI,MAAM,sCAAsC;IACxD;AACA,UAAM,WAAW,MAAM,OAAO,QAAQ;AACtC,UAAM,aAAa,0BAA0B;MAC3C,SAAS,gBAAgB;MACzB,MAAM;MACN,WAAW;KACZ;AAGD,UAAM,UAAU,MAAM,WAAW;MAC/B,MAAM;MACN,WAAW;MACX,SAAS,gBAAgB;MACzB,OAAO,gBAAgB;MACvB,QAAQ,gBAAgB;KACzB;AAED,QAAI,SAAS;AACX,aAAO;IACT;AACA,UAAM,IAAI,MAAM,oDAAoD;EACtE;AACF;AAEA,eAAsB,0BAGpB,EACA,iBACA,iBACA,SACA,UAAS,GAMV;AA9HD;AA+HE,QAAM,4BAEF,qBAAU,WAAV,mBACC,sBADD,mBACoB,qBACtB,qBAAgB,YAAhB,mBAAyB;AAE3B,MAAI,yBAAyB;AAE3B,WAAO,QAAQ,gBAAgB,cAAc,SAAS;EACxD;AAEA,QAAM,kBAAkB,cAAc,SAAS;AAE/C,QAAM,eAAe,MAAM,mBAAmB;IAC5C;IACA;IACA;GACD;AAED,MAAI;AACJ,MAAI,cAAc;AAChB,UAAM,qBAAqB,oBACzB,CAAC,EAAE,MAAM,UAAS,CAAE,GACpB,CAAC,eAAe,CAAC;AAEnB,UAAM,MAAM,QAAQ,gBAAgB,cAAc;MAChD,QAAQ;QACN,MAAM;QACN,SAAS;QACT,SAAS,QAAQ,MAAM;QACvB,mBAAmB,gBAAgB;;MAErC,aAAa;MACb,OAAO,EAAE,gBAAgB,CAAC,EAAE,MAAM,WAAW,MAAM,QAAO,CAAE,EAAC;MAC7D,SAAS,EAAE,SAAS,mBAAkB;KACvC;EACH,OAAO;AACL,UAAM,MAAM,QAAQ,gBAAgB,cAAc,SAAS;EAC7D;AAEA,QAAM,aAAa,MAAM,mBAAmB,eAAe;AAC3D,MAAI,YAAY;AACd,UAAM,UAAU,MAAM,uBAAuB;MAC3C,MAAM;MACN,WAAW;MACX,UAAU;KACX;AACD,QAAI,SAAS;AACX,aAAO;IACT;AACA,UAAM,IAAI,MAAM,4BAA4B;EAC9C,OAAO;AACL,UAAM,WAAW,qBAAqB;MACpC;MACA,cAAc,QAAQ,gBAAgB;MACtC,cAAa,aAAQ,cAAR,mBAAmB;MAChC,wBAAuB,aAAQ,cAAR,mBAAmB;KAC3C;AACD,QAAI,CAAC,UAAU;AACb,YAAM,IAAI,MAAM,sCAAsC;IACxD;AACA,UAAM,WAAW,MAAM,OAAO,QAAQ;AACtC,UAAM,aAAa,0BAA0B;MAC3C,SAAS,gBAAgB;MACzB,MAAM;MACN,WAAW;KACZ;AAGD,UAAM,UAAU,MAAM,WAAW;MAC/B,MAAM;MACN,WAAW;MACX,SAAS,gBAAgB;MACzB,OAAO,gBAAgB;MACvB,QAAQ,gBAAgB;KACzB;AAED,QAAI,SAAS;AACX,aAAO;IACT;AACA,UAAM,IAAI,MACR,4HAA4H;EAEhI;AACF;AAEA,eAAsB,0BAA0B,MAE/C;AACC,QAAM,EAAE,gBAAe,IAAK;AAC5B,QAAM,YAAY,KAAK,IAAG;AAC1B,QAAM,UAAU;AAChB,QAAM,EAAE,oBAAAC,oBAAkB,IAAK,MAAM,OACnC,oCAAiD;AAEnD,MAAI,aAAa,MAAMA,oBAAmB,eAAe;AACzD,SAAO,CAAC,YAAY;AAClB,QAAI,KAAK,IAAG,IAAK,YAAY,SAAS;AACpC,YAAM,IAAI,MACR,gEAAgE;IAEpE;AACA,UAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,GAAG,CAAC;AACvD,iBAAa,MAAMA,oBAAmB,eAAe;EACvD;AACF;AAEA,eAAe,mBAAmB,EAChC,iBACA,iBACA,gBAAe,GAKhB;AACC,MAAI;AACF,UAAM,wBAAwB,MAAM,aAAa;MAC/C,UAAU;MACV,QAAQ;KACT;AAED,UAAM,eAAe,MAAM,aAAa;MACtC,UAAU,YAAY;QACpB,SAAS;QACT,OAAO,gBAAgB;QACvB,QAAQ,gBAAgB;OACzB;MACD,QACE;MACF,QAAQ,CAAC,eAAe;KACzB,EACE,KAAK,CAAC,QAAQ,QAAQ,IAAI,EAC1B,MAAM,MAAM,KAAK;AAEpB,WAAO;EACT,QAAQ;AACN,WAAO;EACT;AACF;AAyBA,eAAsB,mBAAmB,MAKxC;AACC,QAAM,EAAE,OAAO,QAAQ,cAAc,gBAAe,IAAK;AACzD,QAAM,aAAa,MAAM,mBAAmB,eAAe;AAC3D,MAAI,YAAY;AACd;EACF;AAEA,QAAM,CAAC,EAAE,gBAAe,GAAI,EAAE,mBAAkB,CAAE,IAAI,MAAM,QAAQ,IAAI;IACtE,OAAO,gCAAkD;IACzD,OAAO,mCAA6C;GACrD;AACD,QAAM,UAAU,mBAAmB;IACjC;IACA;IACA,IAAI,gBAAgB;IACpB,OAAO;IACP,KAAK;;GACN;AACD,QAAM,eAAe,MAAM,gBAAgB;IACzC,aAAa;IACb,SAAS;GACV;AAED,QAAM,0BAA0B;IAC9B;GACD;AAED,SAAO;AACT;", "names": ["from", "fromHex", "toHex", "validate", "from", "fromHex", "toHex", "concat", "encode", "validate", "toHex", "fromAbi", "encode", "encodeData", "isContractDeployed"]}