{"version": 3, "sources": ["../../ox/core/TypedData.ts", "../../ox/core/AbiParameters.ts", "../../ox/core/internal/abiParameters.ts"], "sourcesContent": ["import type * as abitype from 'abitype'\nimport * as AbiParameters from './AbiParameters.js'\nimport * as Address from './Address.js'\nimport * as Bytes from './Bytes.js'\nimport * as Errors from './Errors.js'\nimport * as Hash from './Hash.js'\nimport * as Hex from './Hex.js'\nimport * as <PERSON><PERSON> from './Json.js'\nimport * as Solidity from './Solidity.js'\nimport type { Compute } from './internal/types.js'\n\nexport type TypedData = abitype.TypedData\nexport type Domain = abitype.TypedDataDomain\nexport type Parameter = abitype.TypedDataParameter\n\n// TODO: Make reusable for Viem?\nexport type Definition<\n  typedData extends TypedData | Record<string, unknown> = TypedData,\n  primaryType extends keyof typedData | 'EIP712Domain' = keyof typedData,\n  ///\n  primaryTypes = typedData extends TypedData ? keyof typedData : string,\n> = primaryType extends 'EIP712Domain'\n  ? EIP712DomainDefinition<typedData, primaryType>\n  : MessageDefinition<typedData, primaryType, primaryTypes>\n\nexport type EIP712DomainDefinition<\n  typedData extends TypedData | Record<string, unknown> = TypedData,\n  primaryType extends 'EIP712Domain' = 'EIP712Domain',\n  ///\n  schema extends Record<string, unknown> = typedData extends TypedData\n    ? abitype.TypedDataToPrimitiveTypes<typedData>\n    : Record<string, unknown>,\n> = {\n  types?: typedData | undefined\n} & {\n  primaryType:\n    | 'EIP712Domain'\n    | (primaryType extends 'EIP712Domain' ? primaryType : never)\n  domain: schema extends { EIP712Domain: infer domain }\n    ? domain\n    : Compute<Domain>\n  message?: undefined\n}\n\nexport type MessageDefinition<\n  typedData extends TypedData | Record<string, unknown> = TypedData,\n  primaryType extends keyof typedData = keyof typedData,\n  ///\n  primaryTypes = typedData extends TypedData ? keyof typedData : string,\n  schema extends Record<string, unknown> = typedData extends TypedData\n    ? abitype.TypedDataToPrimitiveTypes<typedData>\n    : Record<string, unknown>,\n  message = schema[primaryType extends keyof schema\n    ? primaryType\n    : keyof schema],\n> = {\n  types: typedData\n} & {\n  primaryType:\n    | primaryTypes // show all values\n    | (primaryType extends primaryTypes ? primaryType : never) // infer value\n  domain?:\n    | (schema extends { EIP712Domain: infer domain } ? domain : Compute<Domain>)\n    | undefined\n  message: { [_: string]: any } extends message // Check if message was inferred\n    ? Record<string, unknown>\n    : message\n}\n\n/**\n * Asserts that [EIP-712 Typed Data](https://eips.ethereum.org/EIPS/eip-712) is valid.\n *\n * @example\n * ```ts twoslash\n * import { TypedData } from 'ox'\n *\n * TypedData.assert({\n *   domain: {\n *     name: 'Ether!',\n *     version: '1',\n *     chainId: 1,\n *     verifyingContract: '******************************************',\n *   },\n *   primaryType: 'Foo',\n *   types: {\n *     Foo: [\n *       { name: 'address', type: 'address' },\n *       { name: 'name', type: 'string' },\n *       { name: 'foo', type: 'string' },\n *     ],\n *   },\n *   message: {\n *     address: '******************************************',\n *     name: 'jxom',\n *     foo: '******************************************',\n *   },\n * })\n * ```\n *\n * @param value - The Typed Data to validate.\n */\nexport function assert<\n  const typedData extends TypedData | Record<string, unknown>,\n  primaryType extends keyof typedData | 'EIP712Domain',\n>(value: assert.Value<typedData, primaryType>): void {\n  const { domain, message, primaryType, types } =\n    value as unknown as assert.Value\n\n  const validateData = (\n    struct: readonly Parameter[],\n    data: Record<string, unknown>,\n  ) => {\n    for (const param of struct) {\n      const { name, type } = param\n      const value = data[name]\n\n      const integerMatch = type.match(Solidity.integerRegex)\n      if (\n        integerMatch &&\n        (typeof value === 'number' || typeof value === 'bigint')\n      ) {\n        const [, base, size_] = integerMatch\n        // If number cannot be cast to a sized hex value, it is out of range\n        // and will throw.\n        Hex.fromNumber(value, {\n          signed: base === 'int',\n          size: Number.parseInt(size_ ?? '') / 8,\n        })\n      }\n\n      if (\n        type === 'address' &&\n        typeof value === 'string' &&\n        !Address.validate(value)\n      )\n        throw new Address.InvalidAddressError({\n          address: value,\n          cause: new Address.InvalidInputError(),\n        })\n\n      const bytesMatch = type.match(Solidity.bytesRegex)\n      if (bytesMatch) {\n        const [, size] = bytesMatch\n        if (size && Hex.size(value as Hex.Hex) !== Number.parseInt(size))\n          throw new BytesSizeMismatchError({\n            expectedSize: Number.parseInt(size),\n            givenSize: Hex.size(value as Hex.Hex),\n          })\n      }\n\n      const struct = types[type]\n      if (struct) {\n        validateReference(type)\n        validateData(struct, value as Record<string, unknown>)\n      }\n    }\n  }\n\n  // Validate domain types.\n  if (types.EIP712Domain && domain) {\n    if (typeof domain !== 'object') throw new InvalidDomainError({ domain })\n    validateData(types.EIP712Domain, domain)\n  }\n\n  // Validate message types.\n  if (primaryType !== 'EIP712Domain') {\n    if (types[primaryType]) validateData(types[primaryType], message)\n    else throw new InvalidPrimaryTypeError({ primaryType, types })\n  }\n}\n\nexport declare namespace assert {\n  type Value<\n    typedData extends TypedData | Record<string, unknown> = TypedData,\n    primaryType extends keyof typedData | 'EIP712Domain' = keyof typedData,\n  > = Definition<typedData, primaryType>\n\n  type ErrorType =\n    | Address.InvalidAddressError\n    | BytesSizeMismatchError\n    | InvalidPrimaryTypeError\n    | Hex.fromNumber.ErrorType\n    | Hex.size.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Creates [EIP-712 Typed Data](https://eips.ethereum.org/EIPS/eip-712) [`domainSeparator`](https://eips.ethereum.org/EIPS/eip-712#definition-of-domainseparator) for the provided domain.\n *\n * @example\n * ```ts twoslash\n * import { TypedData } from 'ox'\n *\n * TypedData.domainSeparator({\n *   name: 'Ether!',\n *   version: '1',\n *   chainId: 1,\n *   verifyingContract: '******************************************',\n * })\n * // @log: '0x9911ee4f58a7059a8f5385248040e6984d80e2c849500fe6a4d11c4fa98c2af3'\n * ```\n *\n * @param domain - The domain for which to create the domain separator.\n * @returns The domain separator.\n */\nexport function domainSeparator(domain: Domain): Hex.Hex {\n  return hashDomain({\n    domain,\n  })\n}\n\nexport declare namespace domainSeparator {\n  type ErrorType = hashDomain.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Encodes typed data in [EIP-712 format](https://eips.ethereum.org/EIPS/eip-712): `0x19 ‖ 0x01 ‖ domainSeparator ‖ hashStruct(message)`.\n *\n * @example\n * ```ts twoslash\n * import { TypedData, Hash } from 'ox'\n *\n * const data = TypedData.encode({ // [!code focus:33]\n *   domain: {\n *     name: 'Ether Mail',\n *     version: '1',\n *     chainId: 1,\n *     verifyingContract: '******************************************',\n *   },\n *   types: {\n *     Person: [\n *       { name: 'name', type: 'string' },\n *       { name: 'wallet', type: 'address' },\n *     ],\n *     Mail: [\n *       { name: 'from', type: 'Person' },\n *       { name: 'to', type: 'Person' },\n *       { name: 'contents', type: 'string' },\n *     ],\n *   },\n *   primaryType: 'Mail',\n *   message: {\n *     from: {\n *       name: 'Cow',\n *       wallet: '******************************************',\n *     },\n *     to: {\n *       name: 'Bob',\n *       wallet: '******************************************',\n *     },\n *     contents: 'Hello, Bob!',\n *   },\n * })\n * // @log: '0x19012fdf3441fcaf4f30c7e16292b258a5d7054a4e2e00dbd7b7d2f467f2b8fb9413c52c0ee5d84264471806290a3f2c4cecfc5490626bf912d01f240d7a274b371e'\n * // @log: (0x19 ‖ 0x01 ‖ domainSeparator ‖ hashStruct(message))\n *\n * const hash = Hash.keccak256(data)\n * ```\n *\n * @param value - The Typed Data to encode.\n * @returns The encoded Typed Data.\n */\nexport function encode<\n  const typedData extends TypedData | Record<string, unknown>,\n  primaryType extends keyof typedData | 'EIP712Domain',\n>(value: encode.Value<typedData, primaryType>): Hex.Hex {\n  const { domain = {}, message, primaryType } = value as encode.Value\n\n  const types = {\n    EIP712Domain: extractEip712DomainTypes(domain),\n    ...value.types,\n  } as TypedData\n\n  // Need to do a runtime validation check on addresses, byte ranges, integer ranges, etc\n  // as we can't statically check this with TypeScript.\n  assert({\n    domain,\n    message,\n    primaryType,\n    types,\n  })\n\n  // Typed Data Format: `0x19 ‖ 0x01 ‖ domainSeparator ‖ hashStruct(message)`\n  const parts: Hex.Hex[] = ['0x19', '0x01']\n  if (domain)\n    parts.push(\n      hashDomain({\n        domain,\n        types,\n      }),\n    )\n  if (primaryType !== 'EIP712Domain')\n    parts.push(\n      hashStruct({\n        data: message,\n        primaryType,\n        types,\n      }),\n    )\n\n  return Hex.concat(...parts)\n}\n\nexport declare namespace encode {\n  type Value<\n    typedData extends TypedData | Record<string, unknown> = TypedData,\n    primaryType extends keyof typedData | 'EIP712Domain' = keyof typedData,\n  > = Definition<typedData, primaryType>\n\n  type ErrorType =\n    | extractEip712DomainTypes.ErrorType\n    | hashDomain.ErrorType\n    | hashStruct.ErrorType\n    | assert.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Encodes [EIP-712 Typed Data](https://eips.ethereum.org/EIPS/eip-712) schema for the provided primaryType.\n *\n * @example\n * ```ts twoslash\n * import { TypedData } from 'ox'\n *\n * TypedData.encodeType({\n *   types: {\n *     Foo: [\n *       { name: 'address', type: 'address' },\n *       { name: 'name', type: 'string' },\n *       { name: 'foo', type: 'string' },\n *     ],\n *   },\n *   primaryType: 'Foo',\n * })\n * // @log: 'Foo(address address,string name,string foo)'\n * ```\n *\n * @param value - The Typed Data schema.\n * @returns The encoded type.\n */\nexport function encodeType(value: encodeType.Value): string {\n  const { primaryType, types } = value\n\n  let result = ''\n  const unsortedDeps = findTypeDependencies({ primaryType, types })\n  unsortedDeps.delete(primaryType)\n\n  const deps = [primaryType, ...Array.from(unsortedDeps).sort()]\n  for (const type of deps) {\n    result += `${type}(${(types[type] ?? [])\n      .map(({ name, type: t }) => `${t} ${name}`)\n      .join(',')})`\n  }\n\n  return result\n}\n\nexport declare namespace encodeType {\n  type Value = {\n    primaryType: string\n    types: TypedData\n  }\n\n  type ErrorType = findTypeDependencies.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Gets [EIP-712 Typed Data](https://eips.ethereum.org/EIPS/eip-712) schema for EIP-721 domain.\n *\n * @example\n * ```ts twoslash\n * import { TypedData } from 'ox'\n *\n * TypedData.extractEip712DomainTypes({\n *   name: 'Ether!',\n *   version: '1',\n *   chainId: 1,\n *   verifyingContract: '******************************************',\n * })\n * // @log: [\n * // @log:   { 'name': 'name', 'type': 'string' },\n * // @log:   { 'name': 'version', 'type': 'string' },\n * // @log:   { 'name': 'chainId', 'type': 'uint256' },\n * // @log:   { 'name': 'verifyingContract', 'type': 'address' },\n * // @log: ]\n * ```\n *\n * @param domain - The EIP-712 domain.\n * @returns The EIP-712 domain schema.\n */\nexport function extractEip712DomainTypes(\n  domain: Domain | undefined,\n): Parameter[] {\n  return [\n    typeof domain?.name === 'string' && { name: 'name', type: 'string' },\n    domain?.version && { name: 'version', type: 'string' },\n    typeof domain?.chainId === 'number' && {\n      name: 'chainId',\n      type: 'uint256',\n    },\n    domain?.verifyingContract && {\n      name: 'verifyingContract',\n      type: 'address',\n    },\n    domain?.salt && { name: 'salt', type: 'bytes32' },\n  ].filter(Boolean) as Parameter[]\n}\n\nexport declare namespace extractEip712DomainTypes {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Gets the payload to use for signing typed data in [EIP-712 format](https://eips.ethereum.org/EIPS/eip-712).\n *\n * @example\n * ```ts twoslash\n * import { Secp256k1, TypedData, Hash } from 'ox'\n *\n * const payload = TypedData.getSignPayload({ // [!code focus:99]\n *   domain: {\n *     name: 'Ether Mail',\n *     version: '1',\n *     chainId: 1,\n *     verifyingContract: '******************************************',\n *   },\n *   types: {\n *     Person: [\n *       { name: 'name', type: 'string' },\n *       { name: 'wallet', type: 'address' },\n *     ],\n *     Mail: [\n *       { name: 'from', type: 'Person' },\n *       { name: 'to', type: 'Person' },\n *       { name: 'contents', type: 'string' },\n *     ],\n *   },\n *   primaryType: 'Mail',\n *   message: {\n *     from: {\n *       name: 'Cow',\n *       wallet: '******************************************',\n *     },\n *     to: {\n *       name: 'Bob',\n *       wallet: '******************************************',\n *     },\n *     contents: 'Hello, Bob!',\n *   },\n * })\n *\n * const signature = Secp256k1.sign({ payload, privateKey: '0x...' })\n * ```\n *\n * @param value - The typed data to get the sign payload for.\n * @returns The payload to use for signing.\n */\nexport function getSignPayload<\n  const typedData extends TypedData | Record<string, unknown>,\n  primaryType extends keyof typedData | 'EIP712Domain',\n>(value: encode.Value<typedData, primaryType>): Hex.Hex {\n  return Hash.keccak256(encode(value))\n}\n\nexport declare namespace getSignPayload {\n  type ErrorType =\n    | Hash.keccak256.ErrorType\n    | encode.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Hashes [EIP-712 Typed Data](https://eips.ethereum.org/EIPS/eip-712) domain.\n *\n * @example\n * ```ts twoslash\n * import { TypedData } from 'ox'\n *\n * TypedData.hashDomain({\n *   domain: {\n *     name: 'Ether Mail',\n *     version: '1',\n *     chainId: 1,\n *     verifyingContract: '******************************************',\n *   },\n * })\n * // @log: '0x6192106f129ce05c9075d319c1fa6ea9b3ae37cbd0c1ef92e2be7137bb07baa1'\n * ```\n *\n * @param value - The Typed Data domain and types.\n * @returns The hashed domain.\n */\nexport function hashDomain(value: hashDomain.Value): Hex.Hex {\n  const { domain, types } = value\n  return hashStruct({\n    data: domain,\n    primaryType: 'EIP712Domain',\n    types: {\n      ...types,\n      EIP712Domain: types?.EIP712Domain || extractEip712DomainTypes(domain),\n    },\n  })\n}\n\nexport declare namespace hashDomain {\n  type Value = {\n    /** The Typed Data domain. */\n    domain: Domain\n    /** The Typed Data types. */\n    types?:\n      | {\n          EIP712Domain?: readonly Parameter[] | undefined\n          [key: string]: readonly Parameter[] | undefined\n        }\n      | undefined\n  }\n\n  type ErrorType = hashStruct.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Hashes [EIP-712 Typed Data](https://eips.ethereum.org/EIPS/eip-712) struct.\n *\n * @example\n * ```ts twoslash\n * import { TypedData } from 'ox'\n *\n * TypedData.hashStruct({\n *   types: {\n *     Foo: [\n *       { name: 'address', type: 'address' },\n *       { name: 'name', type: 'string' },\n *       { name: 'foo', type: 'string' },\n *     ],\n *   },\n *   primaryType: 'Foo',\n *   data: {\n *     address: '******************************************',\n *     name: 'jxom',\n *     foo: '******************************************',\n *   },\n * })\n * // @log: '0x996fb3b6d48c50312d69abdd4c1b6fb02057c85aa86bb8d04c6f023326a168ce'\n * ```\n *\n * @param value - The Typed Data struct to hash.\n * @returns The hashed Typed Data struct.\n */\nexport function hashStruct(value: hashStruct.Value): Hex.Hex {\n  const { data, primaryType, types } = value\n  const encoded = encodeData({\n    data,\n    primaryType,\n    types,\n  })\n  return Hash.keccak256(encoded)\n}\n\nexport declare namespace hashStruct {\n  type Value = {\n    /** The Typed Data struct to hash. */\n    data: Record<string, unknown>\n    /** The primary type of the Typed Data struct. */\n    primaryType: string\n    /** The types of the Typed Data struct. */\n    types: TypedData\n  }\n\n  type ErrorType =\n    | encodeData.ErrorType\n    | Hash.keccak256.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Serializes [EIP-712 Typed Data](https://eips.ethereum.org/EIPS/eip-712) schema into string.\n *\n * @example\n * ```ts twoslash\n * import { TypedData } from 'ox'\n *\n * TypedData.serialize({\n *   domain: {\n *     name: 'Ether!',\n *     version: '1',\n *     chainId: 1,\n *     verifyingContract: '******************************************',\n *   },\n *   primaryType: 'Foo',\n *   types: {\n *     Foo: [\n *       { name: 'address', type: 'address' },\n *       { name: 'name', type: 'string' },\n *       { name: 'foo', type: 'string' },\n *     ],\n *   },\n *   message: {\n *     address: '******************************************',\n *     name: 'jxom',\n *     foo: '******************************************',\n *   },\n * })\n * // @log: \"{\"domain\":{},\"message\":{\"address\":\"******************************************\",\"name\":\"jxom\",\"foo\":\"******************************************\"},\"primaryType\":\"Foo\",\"types\":{\"Foo\":[{\"name\":\"address\",\"type\":\"address\"},{\"name\":\"name\",\"type\":\"string\"},{\"name\":\"foo\",\"type\":\"string\"}]}}\"\n * ```\n *\n * @param value - The Typed Data schema to serialize.\n * @returns The serialized Typed Data schema. w\n */\nexport function serialize<\n  const typedData extends TypedData | Record<string, unknown>,\n  primaryType extends keyof typedData | 'EIP712Domain',\n>(value: serialize.Value<typedData, primaryType>): string {\n  const {\n    domain: domain_,\n    message: message_,\n    primaryType,\n    types,\n  } = value as unknown as serialize.Value\n\n  const normalizeData = (\n    struct: readonly Parameter[],\n    value: Record<string, unknown>,\n  ) => {\n    const data = { ...value }\n    for (const param of struct) {\n      const { name, type } = param\n      if (type === 'address') data[name] = (data[name] as string).toLowerCase()\n    }\n    return data\n  }\n\n  const domain = (() => {\n    if (!domain_) return {}\n    const type = types.EIP712Domain ?? extractEip712DomainTypes(domain_)\n    return normalizeData(type, domain_)\n  })()\n\n  const message = (() => {\n    if (primaryType === 'EIP712Domain') return undefined\n    if (!types[primaryType]) return {}\n    return normalizeData(types[primaryType], message_)\n  })()\n\n  return Json.stringify({ domain, message, primaryType, types }, (_, value) => {\n    if (typeof value === 'bigint') return value.toString()\n    return value\n  })\n}\n\nexport declare namespace serialize {\n  type Value<\n    typedData extends TypedData | Record<string, unknown> = TypedData,\n    primaryType extends keyof typedData | 'EIP712Domain' = keyof typedData,\n  > = Definition<typedData, primaryType>\n\n  type ErrorType = Json.stringify.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Checks if [EIP-712 Typed Data](https://eips.ethereum.org/EIPS/eip-712) is valid.\n *\n * @example\n * ```ts twoslash\n * import { TypedData } from 'ox'\n *\n * const valid = TypedData.validate({\n *   domain: {\n *     name: 'Ether!',\n *     version: '1',\n *     chainId: 1,\n *     verifyingContract: '******************************************',\n *   },\n *   primaryType: 'Foo',\n *   types: {\n *     Foo: [\n *       { name: 'address', type: 'address' },\n *       { name: 'name', type: 'string' },\n *       { name: 'foo', type: 'string' },\n *     ],\n *   },\n *   message: {\n *     address: '******************************************',\n *     name: 'jxom',\n *     foo: '******************************************',\n *   },\n * })\n * // @log: true\n * ```\n *\n * @param value - The Typed Data to validate.\n */\nexport function validate<\n  const typedData extends TypedData | Record<string, unknown>,\n  primaryType extends keyof typedData | 'EIP712Domain',\n>(value: assert.Value<typedData, primaryType>): boolean {\n  try {\n    assert(value)\n    return true\n  } catch {\n    return false\n  }\n}\n\nexport declare namespace validate {\n  type ErrorType = assert.ErrorType | Errors.GlobalErrorType\n}\n\n/** Thrown when the bytes size of a typed data value does not match the expected size. */\nexport class BytesSizeMismatchError extends Errors.BaseError {\n  override readonly name = 'TypedData.BytesSizeMismatchError'\n\n  constructor({\n    expectedSize,\n    givenSize,\n  }: { expectedSize: number; givenSize: number }) {\n    super(`Expected bytes${expectedSize}, got bytes${givenSize}.`)\n  }\n}\n\n/** Thrown when the domain is invalid. */\nexport class InvalidDomainError extends Errors.BaseError {\n  override readonly name = 'TypedData.InvalidDomainError'\n\n  constructor({ domain }: { domain: unknown }) {\n    super(`Invalid domain \"${Json.stringify(domain)}\".`, {\n      metaMessages: ['Must be a valid EIP-712 domain.'],\n    })\n  }\n}\n\n/** Thrown when the primary type of a typed data value is invalid. */\nexport class InvalidPrimaryTypeError extends Errors.BaseError {\n  override readonly name = 'TypedData.InvalidPrimaryTypeError'\n\n  constructor({\n    primaryType,\n    types,\n  }: { primaryType: string; types: TypedData | Record<string, unknown> }) {\n    super(\n      `Invalid primary type \\`${primaryType}\\` must be one of \\`${JSON.stringify(Object.keys(types))}\\`.`,\n      {\n        metaMessages: ['Check that the primary type is a key in `types`.'],\n      },\n    )\n  }\n}\n\n/** Thrown when the struct type is not a valid type. */\nexport class InvalidStructTypeError extends Errors.BaseError {\n  override readonly name = 'TypedData.InvalidStructTypeError'\n\n  constructor({ type }: { type: string }) {\n    super(`Struct type \"${type}\" is invalid.`, {\n      metaMessages: ['Struct type must not be a Solidity type.'],\n    })\n  }\n}\n\n/** @internal */\nexport function encodeData(value: {\n  data: Record<string, unknown>\n  primaryType: string\n  types: TypedData\n}): Hex.Hex {\n  const { data, primaryType, types } = value\n  const encodedTypes: AbiParameters.Parameter[] = [{ type: 'bytes32' }]\n  const encodedValues: unknown[] = [hashType({ primaryType, types })]\n\n  for (const field of types[primaryType] ?? []) {\n    const [type, value] = encodeField({\n      types,\n      name: field.name,\n      type: field.type,\n      value: data[field.name],\n    })\n    encodedTypes.push(type)\n    encodedValues.push(value)\n  }\n\n  return AbiParameters.encode(encodedTypes, encodedValues)\n}\n\n/** @internal */\nexport declare namespace encodeData {\n  type ErrorType =\n    | AbiParameters.encode.ErrorType\n    | encodeField.ErrorType\n    | hashType.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function hashType(value: {\n  primaryType: string\n  types: TypedData\n}): Hex.Hex {\n  const { primaryType, types } = value\n  const encodedHashType = Hex.fromString(encodeType({ primaryType, types }))\n  return Hash.keccak256(encodedHashType)\n}\n\n/** @internal */\nexport declare namespace hashType {\n  type ErrorType =\n    | Hex.fromString.ErrorType\n    | encodeType.ErrorType\n    | Hash.keccak256.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function encodeField(properties: {\n  types: TypedData\n  name: string\n  type: string\n  value: any\n}): [type: AbiParameters.Parameter, value: Hex.Hex] {\n  let { types, name, type, value } = properties\n\n  if (types[type] !== undefined)\n    return [\n      { type: 'bytes32' },\n      Hash.keccak256(encodeData({ data: value, primaryType: type, types })),\n    ]\n\n  if (type === 'bytes') {\n    const prepend = value.length % 2 ? '0' : ''\n    value = `0x${prepend + value.slice(2)}`\n    return [{ type: 'bytes32' }, Hash.keccak256(value, { as: 'Hex' })]\n  }\n\n  if (type === 'string')\n    return [\n      { type: 'bytes32' },\n      Hash.keccak256(Bytes.fromString(value), { as: 'Hex' }),\n    ]\n\n  if (type.lastIndexOf(']') === type.length - 1) {\n    const parsedType = type.slice(0, type.lastIndexOf('['))\n    const typeValuePairs = (value as [AbiParameters.Parameter, any][]).map(\n      (item) =>\n        encodeField({\n          name,\n          type: parsedType,\n          types,\n          value: item,\n        }),\n    )\n    return [\n      { type: 'bytes32' },\n      Hash.keccak256(\n        AbiParameters.encode(\n          typeValuePairs.map(([t]) => t),\n          typeValuePairs.map(([, v]) => v),\n        ),\n      ),\n    ]\n  }\n\n  return [{ type }, value]\n}\n\n/** @internal */\nexport declare namespace encodeField {\n  type ErrorType =\n    | AbiParameters.encode.ErrorType\n    | Hash.keccak256.ErrorType\n    | Bytes.fromString.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function findTypeDependencies(\n  value: {\n    primaryType: string\n    types: TypedData\n  },\n  results: Set<string> = new Set(),\n): Set<string> {\n  const { primaryType: primaryType_, types } = value\n  const match = primaryType_.match(/^\\w*/u)\n  const primaryType = match?.[0]!\n  if (results.has(primaryType) || types[primaryType] === undefined)\n    return results\n\n  results.add(primaryType)\n\n  for (const field of types[primaryType])\n    findTypeDependencies({ primaryType: field.type, types }, results)\n  return results\n}\n\n/** @internal */\nexport declare namespace findTypeDependencies {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/** @internal */\nfunction validateReference(type: string) {\n  // Struct type must not be a Solidity type.\n  if (\n    type === 'address' ||\n    type === 'bool' ||\n    type === 'string' ||\n    type.startsWith('bytes') ||\n    type.startsWith('uint') ||\n    type.startsWith('int')\n  )\n    throw new InvalidStructTypeError({ type })\n}\n", "import * as abitype from 'abitype'\nimport * as Address from './Address.js'\nimport * as Bytes from './Bytes.js'\nimport * as Errors from './Errors.js'\nimport * as Hex from './Hex.js'\nimport * as Solidity from './Solidity.js'\nimport * as internal from './internal/abiParameters.js'\nimport * as Cursor from './internal/cursor.js'\n\n/** Root type for ABI parameters. */\nexport type AbiParameters = readonly abitype.AbiParameter[]\n\n/** A parameter on an {@link ox#AbiParameters.AbiParameters}. */\nexport type Parameter = abitype.AbiParameter\n\n/** A packed ABI type. */\nexport type PackedAbiType =\n  | abitype.SolidityAddress\n  | abitype.SolidityBool\n  | abitype.SolidityBytes\n  | abitype.SolidityInt\n  | abitype.SolidityString\n  | abitype.SolidityArrayWithoutTuple\n\n/**\n * Decodes ABI-encoded data into its respective primitive values based on ABI Parameters.\n *\n * @example\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const data = AbiParameters.decode(\n *   AbiParameters.from(['string', 'uint', 'bool']),\n *   '******************************************00000000000000000000006000000000000000000000000000000000000000000000000000000000000001a4000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000057761676d69000000000000000000000000000000000000000000000000000000',\n * )\n * // @log: ['wagmi', 420n, true]\n * ```\n *\n * @example\n * ### JSON Parameters\n *\n * You can pass **JSON ABI** Parameters:\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const data = AbiParameters.decode(\n *   [\n *     { name: 'x', type: 'string' },\n *     { name: 'y', type: 'uint' },\n *     { name: 'z', type: 'bool' },\n *   ],\n *   '******************************************00000000000000000000006000000000000000000000000000000000000000000000000000000000000001a4000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000057761676d69000000000000000000000000000000000000000000000000000000',\n * )\n * // @log: ['wagmi', 420n, true]\n * ```\n *\n * @param parameters - The set of ABI parameters to decode, in the shape of the `inputs` or `outputs` attribute of an ABI Item. These parameters must include valid [ABI types](https://docs.soliditylang.org/en/latest/types.html).\n * @param data - ABI encoded data.\n * @param options - Decoding options.\n * @returns Array of decoded values.\n */\nexport function decode<\n  const parameters extends AbiParameters,\n  as extends 'Object' | 'Array' = 'Array',\n>(\n  parameters: parameters,\n  data: Bytes.Bytes | Hex.Hex,\n  options?: decode.Options<as>,\n): decode.ReturnType<parameters, as>\n\n// eslint-disable-next-line jsdoc/require-jsdoc\nexport function decode(\n  parameters: AbiParameters,\n  data: Bytes.Bytes | Hex.Hex,\n  options: {\n    as?: 'Array' | 'Object' | undefined\n    checksumAddress?: boolean | undefined\n  } = {},\n): readonly unknown[] | Record<string, unknown> {\n  const { as = 'Array', checksumAddress = false } = options\n\n  const bytes = typeof data === 'string' ? Bytes.fromHex(data) : data\n  const cursor = Cursor.create(bytes)\n\n  if (Bytes.size(bytes) === 0 && parameters.length > 0)\n    throw new ZeroDataError()\n  if (Bytes.size(bytes) && Bytes.size(bytes) < 32)\n    throw new DataSizeTooSmallError({\n      data: typeof data === 'string' ? data : Hex.fromBytes(data),\n      parameters: parameters as readonly Parameter[],\n      size: Bytes.size(bytes),\n    })\n\n  let consumed = 0\n  const values: any = as === 'Array' ? [] : {}\n  for (let i = 0; i < parameters.length; ++i) {\n    const param = parameters[i] as Parameter\n    cursor.setPosition(consumed)\n    const [data, consumed_] = internal.decodeParameter(cursor, param, {\n      checksumAddress,\n      staticPosition: 0,\n    })\n    consumed += consumed_\n    if (as === 'Array') values.push(data)\n    else values[param.name ?? i] = data\n  }\n  return values\n}\n\nexport declare namespace decode {\n  type Options<as extends 'Object' | 'Array'> = {\n    /**\n     * Whether the decoded values should be returned as an `Object` or `Array`.\n     *\n     * @default \"Array\"\n     */\n    as?: as | 'Object' | 'Array' | undefined\n    /**\n     * Whether decoded addresses should be checksummed.\n     *\n     * @default false\n     */\n    checksumAddress?: boolean | undefined\n  }\n\n  type ReturnType<\n    parameters extends AbiParameters = AbiParameters,\n    as extends 'Object' | 'Array' = 'Array',\n  > = parameters extends readonly []\n    ? as extends 'Object'\n      ? {}\n      : []\n    : as extends 'Object'\n      ? internal.ToObject<parameters>\n      : internal.ToPrimitiveTypes<parameters>\n\n  type ErrorType =\n    | Bytes.fromHex.ErrorType\n    | internal.decodeParameter.ErrorType\n    | ZeroDataError\n    | DataSizeTooSmallError\n    | Errors.GlobalErrorType\n}\n\n/**\n * Encodes primitive values into ABI encoded data as per the [Application Binary Interface (ABI) Specification](https://docs.soliditylang.org/en/latest/abi-spec).\n *\n * @example\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const data = AbiParameters.encode(\n *   AbiParameters.from(['string', 'uint', 'bool']),\n *   ['wagmi', 420n, true],\n * )\n * ```\n *\n * @example\n * ### JSON Parameters\n *\n * Specify **JSON ABI** Parameters as schema:\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const data = AbiParameters.encode(\n *   [\n *     { type: 'string', name: 'name' },\n *     { type: 'uint', name: 'age' },\n *     { type: 'bool', name: 'isOwner' },\n *   ],\n *   ['wagmi', 420n, true],\n * )\n * ```\n *\n * @param parameters - The set of ABI parameters to encode, in the shape of the `inputs` or `outputs` attribute of an ABI Item. These parameters must include valid [ABI types](https://docs.soliditylang.org/en/latest/types.html).\n * @param values - The set of primitive values that correspond to the ABI types defined in `parameters`.\n * @returns ABI encoded data.\n */\nexport function encode<\n  const parameters extends AbiParameters | readonly unknown[],\n>(\n  parameters: parameters,\n  values: parameters extends AbiParameters\n    ? internal.ToPrimitiveTypes<parameters>\n    : never,\n  options?: encode.Options,\n): Hex.Hex {\n  const { checksumAddress = false } = options ?? {}\n\n  if (parameters.length !== values.length)\n    throw new LengthMismatchError({\n      expectedLength: parameters.length as number,\n      givenLength: values.length as any,\n    })\n  // Prepare the parameters to determine dynamic types to encode.\n  const preparedParameters = internal.prepareParameters({\n    checksumAddress,\n    parameters: parameters as readonly Parameter[],\n    values: values as any,\n  })\n  const data = internal.encode(preparedParameters)\n  if (data.length === 0) return '0x'\n  return data\n}\n\nexport declare namespace encode {\n  type ErrorType =\n    | LengthMismatchError\n    | internal.encode.ErrorType\n    | internal.prepareParameters.ErrorType\n    | Errors.GlobalErrorType\n\n  type Options = {\n    /**\n     * Whether addresses should be checked against their checksum.\n     *\n     * @default false\n     */\n    checksumAddress?: boolean | undefined\n  }\n}\n\n/**\n * Encodes an array of primitive values to a [packed ABI encoding](https://docs.soliditylang.org/en/latest/abi-spec.html#non-standard-packed-mode).\n *\n * @example\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const encoded = AbiParameters.encodePacked(\n *   ['address', 'string'],\n *   ['******************************************', 'hello world'],\n * )\n * // @log: '******************************************68656c6c6f20776f726c64'\n * ```\n *\n * @param types - Set of ABI types to pack encode.\n * @param values - The set of primitive values that correspond to the ABI types defined in `types`.\n * @returns The encoded packed data.\n */\nexport function encodePacked<\n  const packedAbiTypes extends readonly PackedAbiType[] | readonly unknown[],\n>(types: packedAbiTypes, values: encodePacked.Values<packedAbiTypes>): Hex.Hex {\n  if (types.length !== values.length)\n    throw new LengthMismatchError({\n      expectedLength: types.length as number,\n      givenLength: values.length as number,\n    })\n\n  const data: Hex.Hex[] = []\n  for (let i = 0; i < (types as unknown[]).length; i++) {\n    const type = types[i]\n    const value = values[i]\n    data.push(encodePacked.encode(type, value))\n  }\n  return Hex.concat(...data)\n}\n\nexport namespace encodePacked {\n  export type ErrorType =\n    | Hex.concat.ErrorType\n    | LengthMismatchError\n    | Errors.GlobalErrorType\n\n  export type Values<\n    packedAbiTypes extends readonly PackedAbiType[] | readonly unknown[],\n  > = {\n    [key in keyof packedAbiTypes]: packedAbiTypes[key] extends abitype.AbiType\n      ? abitype.AbiParameterToPrimitiveType<{ type: packedAbiTypes[key] }>\n      : unknown\n  }\n\n  // eslint-disable-next-line jsdoc/require-jsdoc\n  export function encode<const packedAbiType extends PackedAbiType | unknown>(\n    type: packedAbiType,\n    value: Values<[packedAbiType]>[0],\n    isArray = false,\n  ): Hex.Hex {\n    if (type === 'address') {\n      const address = value as Address.Address\n      Address.assert(address)\n      return Hex.padLeft(\n        address.toLowerCase() as Hex.Hex,\n        isArray ? 32 : 0,\n      ) as Address.Address\n    }\n    if (type === 'string') return Hex.fromString(value as string)\n    if (type === 'bytes') return value as Hex.Hex\n    if (type === 'bool')\n      return Hex.padLeft(Hex.fromBoolean(value as boolean), isArray ? 32 : 1)\n\n    const intMatch = (type as string).match(Solidity.integerRegex)\n    if (intMatch) {\n      const [_type, baseType, bits = '256'] = intMatch\n      const size = Number.parseInt(bits) / 8\n      return Hex.fromNumber(value as number, {\n        size: isArray ? 32 : size,\n        signed: baseType === 'int',\n      })\n    }\n\n    const bytesMatch = (type as string).match(Solidity.bytesRegex)\n    if (bytesMatch) {\n      const [_type, size] = bytesMatch\n      if (Number.parseInt(size!) !== ((value as Hex.Hex).length - 2) / 2)\n        throw new BytesSizeMismatchError({\n          expectedSize: Number.parseInt(size!),\n          value: value as Hex.Hex,\n        })\n      return Hex.padRight(value as Hex.Hex, isArray ? 32 : 0) as Hex.Hex\n    }\n\n    const arrayMatch = (type as string).match(Solidity.arrayRegex)\n    if (arrayMatch && Array.isArray(value)) {\n      const [_type, childType] = arrayMatch\n      const data: Hex.Hex[] = []\n      for (let i = 0; i < value.length; i++) {\n        data.push(encode(childType, value[i], true))\n      }\n      if (data.length === 0) return '0x'\n      return Hex.concat(...data)\n    }\n\n    throw new InvalidTypeError(type as string)\n  }\n}\n\n/**\n * Formats {@link ox#AbiParameters.AbiParameters} into **Human Readable ABI Parameters**.\n *\n * @example\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const formatted = AbiParameters.format([\n *   {\n *     name: 'spender',\n *     type: 'address',\n *   },\n *   {\n *     name: 'amount',\n *     type: 'uint256',\n *   },\n * ])\n *\n * formatted\n * //    ^?\n *\n *\n * ```\n *\n * @param parameters - The ABI Parameters to format.\n * @returns The formatted ABI Parameters  .\n */\nexport function format<\n  const parameters extends readonly [\n    Parameter | abitype.AbiEventParameter,\n    ...(readonly (Parameter | abitype.AbiEventParameter)[]),\n  ],\n>(\n  parameters:\n    | parameters\n    | readonly [\n        Parameter | abitype.AbiEventParameter,\n        ...(readonly (Parameter | abitype.AbiEventParameter)[]),\n      ],\n): abitype.FormatAbiParameters<parameters> {\n  return abitype.formatAbiParameters(parameters)\n}\n\nexport declare namespace format {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Parses arbitrary **JSON ABI Parameters** or **Human Readable ABI Parameters** into typed {@link ox#AbiParameters.AbiParameters}.\n *\n * @example\n * ### JSON Parameters\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const parameters = AbiParameters.from([\n *   {\n *     name: 'spender',\n *     type: 'address',\n *   },\n *   {\n *     name: 'amount',\n *     type: 'uint256',\n *   },\n * ])\n *\n * parameters\n * //^?\n *\n *\n *\n *\n *\n *\n *\n * ```\n *\n * @example\n * ### Human Readable Parameters\n *\n * Human Readable ABI Parameters can be parsed into a typed {@link ox#AbiParameters.AbiParameters}:\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const parameters = AbiParameters.from('address spender, uint256 amount')\n *\n * parameters\n * //^?\n *\n *\n *\n *\n *\n *\n *\n * ```\n *\n * @example\n * It is possible to specify `struct`s along with your definitions:\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * const parameters = AbiParameters.from([\n *   'struct Foo { address spender; uint256 amount; }', // [!code hl]\n *   'Foo foo, address bar',\n * ])\n *\n * parameters\n * //^?\n *\n *\n *\n *\n *\n *\n *\n *\n *\n *\n *\n *\n * ```\n *\n *\n *\n * @param parameters - The ABI Parameters to parse.\n * @returns The typed ABI Parameters.\n */\nexport function from<\n  const parameters extends AbiParameters | string | readonly string[],\n>(\n  parameters: parameters | AbiParameters | string | readonly string[],\n): from.ReturnType<parameters> {\n  if (Array.isArray(parameters) && typeof parameters[0] === 'string')\n    return abitype.parseAbiParameters(parameters) as never\n  if (typeof parameters === 'string')\n    return abitype.parseAbiParameters(parameters) as never\n  return parameters as never\n}\n\nexport declare namespace from {\n  type ReturnType<\n    parameters extends AbiParameters | string | readonly string[],\n  > = parameters extends string\n    ? abitype.ParseAbiParameters<parameters>\n    : parameters extends readonly string[]\n      ? abitype.ParseAbiParameters<parameters>\n      : parameters\n\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Throws when the data size is too small for the given parameters.\n *\n * @example\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * AbiParameters.decode([{ type: 'uint256' }], '0x010f')\n * //                                             ↑ ❌ 2 bytes\n * // @error: AbiParameters.DataSizeTooSmallError: Data size of 2 bytes is too small for given parameters.\n * // @error: Params: (uint256)\n * // @error: Data:   0x010f (2 bytes)\n * ```\n *\n * ### Solution\n *\n * Pass a valid data size.\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * AbiParameters.decode([{ type: 'uint256' }], '******************************************0000000000000000000010f')\n * //                                             ↑ ✅ 32 bytes\n * ```\n */\nexport class DataSizeTooSmallError extends Errors.BaseError {\n  override readonly name = 'AbiParameters.DataSizeTooSmallError'\n  constructor({\n    data,\n    parameters,\n    size,\n  }: { data: Hex.Hex; parameters: readonly Parameter[]; size: number }) {\n    super(`Data size of ${size} bytes is too small for given parameters.`, {\n      metaMessages: [\n        `Params: (${abitype.formatAbiParameters(parameters as readonly [Parameter])})`,\n        `Data:   ${data} (${size} bytes)`,\n      ],\n    })\n  }\n}\n\n/**\n * Throws when zero data is provided, but data is expected.\n *\n * @example\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * AbiParameters.decode([{ type: 'uint256' }], '0x')\n * //                                           ↑ ❌ zero data\n * // @error: AbiParameters.DataSizeTooSmallError: Data size of 2 bytes is too small for given parameters.\n * // @error: Params: (uint256)\n * // @error: Data:   0x010f (2 bytes)\n * ```\n *\n * ### Solution\n *\n * Pass valid data.\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * AbiParameters.decode([{ type: 'uint256' }], '******************************************0000000000000000000010f')\n * //                                             ↑ ✅ 32 bytes\n * ```\n */\nexport class ZeroDataError extends Errors.BaseError {\n  override readonly name = 'AbiParameters.ZeroDataError'\n  constructor() {\n    super('Cannot decode zero data (\"0x\") with ABI parameters.')\n  }\n}\n\n/**\n * The length of the array value does not match the length specified in the corresponding ABI parameter.\n *\n * ### Example\n *\n * ```ts twoslash\n * // @noErrors\n * import { AbiParameters } from 'ox'\n * // ---cut---\n * AbiParameters.encode(AbiParameters.from('uint256[3]'), [[69n, 420n]])\n * //                                               ↑ expected: 3  ↑ ❌ length: 2\n * // @error: AbiParameters.ArrayLengthMismatchError: ABI encoding array length mismatch\n * // @error: for type `uint256[3]`. Expected: `3`. Given: `2`.\n * ```\n *\n * ### Solution\n *\n * Pass an array of the correct length.\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n * // ---cut---\n * AbiParameters.encode(AbiParameters.from(['uint256[3]']), [[69n, 420n, 69n]])\n * //                                                         ↑ ✅ length: 3\n * ```\n */\nexport class ArrayLengthMismatchError extends Errors.BaseError {\n  override readonly name = 'AbiParameters.ArrayLengthMismatchError'\n  constructor({\n    expectedLength,\n    givenLength,\n    type,\n  }: { expectedLength: number; givenLength: number; type: string }) {\n    super(\n      `Array length mismatch for type \\`${type}\\`. Expected: \\`${expectedLength}\\`. Given: \\`${givenLength}\\`.`,\n    )\n  }\n}\n\n/**\n * The size of the bytes value does not match the size specified in the corresponding ABI parameter.\n *\n * ### Example\n *\n * ```ts twoslash\n * // @noErrors\n * import { AbiParameters } from 'ox'\n * // ---cut---\n * AbiParameters.encode(AbiParameters.from('bytes8'), [['0xdeadbeefdeadbeefdeadbeef']])\n * //                                            ↑ expected: 8 bytes  ↑ ❌ size: 12 bytes\n * // @error: BytesSizeMismatchError: Size of bytes \"0xdeadbeefdeadbeefdeadbeef\"\n * // @error: (bytes12) does not match expected size (bytes8).\n * ```\n *\n * ### Solution\n *\n * Pass a bytes value of the correct size.\n *\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n * // ---cut---\n * AbiParameters.encode(AbiParameters.from(['bytes8']), ['0xdeadbeefdeadbeef'])\n * //                                                       ↑ ✅ size: 8 bytes\n * ```\n */\nexport class BytesSizeMismatchError extends Errors.BaseError {\n  override readonly name = 'AbiParameters.BytesSizeMismatchError'\n  constructor({\n    expectedSize,\n    value,\n  }: { expectedSize: number; value: Hex.Hex }) {\n    super(\n      `Size of bytes \"${value}\" (bytes${Hex.size(\n        value,\n      )}) does not match expected size (bytes${expectedSize}).`,\n    )\n  }\n}\n\n/**\n * The length of the values to encode does not match the length of the ABI parameters.\n *\n * ### Example\n *\n * ```ts twoslash\n * // @noErrors\n * import { AbiParameters } from 'ox'\n * // ---cut---\n * AbiParameters.encode(AbiParameters.from(['string', 'uint256']), ['hello'])\n * // @error: LengthMismatchError: ABI encoding params/values length mismatch.\n * // @error: Expected length (params): 2\n * // @error: Given length (values): 1\n * ```\n *\n * ### Solution\n *\n * Pass the correct number of values to encode.\n *\n * ### Solution\n *\n * Pass a [valid ABI type](https://docs.soliditylang.org/en/develop/abi-spec.html#types).\n */\nexport class LengthMismatchError extends Errors.BaseError {\n  override readonly name = 'AbiParameters.LengthMismatchError'\n  constructor({\n    expectedLength,\n    givenLength,\n  }: { expectedLength: number; givenLength: number }) {\n    super(\n      [\n        'ABI encoding parameters/values length mismatch.',\n        `Expected length (parameters): ${expectedLength}`,\n        `Given length (values): ${givenLength}`,\n      ].join('\\n'),\n    )\n  }\n}\n\n/**\n * The value provided is not a valid array as specified in the corresponding ABI parameter.\n *\n * ### Example\n *\n * ```ts twoslash\n * // @noErrors\n * import { AbiParameters } from 'ox'\n * // ---cut---\n * AbiParameters.encode(AbiParameters.from(['uint256[3]']), [69])\n * ```\n *\n * ### Solution\n *\n * Pass an array value.\n */\nexport class InvalidArrayError extends Errors.BaseError {\n  override readonly name = 'AbiParameters.InvalidArrayError'\n  constructor(value: unknown) {\n    super(`Value \\`${value}\\` is not a valid array.`)\n  }\n}\n\n/**\n * Throws when the ABI parameter type is invalid.\n *\n * @example\n * ```ts twoslash\n * import { AbiParameters } from 'ox'\n *\n * AbiParameters.decode([{ type: 'lol' }], '******************************************0000000000000000000010f')\n * //                             ↑ ❌ invalid type\n * // @error: AbiParameters.InvalidTypeError: Type `lol` is not a valid ABI Type.\n * ```\n */\nexport class InvalidTypeError extends Errors.BaseError {\n  override readonly name = 'AbiParameters.InvalidTypeError'\n  constructor(type: string) {\n    super(`Type \\`${type}\\` is not a valid ABI Type.`)\n  }\n}\n", "import type {\n  AbiParameter,\n  AbiParameterKind,\n  AbiParameterToPrimitiveType,\n  AbiParametersToPrimitiveTypes,\n} from 'abitype'\nimport * as AbiParameters from '../AbiParameters.js'\nimport * as Address from '../Address.js'\nimport * as Bytes from '../Bytes.js'\nimport * as Errors from '../Errors.js'\nimport * as Hex from '../Hex.js'\nimport { integerRegex } from '../Solidity.js'\nimport type * as Cursor from './cursor.js'\nimport type { Compute, IsNarrowable, UnionToIntersection } from './types.js'\n\n/** @internal */\nexport type ParameterToPrimitiveType<\n  abiParameter extends AbiParameter | { name: string; type: unknown },\n  abiParameterKind extends AbiParameterKind = AbiParameterKind,\n> = AbiParameterToPrimitiveType<abiParameter, abiParameterKind>\n\n/** @internal */\nexport type PreparedParameter = { dynamic: boolean; encoded: Hex.Hex }\n\n/** @internal */\nexport type ToObject<\n  parameters extends readonly AbiParameter[],\n  kind extends AbiParameterKind = AbiParameterKind,\n> = IsNarrowable<parameters, AbiParameters.AbiParameters> extends true\n  ? Compute<\n      UnionToIntersection<\n        {\n          [index in keyof parameters]: parameters[index] extends {\n            name: infer name extends string\n          }\n            ? {\n                [key in name]: AbiParameterToPrimitiveType<\n                  parameters[index],\n                  kind\n                >\n              }\n            : {\n                [key in index]: AbiParameterToPrimitiveType<\n                  parameters[index],\n                  kind\n                >\n              }\n        }[number]\n      >\n    >\n  : unknown\n\n/** @internal */\nexport type ToPrimitiveTypes<\n  abiParameters extends readonly AbiParameter[],\n  abiParameterKind extends AbiParameterKind = AbiParameterKind,\n> = AbiParametersToPrimitiveTypes<abiParameters, abiParameterKind>\n\n/** @internal */\nexport type Tuple = ParameterToPrimitiveType<TupleAbiParameter>\n\n/** @internal */\nexport function decodeParameter(\n  cursor: Cursor.Cursor,\n  param: AbiParameters.Parameter,\n  options: { checksumAddress?: boolean | undefined; staticPosition: number },\n) {\n  const { checksumAddress, staticPosition } = options\n  const arrayComponents = getArrayComponents(param.type)\n  if (arrayComponents) {\n    const [length, type] = arrayComponents\n    return decodeArray(\n      cursor,\n      { ...param, type },\n      { checksumAddress, length, staticPosition },\n    )\n  }\n  if (param.type === 'tuple')\n    return decodeTuple(cursor, param as TupleAbiParameter, {\n      checksumAddress,\n      staticPosition,\n    })\n  if (param.type === 'address')\n    return decodeAddress(cursor, { checksum: checksumAddress })\n  if (param.type === 'bool') return decodeBool(cursor)\n  if (param.type.startsWith('bytes'))\n    return decodeBytes(cursor, param, { staticPosition })\n  if (param.type.startsWith('uint') || param.type.startsWith('int'))\n    return decodeNumber(cursor, param)\n  if (param.type === 'string') return decodeString(cursor, { staticPosition })\n  throw new AbiParameters.InvalidTypeError(param.type)\n}\n\nexport declare namespace decodeParameter {\n  type ErrorType =\n    | decodeArray.ErrorType\n    | decodeTuple.ErrorType\n    | decodeAddress.ErrorType\n    | decodeBool.ErrorType\n    | decodeBytes.ErrorType\n    | decodeNumber.ErrorType\n    | decodeString.ErrorType\n    | AbiParameters.InvalidTypeError\n    | Errors.GlobalErrorType\n}\n\nconst sizeOfLength = 32\nconst sizeOfOffset = 32\n\n/** @internal */\nexport function decodeAddress(\n  cursor: Cursor.Cursor,\n  options: { checksum?: boolean | undefined } = {},\n) {\n  const { checksum = false } = options\n  const value = cursor.readBytes(32)\n  const wrap = (address: Hex.Hex) =>\n    checksum ? Address.checksum(address) : address\n  return [wrap(Hex.fromBytes(Bytes.slice(value, -20))), 32]\n}\n\nexport declare namespace decodeAddress {\n  type ErrorType =\n    | Hex.fromBytes.ErrorType\n    | Bytes.slice.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function decodeArray(\n  cursor: Cursor.Cursor,\n  param: AbiParameters.Parameter,\n  options: {\n    checksumAddress?: boolean | undefined\n    length: number | null\n    staticPosition: number\n  },\n) {\n  const { checksumAddress, length, staticPosition } = options\n\n  // If the length of the array is not known in advance (dynamic array),\n  // this means we will need to wonder off to the pointer and decode.\n  if (!length) {\n    // Dealing with a dynamic type, so get the offset of the array data.\n    const offset = Bytes.toNumber(cursor.readBytes(sizeOfOffset))\n\n    // Start is the static position of current slot + offset.\n    const start = staticPosition + offset\n    const startOfData = start + sizeOfLength\n\n    // Get the length of the array from the offset.\n    cursor.setPosition(start)\n    const length = Bytes.toNumber(cursor.readBytes(sizeOfLength))\n\n    // Check if the array has any dynamic children.\n    const dynamicChild = hasDynamicChild(param)\n\n    let consumed = 0\n    const value: unknown[] = []\n    for (let i = 0; i < length; ++i) {\n      // If any of the children is dynamic, then all elements will be offset pointer, thus size of one slot (32 bytes).\n      // Otherwise, elements will be the size of their encoding (consumed bytes).\n      cursor.setPosition(startOfData + (dynamicChild ? i * 32 : consumed))\n      const [data, consumed_] = decodeParameter(cursor, param, {\n        checksumAddress,\n        staticPosition: startOfData,\n      })\n      consumed += consumed_\n      value.push(data)\n    }\n\n    // As we have gone wondering, restore to the original position + next slot.\n    cursor.setPosition(staticPosition + 32)\n    return [value, 32]\n  }\n\n  // If the length of the array is known in advance,\n  // and the length of an element deeply nested in the array is not known,\n  // we need to decode the offset of the array data.\n  if (hasDynamicChild(param)) {\n    // Dealing with dynamic types, so get the offset of the array data.\n    const offset = Bytes.toNumber(cursor.readBytes(sizeOfOffset))\n\n    // Start is the static position of current slot + offset.\n    const start = staticPosition + offset\n\n    const value: unknown[] = []\n    for (let i = 0; i < length; ++i) {\n      // Move cursor along to the next slot (next offset pointer).\n      cursor.setPosition(start + i * 32)\n      const [data] = decodeParameter(cursor, param, {\n        checksumAddress,\n        staticPosition: start,\n      })\n      value.push(data)\n    }\n\n    // As we have gone wondering, restore to the original position + next slot.\n    cursor.setPosition(staticPosition + 32)\n    return [value, 32]\n  }\n\n  // If the length of the array is known in advance and the array is deeply static,\n  // then we can just decode each element in sequence.\n  let consumed = 0\n  const value: unknown[] = []\n  for (let i = 0; i < length; ++i) {\n    const [data, consumed_] = decodeParameter(cursor, param, {\n      checksumAddress,\n      staticPosition: staticPosition + consumed,\n    })\n    consumed += consumed_\n    value.push(data)\n  }\n  return [value, consumed]\n}\n\nexport declare namespace decodeArray {\n  type ErrorType = Bytes.toNumber.ErrorType | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function decodeBool(cursor: Cursor.Cursor) {\n  return [Bytes.toBoolean(cursor.readBytes(32), { size: 32 }), 32]\n}\n\nexport declare namespace decodeBool {\n  type ErrorType = Bytes.toBoolean.ErrorType | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function decodeBytes(\n  cursor: Cursor.Cursor,\n  param: AbiParameters.Parameter,\n  { staticPosition }: { staticPosition: number },\n) {\n  const [_, size] = param.type.split('bytes')\n  if (!size) {\n    // Dealing with dynamic types, so get the offset of the bytes data.\n    const offset = Bytes.toNumber(cursor.readBytes(32))\n\n    // Set position of the cursor to start of bytes data.\n    cursor.setPosition(staticPosition + offset)\n\n    const length = Bytes.toNumber(cursor.readBytes(32))\n\n    // If there is no length, we have zero data.\n    if (length === 0) {\n      // As we have gone wondering, restore to the original position + next slot.\n      cursor.setPosition(staticPosition + 32)\n      return ['0x', 32]\n    }\n\n    const data = cursor.readBytes(length)\n\n    // As we have gone wondering, restore to the original position + next slot.\n    cursor.setPosition(staticPosition + 32)\n    return [Hex.fromBytes(data), 32]\n  }\n\n  const value = Hex.fromBytes(cursor.readBytes(Number.parseInt(size), 32))\n  return [value, 32]\n}\n\nexport declare namespace decodeBytes {\n  type ErrorType =\n    | Hex.fromBytes.ErrorType\n    | Bytes.toNumber.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function decodeNumber(\n  cursor: Cursor.Cursor,\n  param: AbiParameters.Parameter,\n) {\n  const signed = param.type.startsWith('int')\n  const size = Number.parseInt(param.type.split('int')[1] || '256')\n  const value = cursor.readBytes(32)\n  return [\n    size > 48\n      ? Bytes.toBigInt(value, { signed })\n      : Bytes.toNumber(value, { signed }),\n    32,\n  ]\n}\n\nexport declare namespace decodeNumber {\n  type ErrorType =\n    | Bytes.toNumber.ErrorType\n    | Bytes.toBigInt.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport type TupleAbiParameter = AbiParameters.Parameter & {\n  components: readonly AbiParameters.Parameter[]\n}\n\n/** @internal */\nexport function decodeTuple(\n  cursor: Cursor.Cursor,\n  param: TupleAbiParameter,\n  options: { checksumAddress?: boolean | undefined; staticPosition: number },\n) {\n  const { checksumAddress, staticPosition } = options\n\n  // Tuples can have unnamed components (i.e. they are arrays), so we must\n  // determine whether the tuple is named or unnamed. In the case of a named\n  // tuple, the value will be an object where each property is the name of the\n  // component. In the case of an unnamed tuple, the value will be an array.\n  const hasUnnamedChild =\n    param.components.length === 0 || param.components.some(({ name }) => !name)\n\n  // Initialize the value to an object or an array, depending on whether the\n  // tuple is named or unnamed.\n  const value: any = hasUnnamedChild ? [] : {}\n  let consumed = 0\n\n  // If the tuple has a dynamic child, we must first decode the offset to the\n  // tuple data.\n  if (hasDynamicChild(param)) {\n    // Dealing with dynamic types, so get the offset of the tuple data.\n    const offset = Bytes.toNumber(cursor.readBytes(sizeOfOffset))\n\n    // Start is the static position of referencing slot + offset.\n    const start = staticPosition + offset\n\n    for (let i = 0; i < param.components.length; ++i) {\n      const component = param.components[i]!\n      cursor.setPosition(start + consumed)\n      const [data, consumed_] = decodeParameter(cursor, component, {\n        checksumAddress,\n        staticPosition: start,\n      })\n      consumed += consumed_\n      value[hasUnnamedChild ? i : component?.name!] = data\n    }\n\n    // As we have gone wondering, restore to the original position + next slot.\n    cursor.setPosition(staticPosition + 32)\n    return [value, 32]\n  }\n\n  // If the tuple has static children, we can just decode each component\n  // in sequence.\n  for (let i = 0; i < param.components.length; ++i) {\n    const component = param.components[i]!\n    const [data, consumed_] = decodeParameter(cursor, component, {\n      checksumAddress,\n      staticPosition,\n    })\n    value[hasUnnamedChild ? i : component?.name!] = data\n    consumed += consumed_\n  }\n  return [value, consumed]\n}\n\nexport declare namespace decodeTuple {\n  type ErrorType = Bytes.toNumber.ErrorType | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function decodeString(\n  cursor: Cursor.Cursor,\n  { staticPosition }: { staticPosition: number },\n) {\n  // Get offset to start of string data.\n  const offset = Bytes.toNumber(cursor.readBytes(32))\n\n  // Start is the static position of current slot + offset.\n  const start = staticPosition + offset\n  cursor.setPosition(start)\n\n  const length = Bytes.toNumber(cursor.readBytes(32))\n\n  // If there is no length, we have zero data (empty string).\n  if (length === 0) {\n    cursor.setPosition(staticPosition + 32)\n    return ['', 32]\n  }\n\n  const data = cursor.readBytes(length, 32)\n  const value = Bytes.toString(Bytes.trimLeft(data))\n\n  // As we have gone wondering, restore to the original position + next slot.\n  cursor.setPosition(staticPosition + 32)\n\n  return [value, 32]\n}\n\nexport declare namespace decodeString {\n  type ErrorType =\n    | Bytes.toNumber.ErrorType\n    | Bytes.toString.ErrorType\n    | Bytes.trimLeft.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function prepareParameters<\n  const parameters extends AbiParameters.AbiParameters,\n>({\n  checksumAddress,\n  parameters,\n  values,\n}: {\n  checksumAddress?: boolean | undefined\n  parameters: parameters\n  values: parameters extends AbiParameters.AbiParameters\n    ? ToPrimitiveTypes<parameters>\n    : never\n}) {\n  const preparedParameters: PreparedParameter[] = []\n  for (let i = 0; i < parameters.length; i++) {\n    preparedParameters.push(\n      prepareParameter({\n        checksumAddress,\n        parameter: parameters[i]!,\n        value: values[i],\n      }),\n    )\n  }\n  return preparedParameters\n}\n\n/** @internal */\nexport declare namespace prepareParameters {\n  type ErrorType = prepareParameter.ErrorType | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function prepareParameter<\n  const parameter extends AbiParameters.Parameter,\n>({\n  checksumAddress = false,\n  parameter: parameter_,\n  value,\n}: {\n  parameter: parameter\n  value: parameter extends AbiParameters.Parameter\n    ? ParameterToPrimitiveType<parameter>\n    : never\n  checksumAddress?: boolean | undefined\n}): PreparedParameter {\n  const parameter = parameter_ as AbiParameters.Parameter\n\n  const arrayComponents = getArrayComponents(parameter.type)\n  if (arrayComponents) {\n    const [length, type] = arrayComponents\n    return encodeArray(value, {\n      checksumAddress,\n      length,\n      parameter: {\n        ...parameter,\n        type,\n      },\n    })\n  }\n  if (parameter.type === 'tuple') {\n    return encodeTuple(value as unknown as Tuple, {\n      checksumAddress,\n      parameter: parameter as TupleAbiParameter,\n    })\n  }\n  if (parameter.type === 'address') {\n    return encodeAddress(value as unknown as Hex.Hex, {\n      checksum: checksumAddress,\n    })\n  }\n  if (parameter.type === 'bool') {\n    return encodeBoolean(value as unknown as boolean)\n  }\n  if (parameter.type.startsWith('uint') || parameter.type.startsWith('int')) {\n    const signed = parameter.type.startsWith('int')\n    const [, , size = '256'] = integerRegex.exec(parameter.type) ?? []\n    return encodeNumber(value as unknown as number, {\n      signed,\n      size: Number(size),\n    })\n  }\n  if (parameter.type.startsWith('bytes')) {\n    return encodeBytes(value as unknown as Hex.Hex, { type: parameter.type })\n  }\n  if (parameter.type === 'string') {\n    return encodeString(value as unknown as string)\n  }\n  throw new AbiParameters.InvalidTypeError(parameter.type)\n}\n\n/** @internal */\nexport declare namespace prepareParameter {\n  type ErrorType =\n    | encodeArray.ErrorType\n    | encodeTuple.ErrorType\n    | encodeAddress.ErrorType\n    | encodeBoolean.ErrorType\n    | encodeBytes.ErrorType\n    | encodeString.ErrorType\n    | AbiParameters.InvalidTypeError\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function encode(preparedParameters: PreparedParameter[]): Hex.Hex {\n  // 1. Compute the size of the static part of the parameters.\n  let staticSize = 0\n  for (let i = 0; i < preparedParameters.length; i++) {\n    const { dynamic, encoded } = preparedParameters[i]!\n    if (dynamic) staticSize += 32\n    else staticSize += Hex.size(encoded)\n  }\n\n  // 2. Split the parameters into static and dynamic parts.\n  const staticParameters: Hex.Hex[] = []\n  const dynamicParameters: Hex.Hex[] = []\n  let dynamicSize = 0\n  for (let i = 0; i < preparedParameters.length; i++) {\n    const { dynamic, encoded } = preparedParameters[i]!\n    if (dynamic) {\n      staticParameters.push(\n        Hex.fromNumber(staticSize + dynamicSize, { size: 32 }),\n      )\n      dynamicParameters.push(encoded)\n      dynamicSize += Hex.size(encoded)\n    } else {\n      staticParameters.push(encoded)\n    }\n  }\n\n  // 3. Concatenate static and dynamic parts.\n  return Hex.concat(...staticParameters, ...dynamicParameters)\n}\n\n/** @internal */\nexport declare namespace encode {\n  type ErrorType =\n    | Hex.concat.ErrorType\n    | Hex.fromNumber.ErrorType\n    | Hex.size.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function encodeAddress(\n  value: Hex.Hex,\n  options: { checksum: boolean },\n): PreparedParameter {\n  const { checksum = false } = options\n  Address.assert(value, { strict: checksum })\n  return {\n    dynamic: false,\n    encoded: Hex.padLeft(value.toLowerCase() as Hex.Hex),\n  }\n}\n\n/** @internal */\nexport declare namespace encodeAddress {\n  type ErrorType =\n    | Address.assert.ErrorType\n    | Hex.padLeft.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function encodeArray<const parameter extends AbiParameters.Parameter>(\n  value: ParameterToPrimitiveType<parameter>,\n  options: {\n    checksumAddress?: boolean | undefined\n    length: number | null\n    parameter: parameter\n  },\n): PreparedParameter {\n  const { checksumAddress, length, parameter } = options\n\n  const dynamic = length === null\n\n  if (!Array.isArray(value)) throw new AbiParameters.InvalidArrayError(value)\n  if (!dynamic && value.length !== length)\n    throw new AbiParameters.ArrayLengthMismatchError({\n      expectedLength: length!,\n      givenLength: value.length,\n      type: `${parameter.type}[${length}]`,\n    })\n\n  let dynamicChild = false\n  const preparedParameters: PreparedParameter[] = []\n  for (let i = 0; i < value.length; i++) {\n    const preparedParam = prepareParameter({\n      checksumAddress,\n      parameter,\n      value: value[i],\n    })\n    if (preparedParam.dynamic) dynamicChild = true\n    preparedParameters.push(preparedParam)\n  }\n\n  if (dynamic || dynamicChild) {\n    const data = encode(preparedParameters)\n    if (dynamic) {\n      const length = Hex.fromNumber(preparedParameters.length, { size: 32 })\n      return {\n        dynamic: true,\n        encoded:\n          preparedParameters.length > 0 ? Hex.concat(length, data) : length,\n      }\n    }\n    if (dynamicChild) return { dynamic: true, encoded: data }\n  }\n  return {\n    dynamic: false,\n    encoded: Hex.concat(...preparedParameters.map(({ encoded }) => encoded)),\n  }\n}\n\n/** @internal */\nexport declare namespace encodeArray {\n  type ErrorType =\n    | AbiParameters.InvalidArrayError\n    | AbiParameters.ArrayLengthMismatchError\n    | Hex.concat.ErrorType\n    | Hex.fromNumber.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function encodeBytes(\n  value: Hex.Hex,\n  { type }: { type: string },\n): PreparedParameter {\n  const [, parametersize] = type.split('bytes')\n  const bytesSize = Hex.size(value)\n  if (!parametersize) {\n    let value_ = value\n    // If the size is not divisible by 32 bytes, pad the end\n    // with empty bytes to the ceiling 32 bytes.\n    if (bytesSize % 32 !== 0)\n      value_ = Hex.padRight(value_, Math.ceil((value.length - 2) / 2 / 32) * 32)\n    return {\n      dynamic: true,\n      encoded: Hex.concat(\n        Hex.padLeft(Hex.fromNumber(bytesSize, { size: 32 })),\n        value_,\n      ),\n    }\n  }\n  if (bytesSize !== Number.parseInt(parametersize))\n    throw new AbiParameters.BytesSizeMismatchError({\n      expectedSize: Number.parseInt(parametersize),\n      value,\n    })\n  return { dynamic: false, encoded: Hex.padRight(value) }\n}\n\n/** @internal */\nexport declare namespace encodeBytes {\n  type ErrorType =\n    | Hex.padLeft.ErrorType\n    | Hex.padRight.ErrorType\n    | Hex.fromNumber.ErrorType\n    | Hex.slice.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function encodeBoolean(value: boolean): PreparedParameter {\n  if (typeof value !== 'boolean')\n    throw new Errors.BaseError(\n      `Invalid boolean value: \"${value}\" (type: ${typeof value}). Expected: \\`true\\` or \\`false\\`.`,\n    )\n  return { dynamic: false, encoded: Hex.padLeft(Hex.fromBoolean(value)) }\n}\n\n/** @internal */\nexport declare namespace encodeBoolean {\n  type ErrorType =\n    | Hex.padLeft.ErrorType\n    | Hex.fromBoolean.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function encodeNumber(\n  value: number,\n  { signed, size }: { signed: boolean; size: number },\n): PreparedParameter {\n  if (typeof size === 'number') {\n    const max = 2n ** (BigInt(size) - (signed ? 1n : 0n)) - 1n\n    const min = signed ? -max - 1n : 0n\n    if (value > max || value < min)\n      throw new Hex.IntegerOutOfRangeError({\n        max: max.toString(),\n        min: min.toString(),\n        signed,\n        size: size / 8,\n        value: value.toString(),\n      })\n  }\n  return {\n    dynamic: false,\n    encoded: Hex.fromNumber(value, {\n      size: 32,\n      signed,\n    }),\n  }\n}\n\n/** @internal */\nexport declare namespace encodeNumber {\n  type ErrorType = Hex.fromNumber.ErrorType | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function encodeString(value: string): PreparedParameter {\n  const hexValue = Hex.fromString(value)\n  const partsLength = Math.ceil(Hex.size(hexValue) / 32)\n  const parts: Hex.Hex[] = []\n  for (let i = 0; i < partsLength; i++) {\n    parts.push(Hex.padRight(Hex.slice(hexValue, i * 32, (i + 1) * 32)))\n  }\n  return {\n    dynamic: true,\n    encoded: Hex.concat(\n      Hex.padRight(Hex.fromNumber(Hex.size(hexValue), { size: 32 })),\n      ...parts,\n    ),\n  }\n}\n\n/** @internal */\nexport declare namespace encodeString {\n  type ErrorType =\n    | Hex.fromNumber.ErrorType\n    | Hex.padRight.ErrorType\n    | Hex.slice.ErrorType\n    | Hex.size.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function encodeTuple<\n  const parameter extends AbiParameters.Parameter & {\n    components: readonly AbiParameters.Parameter[]\n  },\n>(\n  value: ParameterToPrimitiveType<parameter>,\n  options: {\n    checksumAddress?: boolean | undefined\n    parameter: parameter\n  },\n): PreparedParameter {\n  const { checksumAddress, parameter } = options\n\n  let dynamic = false\n  const preparedParameters: PreparedParameter[] = []\n  for (let i = 0; i < parameter.components.length; i++) {\n    const param_ = parameter.components[i]!\n    const index = Array.isArray(value) ? i : param_.name\n    const preparedParam = prepareParameter({\n      checksumAddress,\n      parameter: param_,\n      value: (value as any)[index!] as readonly unknown[],\n    })\n    preparedParameters.push(preparedParam)\n    if (preparedParam.dynamic) dynamic = true\n  }\n  return {\n    dynamic,\n    encoded: dynamic\n      ? encode(preparedParameters)\n      : Hex.concat(...preparedParameters.map(({ encoded }) => encoded)),\n  }\n}\n\n/** @internal */\nexport declare namespace encodeTuple {\n  type ErrorType = Hex.concat.ErrorType | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function getArrayComponents(\n  type: string,\n): [length: number | null, innerType: string] | undefined {\n  const matches = type.match(/^(.*)\\[(\\d+)?\\]$/)\n  return matches\n    ? // Return `null` if the array is dynamic.\n      [matches[2]! ? Number(matches[2]!) : null, matches[1]!]\n    : undefined\n}\n\n/** @internal */\nexport function hasDynamicChild(param: AbiParameters.Parameter) {\n  const { type } = param\n  if (type === 'string') return true\n  if (type === 'bytes') return true\n  if (type.endsWith('[]')) return true\n\n  if (type === 'tuple') return (param as any).components?.some(hasDynamicChild)\n\n  const arrayComponents = getArrayComponents(param.type)\n  if (\n    arrayComponents &&\n    hasDynamicChild({\n      ...param,\n      type: arrayComponents[1],\n    } as AbiParameters.Parameter)\n  )\n    return true\n\n  return false\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;;gCAAAA;EAAA;;;gBAAAC;EAAA;gBAAAC;EAAA;;;;;;;;;;kBAAAC;;;;ACDA;;;;;;;;;;gBAAAC;EAAA;;;;;;AC8DM,SAAU,gBACd,QACA,OACA,SAA0E;AAE1E,QAAM,EAAE,iBAAiB,eAAc,IAAK;AAC5C,QAAM,kBAAkB,mBAAmB,MAAM,IAAI;AACrD,MAAI,iBAAiB;AACnB,UAAM,CAAC,QAAQ,IAAI,IAAI;AACvB,WAAO,YACL,QACA,EAAE,GAAG,OAAO,KAAI,GAChB,EAAE,iBAAiB,QAAQ,eAAc,CAAE;EAE/C;AACA,MAAI,MAAM,SAAS;AACjB,WAAO,YAAY,QAAQ,OAA4B;MACrD;MACA;KACD;AACH,MAAI,MAAM,SAAS;AACjB,WAAO,cAAc,QAAQ,EAAE,UAAU,gBAAe,CAAE;AAC5D,MAAI,MAAM,SAAS;AAAQ,WAAO,WAAW,MAAM;AACnD,MAAI,MAAM,KAAK,WAAW,OAAO;AAC/B,WAAO,YAAY,QAAQ,OAAO,EAAE,eAAc,CAAE;AACtD,MAAI,MAAM,KAAK,WAAW,MAAM,KAAK,MAAM,KAAK,WAAW,KAAK;AAC9D,WAAO,aAAa,QAAQ,KAAK;AACnC,MAAI,MAAM,SAAS;AAAU,WAAO,aAAa,QAAQ,EAAE,eAAc,CAAE;AAC3E,QAAM,IAAkB,iBAAiB,MAAM,IAAI;AACrD;AAeA,IAAM,eAAe;AACrB,IAAM,eAAe;AAGf,SAAU,cACd,QACA,UAA8C,CAAA,GAAE;AAEhD,QAAM,EAAE,UAAAC,YAAW,MAAK,IAAK;AAC7B,QAAM,QAAQ,OAAO,UAAU,EAAE;AACjC,QAAM,OAAO,CAAC,YACZA,YAAmB,SAAS,OAAO,IAAI;AACzC,SAAO,CAAC,KAAS,UAAgB,MAAM,OAAO,GAAG,CAAC,CAAC,GAAG,EAAE;AAC1D;AAUM,SAAU,YACd,QACA,OACA,SAIC;AAED,QAAM,EAAE,iBAAiB,QAAQ,eAAc,IAAK;AAIpD,MAAI,CAAC,QAAQ;AAEX,UAAM,SAAe,SAAS,OAAO,UAAU,YAAY,CAAC;AAG5D,UAAM,QAAQ,iBAAiB;AAC/B,UAAM,cAAc,QAAQ;AAG5B,WAAO,YAAY,KAAK;AACxB,UAAMC,UAAe,SAAS,OAAO,UAAU,YAAY,CAAC;AAG5D,UAAM,eAAe,gBAAgB,KAAK;AAE1C,QAAIC,YAAW;AACf,UAAMC,SAAmB,CAAA;AACzB,aAAS,IAAI,GAAG,IAAIF,SAAQ,EAAE,GAAG;AAG/B,aAAO,YAAY,eAAe,eAAe,IAAI,KAAKC,UAAS;AACnE,YAAM,CAAC,MAAM,SAAS,IAAI,gBAAgB,QAAQ,OAAO;QACvD;QACA,gBAAgB;OACjB;AACD,MAAAA,aAAY;AACZ,MAAAC,OAAM,KAAK,IAAI;IACjB;AAGA,WAAO,YAAY,iBAAiB,EAAE;AACtC,WAAO,CAACA,QAAO,EAAE;EACnB;AAKA,MAAI,gBAAgB,KAAK,GAAG;AAE1B,UAAM,SAAe,SAAS,OAAO,UAAU,YAAY,CAAC;AAG5D,UAAM,QAAQ,iBAAiB;AAE/B,UAAMA,SAAmB,CAAA;AACzB,aAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAE/B,aAAO,YAAY,QAAQ,IAAI,EAAE;AACjC,YAAM,CAAC,IAAI,IAAI,gBAAgB,QAAQ,OAAO;QAC5C;QACA,gBAAgB;OACjB;AACD,MAAAA,OAAM,KAAK,IAAI;IACjB;AAGA,WAAO,YAAY,iBAAiB,EAAE;AACtC,WAAO,CAACA,QAAO,EAAE;EACnB;AAIA,MAAI,WAAW;AACf,QAAM,QAAmB,CAAA;AACzB,WAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC/B,UAAM,CAAC,MAAM,SAAS,IAAI,gBAAgB,QAAQ,OAAO;MACvD;MACA,gBAAgB,iBAAiB;KAClC;AACD,gBAAY;AACZ,UAAM,KAAK,IAAI;EACjB;AACA,SAAO,CAAC,OAAO,QAAQ;AACzB;AAOM,SAAU,WAAW,QAAqB;AAC9C,SAAO,CAAO,UAAU,OAAO,UAAU,EAAE,GAAG,EAAE,MAAM,GAAE,CAAE,GAAG,EAAE;AACjE;AAOM,SAAU,YACd,QACA,OACA,EAAE,eAAc,GAA8B;AAE9C,QAAM,CAAC,GAAGC,KAAI,IAAI,MAAM,KAAK,MAAM,OAAO;AAC1C,MAAI,CAACA,OAAM;AAET,UAAM,SAAe,SAAS,OAAO,UAAU,EAAE,CAAC;AAGlD,WAAO,YAAY,iBAAiB,MAAM;AAE1C,UAAM,SAAe,SAAS,OAAO,UAAU,EAAE,CAAC;AAGlD,QAAI,WAAW,GAAG;AAEhB,aAAO,YAAY,iBAAiB,EAAE;AACtC,aAAO,CAAC,MAAM,EAAE;IAClB;AAEA,UAAM,OAAO,OAAO,UAAU,MAAM;AAGpC,WAAO,YAAY,iBAAiB,EAAE;AACtC,WAAO,CAAK,UAAU,IAAI,GAAG,EAAE;EACjC;AAEA,QAAM,QAAY,UAAU,OAAO,UAAU,OAAO,SAASA,KAAI,GAAG,EAAE,CAAC;AACvE,SAAO,CAAC,OAAO,EAAE;AACnB;AAUM,SAAU,aACd,QACA,OAA8B;AAE9B,QAAM,SAAS,MAAM,KAAK,WAAW,KAAK;AAC1C,QAAMA,QAAO,OAAO,SAAS,MAAM,KAAK,MAAM,KAAK,EAAE,CAAC,KAAK,KAAK;AAChE,QAAM,QAAQ,OAAO,UAAU,EAAE;AACjC,SAAO;IACLA,QAAO,KACG,SAAS,OAAO,EAAE,OAAM,CAAE,IAC1B,SAAS,OAAO,EAAE,OAAM,CAAE;IACpC;;AAEJ;AAeM,SAAU,YACd,QACA,OACA,SAA0E;AAE1E,QAAM,EAAE,iBAAiB,eAAc,IAAK;AAM5C,QAAM,kBACJ,MAAM,WAAW,WAAW,KAAK,MAAM,WAAW,KAAK,CAAC,EAAE,KAAI,MAAO,CAAC,IAAI;AAI5E,QAAM,QAAa,kBAAkB,CAAA,IAAK,CAAA;AAC1C,MAAI,WAAW;AAIf,MAAI,gBAAgB,KAAK,GAAG;AAE1B,UAAM,SAAe,SAAS,OAAO,UAAU,YAAY,CAAC;AAG5D,UAAM,QAAQ,iBAAiB;AAE/B,aAAS,IAAI,GAAG,IAAI,MAAM,WAAW,QAAQ,EAAE,GAAG;AAChD,YAAM,YAAY,MAAM,WAAW,CAAC;AACpC,aAAO,YAAY,QAAQ,QAAQ;AACnC,YAAM,CAAC,MAAM,SAAS,IAAI,gBAAgB,QAAQ,WAAW;QAC3D;QACA,gBAAgB;OACjB;AACD,kBAAY;AACZ,YAAM,kBAAkB,IAAI,uCAAW,IAAK,IAAI;IAClD;AAGA,WAAO,YAAY,iBAAiB,EAAE;AACtC,WAAO,CAAC,OAAO,EAAE;EACnB;AAIA,WAAS,IAAI,GAAG,IAAI,MAAM,WAAW,QAAQ,EAAE,GAAG;AAChD,UAAM,YAAY,MAAM,WAAW,CAAC;AACpC,UAAM,CAAC,MAAM,SAAS,IAAI,gBAAgB,QAAQ,WAAW;MAC3D;MACA;KACD;AACD,UAAM,kBAAkB,IAAI,uCAAW,IAAK,IAAI;AAChD,gBAAY;EACd;AACA,SAAO,CAAC,OAAO,QAAQ;AACzB;AAOM,SAAU,aACd,QACA,EAAE,eAAc,GAA8B;AAG9C,QAAM,SAAe,SAAS,OAAO,UAAU,EAAE,CAAC;AAGlD,QAAM,QAAQ,iBAAiB;AAC/B,SAAO,YAAY,KAAK;AAExB,QAAM,SAAe,SAAS,OAAO,UAAU,EAAE,CAAC;AAGlD,MAAI,WAAW,GAAG;AAChB,WAAO,YAAY,iBAAiB,EAAE;AACtC,WAAO,CAAC,IAAI,EAAE;EAChB;AAEA,QAAM,OAAO,OAAO,UAAU,QAAQ,EAAE;AACxC,QAAM,QAAc,SAAe,SAAS,IAAI,CAAC;AAGjD,SAAO,YAAY,iBAAiB,EAAE;AAEtC,SAAO,CAAC,OAAO,EAAE;AACnB;AAWM,SAAU,kBAEd,EACA,iBACA,YACA,OAAM,GAOP;AACC,QAAM,qBAA0C,CAAA;AAChD,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,uBAAmB,KACjB,iBAAiB;MACf;MACA,WAAW,WAAW,CAAC;MACvB,OAAO,OAAO,CAAC;KAChB,CAAC;EAEN;AACA,SAAO;AACT;AAQM,SAAU,iBAEd,EACA,kBAAkB,OAClB,WAAW,YACX,MAAK,GAON;AACC,QAAM,YAAY;AAElB,QAAM,kBAAkB,mBAAmB,UAAU,IAAI;AACzD,MAAI,iBAAiB;AACnB,UAAM,CAAC,QAAQ,IAAI,IAAI;AACvB,WAAO,YAAY,OAAO;MACxB;MACA;MACA,WAAW;QACT,GAAG;QACH;;KAEH;EACH;AACA,MAAI,UAAU,SAAS,SAAS;AAC9B,WAAO,YAAY,OAA2B;MAC5C;MACA;KACD;EACH;AACA,MAAI,UAAU,SAAS,WAAW;AAChC,WAAO,cAAc,OAA6B;MAChD,UAAU;KACX;EACH;AACA,MAAI,UAAU,SAAS,QAAQ;AAC7B,WAAO,cAAc,KAA2B;EAClD;AACA,MAAI,UAAU,KAAK,WAAW,MAAM,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG;AACzE,UAAM,SAAS,UAAU,KAAK,WAAW,KAAK;AAC9C,UAAM,CAAC,EAAC,EAAGA,QAAO,KAAK,IAAI,aAAa,KAAK,UAAU,IAAI,KAAK,CAAA;AAChE,WAAO,aAAa,OAA4B;MAC9C;MACA,MAAM,OAAOA,KAAI;KAClB;EACH;AACA,MAAI,UAAU,KAAK,WAAW,OAAO,GAAG;AACtC,WAAO,YAAY,OAA6B,EAAE,MAAM,UAAU,KAAI,CAAE;EAC1E;AACA,MAAI,UAAU,SAAS,UAAU;AAC/B,WAAO,aAAa,KAA0B;EAChD;AACA,QAAM,IAAkB,iBAAiB,UAAU,IAAI;AACzD;AAgBM,SAAU,OAAO,oBAAuC;AAE5D,MAAI,aAAa;AACjB,WAAS,IAAI,GAAG,IAAI,mBAAmB,QAAQ,KAAK;AAClD,UAAM,EAAE,SAAS,QAAO,IAAK,mBAAmB,CAAC;AACjD,QAAI;AAAS,oBAAc;;AACtB,oBAAkBA,MAAK,OAAO;EACrC;AAGA,QAAM,mBAA8B,CAAA;AACpC,QAAM,oBAA+B,CAAA;AACrC,MAAI,cAAc;AAClB,WAAS,IAAI,GAAG,IAAI,mBAAmB,QAAQ,KAAK;AAClD,UAAM,EAAE,SAAS,QAAO,IAAK,mBAAmB,CAAC;AACjD,QAAI,SAAS;AACX,uBAAiB,KACX,WAAW,aAAa,aAAa,EAAE,MAAM,GAAE,CAAE,CAAC;AAExD,wBAAkB,KAAK,OAAO;AAC9B,qBAAmBA,MAAK,OAAO;IACjC,OAAO;AACL,uBAAiB,KAAK,OAAO;IAC/B;EACF;AAGA,SAAW,OAAO,GAAG,kBAAkB,GAAG,iBAAiB;AAC7D;AAYM,SAAU,cACd,OACA,SAA8B;AAE9B,QAAM,EAAE,UAAAJ,YAAW,MAAK,IAAK;AAC7B,EAAQ,OAAO,OAAO,EAAE,QAAQA,UAAQ,CAAE;AAC1C,SAAO;IACL,SAAS;IACT,SAAa,QAAQ,MAAM,YAAW,CAAa;;AAEvD;AAWM,SAAU,YACd,OACA,SAIC;AAED,QAAM,EAAE,iBAAiB,QAAQ,UAAS,IAAK;AAE/C,QAAM,UAAU,WAAW;AAE3B,MAAI,CAAC,MAAM,QAAQ,KAAK;AAAG,UAAM,IAAkB,kBAAkB,KAAK;AAC1E,MAAI,CAAC,WAAW,MAAM,WAAW;AAC/B,UAAM,IAAkB,yBAAyB;MAC/C,gBAAgB;MAChB,aAAa,MAAM;MACnB,MAAM,GAAG,UAAU,IAAI,IAAI,MAAM;KAClC;AAEH,MAAI,eAAe;AACnB,QAAM,qBAA0C,CAAA;AAChD,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,UAAM,gBAAgB,iBAAiB;MACrC;MACA;MACA,OAAO,MAAM,CAAC;KACf;AACD,QAAI,cAAc;AAAS,qBAAe;AAC1C,uBAAmB,KAAK,aAAa;EACvC;AAEA,MAAI,WAAW,cAAc;AAC3B,UAAM,OAAO,OAAO,kBAAkB;AACtC,QAAI,SAAS;AACX,YAAMC,UAAa,WAAW,mBAAmB,QAAQ,EAAE,MAAM,GAAE,CAAE;AACrE,aAAO;QACL,SAAS;QACT,SACE,mBAAmB,SAAS,IAAQ,OAAOA,SAAQ,IAAI,IAAIA;;IAEjE;AACA,QAAI;AAAc,aAAO,EAAE,SAAS,MAAM,SAAS,KAAI;EACzD;AACA,SAAO;IACL,SAAS;IACT,SAAa,OAAO,GAAG,mBAAmB,IAAI,CAAC,EAAE,QAAO,MAAO,OAAO,CAAC;;AAE3E;AAaM,SAAU,YACd,OACA,EAAE,KAAI,GAAoB;AAE1B,QAAM,CAAC,EAAE,aAAa,IAAI,KAAK,MAAM,OAAO;AAC5C,QAAM,YAAgBG,MAAK,KAAK;AAChC,MAAI,CAAC,eAAe;AAClB,QAAI,SAAS;AAGb,QAAI,YAAY,OAAO;AACrB,eAAa,SAAS,QAAQ,KAAK,MAAM,MAAM,SAAS,KAAK,IAAI,EAAE,IAAI,EAAE;AAC3E,WAAO;MACL,SAAS;MACT,SAAa,OACP,QAAY,WAAW,WAAW,EAAE,MAAM,GAAE,CAAE,CAAC,GACnD,MAAM;;EAGZ;AACA,MAAI,cAAc,OAAO,SAAS,aAAa;AAC7C,UAAM,IAAkB,uBAAuB;MAC7C,cAAc,OAAO,SAAS,aAAa;MAC3C;KACD;AACH,SAAO,EAAE,SAAS,OAAO,SAAa,SAAS,KAAK,EAAC;AACvD;AAaM,SAAU,cAAc,OAAc;AAC1C,MAAI,OAAO,UAAU;AACnB,UAAM,IAAW,UACf,2BAA2B,KAAK,YAAY,OAAO,KAAK,qCAAqC;AAEjG,SAAO,EAAE,SAAS,OAAO,SAAa,QAAY,YAAY,KAAK,CAAC,EAAC;AACvE;AAWM,SAAU,aACd,OACA,EAAE,QAAQ,MAAAA,MAAI,GAAqC;AAEnD,MAAI,OAAOA,UAAS,UAAU;AAC5B,UAAM,MAAM,OAAO,OAAOA,KAAI,KAAK,SAAS,KAAK,OAAO;AACxD,UAAM,MAAM,SAAS,CAAC,MAAM,KAAK;AACjC,QAAI,QAAQ,OAAO,QAAQ;AACzB,YAAM,IAAQ,uBAAuB;QACnC,KAAK,IAAI,SAAQ;QACjB,KAAK,IAAI,SAAQ;QACjB;QACA,MAAMA,QAAO;QACb,OAAO,MAAM,SAAQ;OACtB;EACL;AACA,SAAO;IACL,SAAS;IACT,SAAa,WAAW,OAAO;MAC7B,MAAM;MACN;KACD;;AAEL;AAQM,SAAU,aAAa,OAAa;AACxC,QAAM,WAAeC,YAAW,KAAK;AACrC,QAAM,cAAc,KAAK,KAASD,MAAK,QAAQ,IAAI,EAAE;AACrD,QAAM,QAAmB,CAAA;AACzB,WAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,UAAM,KAAS,SAAaE,OAAM,UAAU,IAAI,KAAK,IAAI,KAAK,EAAE,CAAC,CAAC;EACpE;AACA,SAAO;IACL,SAAS;IACT,SAAa,OACP,SAAa,WAAeF,MAAK,QAAQ,GAAG,EAAE,MAAM,GAAE,CAAE,CAAC,GAC7D,GAAG,KAAK;;AAGd;AAaM,SAAU,YAKd,OACA,SAGC;AAED,QAAM,EAAE,iBAAiB,UAAS,IAAK;AAEvC,MAAI,UAAU;AACd,QAAM,qBAA0C,CAAA;AAChD,WAAS,IAAI,GAAG,IAAI,UAAU,WAAW,QAAQ,KAAK;AACpD,UAAM,SAAS,UAAU,WAAW,CAAC;AACrC,UAAM,QAAQ,MAAM,QAAQ,KAAK,IAAI,IAAI,OAAO;AAChD,UAAM,gBAAgB,iBAAiB;MACrC;MACA,WAAW;MACX,OAAQ,MAAc,KAAM;KAC7B;AACD,uBAAmB,KAAK,aAAa;AACrC,QAAI,cAAc;AAAS,gBAAU;EACvC;AACA,SAAO;IACL;IACA,SAAS,UACL,OAAO,kBAAkB,IACrB,OAAO,GAAG,mBAAmB,IAAI,CAAC,EAAE,QAAO,MAAO,OAAO,CAAC;;AAEtE;AAQM,SAAU,mBACd,MAAY;AAEZ,QAAM,UAAU,KAAK,MAAM,kBAAkB;AAC7C,SAAO;;IAEH,CAAC,QAAQ,CAAC,IAAK,OAAO,QAAQ,CAAC,CAAE,IAAI,MAAM,QAAQ,CAAC,CAAE;MACtD;AACN;AAGM,SAAU,gBAAgB,OAA8B;AAjxB9D;AAkxBE,QAAM,EAAE,KAAI,IAAK;AACjB,MAAI,SAAS;AAAU,WAAO;AAC9B,MAAI,SAAS;AAAS,WAAO;AAC7B,MAAI,KAAK,SAAS,IAAI;AAAG,WAAO;AAEhC,MAAI,SAAS;AAAS,YAAQ,WAAc,eAAd,mBAA0B,KAAK;AAE7D,QAAM,kBAAkB,mBAAmB,MAAM,IAAI;AACrD,MACE,mBACA,gBAAgB;IACd,GAAG;IACH,MAAM,gBAAgB,CAAC;GACG;AAE5B,WAAO;AAET,SAAO;AACT;;;ADluBM,SAAU,OACd,YACA,MACA,UAGI,CAAA,GAAE;AAEN,QAAM,EAAE,KAAK,SAAS,kBAAkB,MAAK,IAAK;AAElD,QAAM,QAAQ,OAAO,SAAS,WAAiB,QAAQ,IAAI,IAAI;AAC/D,QAAM,SAAgB,OAAO,KAAK;AAElC,MAAU,KAAK,KAAK,MAAM,KAAK,WAAW,SAAS;AACjD,UAAM,IAAI,cAAa;AACzB,MAAU,KAAK,KAAK,KAAW,KAAK,KAAK,IAAI;AAC3C,UAAM,IAAI,sBAAsB;MAC9B,MAAM,OAAO,SAAS,WAAW,OAAW,UAAU,IAAI;MAC1D;MACA,MAAY,KAAK,KAAK;KACvB;AAEH,MAAI,WAAW;AACf,QAAM,SAAc,OAAO,UAAU,CAAA,IAAK,CAAA;AAC1C,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE,GAAG;AAC1C,UAAM,QAAQ,WAAW,CAAC;AAC1B,WAAO,YAAY,QAAQ;AAC3B,UAAM,CAACG,OAAM,SAAS,IAAa,gBAAgB,QAAQ,OAAO;MAChE;MACA,gBAAgB;KACjB;AACD,gBAAY;AACZ,QAAI,OAAO;AAAS,aAAO,KAAKA,KAAI;;AAC/B,aAAO,MAAM,QAAQ,CAAC,IAAIA;EACjC;AACA,SAAO;AACT;AAwEM,SAAUC,QAGd,YACA,QAGA,SAAwB;AAExB,QAAM,EAAE,kBAAkB,MAAK,IAAK,WAAW,CAAA;AAE/C,MAAI,WAAW,WAAW,OAAO;AAC/B,UAAM,IAAI,oBAAoB;MAC5B,gBAAgB,WAAW;MAC3B,aAAa,OAAO;KACrB;AAEH,QAAM,qBAA8B,kBAAkB;IACpD;IACA;IACA;GACD;AACD,QAAM,OAAgB,OAAO,kBAAkB;AAC/C,MAAI,KAAK,WAAW;AAAG,WAAO;AAC9B,SAAO;AACT;AAqCM,SAAU,aAEd,OAAuB,QAA2C;AAClE,MAAI,MAAM,WAAW,OAAO;AAC1B,UAAM,IAAI,oBAAoB;MAC5B,gBAAgB,MAAM;MACtB,aAAa,OAAO;KACrB;AAEH,QAAM,OAAkB,CAAA;AACxB,WAAS,IAAI,GAAG,IAAK,MAAoB,QAAQ,KAAK;AACpD,UAAM,OAAO,MAAM,CAAC;AACpB,UAAM,QAAQ,OAAO,CAAC;AACtB,SAAK,KAAK,aAAa,OAAO,MAAM,KAAK,CAAC;EAC5C;AACA,SAAW,OAAO,GAAG,IAAI;AAC3B;CAEA,SAAiBC,eAAY;AAe3B,WAAgBD,QACd,MACA,OACA,UAAU,OAAK;AAEf,QAAI,SAAS,WAAW;AACtB,YAAM,UAAU;AAChB,MAAQ,OAAO,OAAO;AACtB,aAAW,QACT,QAAQ,YAAW,GACnB,UAAU,KAAK,CAAC;IAEpB;AACA,QAAI,SAAS;AAAU,aAAWE,YAAW,KAAe;AAC5D,QAAI,SAAS;AAAS,aAAO;AAC7B,QAAI,SAAS;AACX,aAAW,QAAY,YAAY,KAAgB,GAAG,UAAU,KAAK,CAAC;AAExE,UAAM,WAAY,KAAgB,MAAe,YAAY;AAC7D,QAAI,UAAU;AACZ,YAAM,CAAC,OAAO,UAAU,OAAO,KAAK,IAAI;AACxC,YAAMC,QAAO,OAAO,SAAS,IAAI,IAAI;AACrC,aAAW,WAAW,OAAiB;QACrC,MAAM,UAAU,KAAKA;QACrB,QAAQ,aAAa;OACtB;IACH;AAEA,UAAM,aAAc,KAAgB,MAAe,UAAU;AAC7D,QAAI,YAAY;AACd,YAAM,CAAC,OAAOA,KAAI,IAAI;AACtB,UAAI,OAAO,SAASA,KAAK,OAAQ,MAAkB,SAAS,KAAK;AAC/D,cAAM,IAAI,uBAAuB;UAC/B,cAAc,OAAO,SAASA,KAAK;UACnC;SACD;AACH,aAAW,SAAS,OAAkB,UAAU,KAAK,CAAC;IACxD;AAEA,UAAM,aAAc,KAAgB,MAAe,UAAU;AAC7D,QAAI,cAAc,MAAM,QAAQ,KAAK,GAAG;AACtC,YAAM,CAAC,OAAO,SAAS,IAAI;AAC3B,YAAM,OAAkB,CAAA;AACxB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,aAAK,KAAKH,QAAO,WAAW,MAAM,CAAC,GAAG,IAAI,CAAC;MAC7C;AACA,UAAI,KAAK,WAAW;AAAG,eAAO;AAC9B,aAAW,OAAO,GAAG,IAAI;IAC3B;AAEA,UAAM,IAAI,iBAAiB,IAAc;EAC3C;AAnDgB,EAAAC,cAAA,SAAMD;AAoDxB,GAnEiB,iBAAA,eAAY,CAAA,EAAA;AAgGvB,SAAU,OAMd,YAKK;AAEL,SAAe,oBAAoB,UAAU;AAC/C;AA0FM,SAAU,KAGd,YAAmE;AAEnE,MAAI,MAAM,QAAQ,UAAU,KAAK,OAAO,WAAW,CAAC,MAAM;AACxD,WAAe,mBAAmB,UAAU;AAC9C,MAAI,OAAO,eAAe;AACxB,WAAe,mBAAmB,UAAU;AAC9C,SAAO;AACT;AAuCM,IAAO,wBAAP,cAA4C,UAAS;EAEzD,YAAY,EACV,MACA,YACA,MAAAG,MAAI,GAC8D;AAClE,UAAM,gBAAgBA,KAAI,6CAA6C;MACrE,cAAc;QACZ,YAAoB,oBAAoB,UAAkC,CAAC;QAC3E,WAAW,IAAI,KAAKA,KAAI;;KAE3B;AAXe,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAYzB;;AA4BI,IAAO,gBAAP,cAAoC,UAAS;EAEjD,cAAA;AACE,UAAM,qDAAqD;AAF3C,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAGzB;;AA6BI,IAAO,2BAAP,cAA+C,UAAS;EAE5D,YAAY,EACV,gBACA,aACA,KAAI,GAC0D;AAC9D,UACE,oCAAoC,IAAI,mBAAmB,cAAc,gBAAgB,WAAW,KAAK;AAP3F,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EASzB;;AA6BI,IAAO,yBAAP,cAA6C,UAAS;EAE1D,YAAY,EACV,cACA,MAAK,GACoC;AACzC,UACE,kBAAkB,KAAK,WAAeA,MACpC,KAAK,CACN,wCAAwC,YAAY,IAAI;AAR3C,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAUzB;;AA0BI,IAAO,sBAAP,cAA0C,UAAS;EAEvD,YAAY,EACV,gBACA,YAAW,GACqC;AAChD,UACE;MACE;MACA,iCAAiC,cAAc;MAC/C,0BAA0B,WAAW;MACrC,KAAK,IAAI,CAAC;AAVE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAYzB;;AAmBI,IAAO,oBAAP,cAAwC,UAAS;EAErD,YAAY,OAAc;AACxB,UAAM,WAAW,KAAK,0BAA0B;AAFhC,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAGzB;;AAeI,IAAO,mBAAP,cAAuC,UAAS;EAEpD,YAAY,MAAY;AACtB,UAAM,UAAU,IAAI,6BAA6B;AAFjC,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAGzB;;;;ADrmBI,SAAUC,QAGd,OAA2C;AAC3C,QAAM,EAAE,QAAQ,SAAS,aAAa,MAAK,IACzC;AAEF,QAAM,eAAe,CACnB,QACA,SACE;AACF,eAAW,SAAS,QAAQ;AAC1B,YAAM,EAAE,MAAM,KAAI,IAAK;AACvB,YAAMC,SAAQ,KAAK,IAAI;AAEvB,YAAM,eAAe,KAAK,MAAe,YAAY;AACrD,UACE,iBACC,OAAOA,WAAU,YAAY,OAAOA,WAAU,WAC/C;AACA,cAAM,CAAC,EAAE,MAAM,KAAK,IAAI;AAGxB,QAAI,WAAWA,QAAO;UACpB,QAAQ,SAAS;UACjB,MAAM,OAAO,SAAS,SAAS,EAAE,IAAI;SACtC;MACH;AAEA,UACE,SAAS,aACT,OAAOA,WAAU,YACjB,CAAS,SAASA,MAAK;AAEvB,cAAM,IAAY,oBAAoB;UACpC,SAASA;UACT,OAAO,IAAY,kBAAiB;SACrC;AAEH,YAAM,aAAa,KAAK,MAAe,UAAU;AACjD,UAAI,YAAY;AACd,cAAM,CAAC,EAAEC,KAAI,IAAI;AACjB,YAAIA,SAAYA,MAAKD,MAAgB,MAAM,OAAO,SAASC,KAAI;AAC7D,gBAAM,IAAIC,wBAAuB;YAC/B,cAAc,OAAO,SAASD,KAAI;YAClC,WAAeA,MAAKD,MAAgB;WACrC;MACL;AAEA,YAAMG,UAAS,MAAM,IAAI;AACzB,UAAIA,SAAQ;AACV,0BAAkB,IAAI;AACtB,qBAAaA,SAAQH,MAAgC;MACvD;IACF;EACF;AAGA,MAAI,MAAM,gBAAgB,QAAQ;AAChC,QAAI,OAAO,WAAW;AAAU,YAAM,IAAI,mBAAmB,EAAE,OAAM,CAAE;AACvE,iBAAa,MAAM,cAAc,MAAM;EACzC;AAGA,MAAI,gBAAgB,gBAAgB;AAClC,QAAI,MAAM,WAAW;AAAG,mBAAa,MAAM,WAAW,GAAG,OAAO;;AAC3D,YAAM,IAAI,wBAAwB,EAAE,aAAa,MAAK,CAAE;EAC/D;AACF;AAoCM,SAAU,gBAAgB,QAAc;AAC5C,SAAO,WAAW;IAChB;GACD;AACH;AAqDM,SAAUI,QAGd,OAA2C;AAC3C,QAAM,EAAE,SAAS,CAAA,GAAI,SAAS,YAAW,IAAK;AAE9C,QAAM,QAAQ;IACZ,cAAc,yBAAyB,MAAM;IAC7C,GAAG,MAAM;;AAKX,EAAAL,QAAO;IACL;IACA;IACA;IACA;GACD;AAGD,QAAM,QAAmB,CAAC,QAAQ,MAAM;AACxC,MAAI;AACF,UAAM,KACJ,WAAW;MACT;MACA;KACD,CAAC;AAEN,MAAI,gBAAgB;AAClB,UAAM,KACJ,WAAW;MACT,MAAM;MACN;MACA;KACD,CAAC;AAGN,SAAW,OAAO,GAAG,KAAK;AAC5B;AAuCM,SAAU,WAAW,OAAuB;AAChD,QAAM,EAAE,aAAa,MAAK,IAAK;AAE/B,MAAI,SAAS;AACb,QAAM,eAAe,qBAAqB,EAAE,aAAa,MAAK,CAAE;AAChE,eAAa,OAAO,WAAW;AAE/B,QAAM,OAAO,CAAC,aAAa,GAAG,MAAM,KAAK,YAAY,EAAE,KAAI,CAAE;AAC7D,aAAW,QAAQ,MAAM;AACvB,cAAU,GAAG,IAAI,KAAK,MAAM,IAAI,KAAK,CAAA,GAClC,IAAI,CAAC,EAAE,MAAM,MAAM,EAAC,MAAO,GAAG,CAAC,IAAI,IAAI,EAAE,EACzC,KAAK,GAAG,CAAC;EACd;AAEA,SAAO;AACT;AAmCM,SAAU,yBACd,QAA0B;AAE1B,SAAO;IACL,QAAO,iCAAQ,UAAS,YAAY,EAAE,MAAM,QAAQ,MAAM,SAAQ;KAClE,iCAAQ,YAAW,EAAE,MAAM,WAAW,MAAM,SAAQ;IACpD,QAAO,iCAAQ,aAAY,YAAY;MACrC,MAAM;MACN,MAAM;;KAER,iCAAQ,sBAAqB;MAC3B,MAAM;MACN,MAAM;;KAER,iCAAQ,SAAQ,EAAE,MAAM,QAAQ,MAAM,UAAS;IAC/C,OAAO,OAAO;AAClB;AAmDM,SAAU,eAGd,OAA2C;AAC3C,SAAY,UAAUK,QAAO,KAAK,CAAC;AACrC;AA8BM,SAAU,WAAW,OAAuB;AAChD,QAAM,EAAE,QAAQ,MAAK,IAAK;AAC1B,SAAO,WAAW;IAChB,MAAM;IACN,aAAa;IACb,OAAO;MACL,GAAG;MACH,eAAc,+BAAO,iBAAgB,yBAAyB,MAAM;;GAEvE;AACH;AA8CM,SAAU,WAAW,OAAuB;AAChD,QAAM,EAAE,MAAM,aAAa,MAAK,IAAK;AACrC,QAAM,UAAU,WAAW;IACzB;IACA;IACA;GACD;AACD,SAAY,UAAU,OAAO;AAC/B;AAoDM,SAAU,UAGd,OAA8C;AAC9C,QAAM,EACJ,QAAQ,SACR,SAAS,UACT,aACA,MAAK,IACH;AAEJ,QAAM,gBAAgB,CACpB,QACAJ,WACE;AACF,UAAM,OAAO,EAAE,GAAGA,OAAK;AACvB,eAAW,SAAS,QAAQ;AAC1B,YAAM,EAAE,MAAM,KAAI,IAAK;AACvB,UAAI,SAAS;AAAW,aAAK,IAAI,IAAK,KAAK,IAAI,EAAa,YAAW;IACzE;AACA,WAAO;EACT;AAEA,QAAM,UAAU,MAAK;AACnB,QAAI,CAAC;AAAS,aAAO,CAAA;AACrB,UAAM,OAAO,MAAM,gBAAgB,yBAAyB,OAAO;AACnE,WAAO,cAAc,MAAM,OAAO;EACpC,GAAE;AAEF,QAAM,WAAW,MAAK;AACpB,QAAI,gBAAgB;AAAgB,aAAO;AAC3C,QAAI,CAAC,MAAM,WAAW;AAAG,aAAO,CAAA;AAChC,WAAO,cAAc,MAAM,WAAW,GAAG,QAAQ;EACnD,GAAE;AAEF,SAAY,UAAU,EAAE,QAAQ,SAAS,aAAa,MAAK,GAAI,CAAC,GAAGA,WAAS;AAC1E,QAAI,OAAOA,WAAU;AAAU,aAAOA,OAAM,SAAQ;AACpD,WAAOA;EACT,CAAC;AACH;AA4CM,SAAUK,UAGd,OAA2C;AAC3C,MAAI;AACF,IAAAN,QAAO,KAAK;AACZ,WAAO;EACT,QAAQ;AACN,WAAO;EACT;AACF;AAOM,IAAOG,0BAAP,cAA6C,UAAS;EAG1D,YAAY,EACV,cACA,UAAS,GACmC;AAC5C,UAAM,iBAAiB,YAAY,cAAc,SAAS,GAAG;AAN7C,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAOzB;;AAII,IAAO,qBAAP,cAAyC,UAAS;EAGtD,YAAY,EAAE,OAAM,GAAuB;AACzC,UAAM,mBAAwB,UAAU,MAAM,CAAC,MAAM;MACnD,cAAc,CAAC,iCAAiC;KACjD;AALe,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMzB;;AAII,IAAO,0BAAP,cAA8C,UAAS;EAG3D,YAAY,EACV,aACA,MAAK,GAC+D;AACpE,UACE,0BAA0B,WAAW,uBAAuB,KAAK,UAAU,OAAO,KAAK,KAAK,CAAC,CAAC,OAC9F;MACE,cAAc,CAAC,kDAAkD;KAClE;AAVa,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAYzB;;AAII,IAAO,yBAAP,cAA6C,UAAS;EAG1D,YAAY,EAAE,KAAI,GAAoB;AACpC,UAAM,gBAAgB,IAAI,iBAAiB;MACzC,cAAc,CAAC,0CAA0C;KAC1D;AALe,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAMzB;;AAII,SAAU,WAAW,OAI1B;AACC,QAAM,EAAE,MAAM,aAAa,MAAK,IAAK;AACrC,QAAM,eAA0C,CAAC,EAAE,MAAM,UAAS,CAAE;AACpE,QAAM,gBAA2B,CAAC,SAAS,EAAE,aAAa,MAAK,CAAE,CAAC;AAElE,aAAW,SAAS,MAAM,WAAW,KAAK,CAAA,GAAI;AAC5C,UAAM,CAAC,MAAMF,MAAK,IAAI,YAAY;MAChC;MACA,MAAM,MAAM;MACZ,MAAM,MAAM;MACZ,OAAO,KAAK,MAAM,IAAI;KACvB;AACD,iBAAa,KAAK,IAAI;AACtB,kBAAc,KAAKA,MAAK;EAC1B;AAEA,SAAqBI,QAAO,cAAc,aAAa;AACzD;AAYM,SAAU,SAAS,OAGxB;AACC,QAAM,EAAE,aAAa,MAAK,IAAK;AAC/B,QAAM,kBAAsBE,YAAW,WAAW,EAAE,aAAa,MAAK,CAAE,CAAC;AACzE,SAAY,UAAU,eAAe;AACvC;AAYM,SAAU,YAAY,YAK3B;AACC,MAAI,EAAE,OAAO,MAAM,MAAM,MAAK,IAAK;AAEnC,MAAI,MAAM,IAAI,MAAM;AAClB,WAAO;MACL,EAAE,MAAM,UAAS;MACZ,UAAU,WAAW,EAAE,MAAM,OAAO,aAAa,MAAM,MAAK,CAAE,CAAC;;AAGxE,MAAI,SAAS,SAAS;AACpB,UAAM,UAAU,MAAM,SAAS,IAAI,MAAM;AACzC,YAAQ,KAAK,UAAU,MAAM,MAAM,CAAC,CAAC;AACrC,WAAO,CAAC,EAAE,MAAM,UAAS,GAAS,UAAU,OAAO,EAAE,IAAI,MAAK,CAAE,CAAC;EACnE;AAEA,MAAI,SAAS;AACX,WAAO;MACL,EAAE,MAAM,UAAS;MACZ,UAAgB,WAAW,KAAK,GAAG,EAAE,IAAI,MAAK,CAAE;;AAGzD,MAAI,KAAK,YAAY,GAAG,MAAM,KAAK,SAAS,GAAG;AAC7C,UAAM,aAAa,KAAK,MAAM,GAAG,KAAK,YAAY,GAAG,CAAC;AACtD,UAAM,iBAAkB,MAA2C,IACjE,CAAC,SACC,YAAY;MACV;MACA,MAAM;MACN;MACA,OAAO;KACR,CAAC;AAEN,WAAO;MACL,EAAE,MAAM,UAAS;MACZ,UACWF,QACZ,eAAe,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,GAC7B,eAAe,IAAI,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CACjC;;EAGP;AAEA,SAAO,CAAC,EAAE,KAAI,GAAI,KAAK;AACzB;AAYM,SAAU,qBACd,OAIA,UAAuB,oBAAI,IAAG,GAAE;AAEhC,QAAM,EAAE,aAAa,cAAc,MAAK,IAAK;AAC7C,QAAM,QAAQ,aAAa,MAAM,OAAO;AACxC,QAAM,cAAc,+BAAQ;AAC5B,MAAI,QAAQ,IAAI,WAAW,KAAK,MAAM,WAAW,MAAM;AACrD,WAAO;AAET,UAAQ,IAAI,WAAW;AAEvB,aAAW,SAAS,MAAM,WAAW;AACnC,yBAAqB,EAAE,aAAa,MAAM,MAAM,MAAK,GAAI,OAAO;AAClE,SAAO;AACT;AAQA,SAAS,kBAAkB,MAAY;AAErC,MACE,SAAS,aACT,SAAS,UACT,SAAS,YACT,KAAK,WAAW,OAAO,KACvB,KAAK,WAAW,MAAM,KACtB,KAAK,WAAW,KAAK;AAErB,UAAM,IAAI,uBAAuB,EAAE,KAAI,CAAE;AAC7C;", "names": ["BytesSizeMismatchError", "assert", "encode", "validate", "encode", "checksum", "length", "consumed", "value", "size", "fromString", "slice", "data", "encode", "encodePacked", "fromString", "size", "assert", "value", "size", "BytesSizeMismatchError", "struct", "encode", "validate", "fromString"]}