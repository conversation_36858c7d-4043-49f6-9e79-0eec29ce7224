import {
  from as from3,
  from<PERSON>ub<PERSON><PERSON><PERSON>,
  isEqual,
  keccak256,
  toBytes
} from "./chunk-PZ3FUNDW.js";
import {
  concat2 as concat,
  from,
  from2,
  fromBytes,
  fromString2 as fromString,
  size2 as size
} from "./chunk-UG7W3O5D.js";
import {
  secp256k1
} from "./chunk-AIY5L6NH.js";
import {
  __export
} from "./chunk-OS7ZSSJM.js";

// node_modules/ox/_esm/core/Secp256k1.js
var Secp256k1_exports = {};
__export(Secp256k1_exports, {
  getPublicKey: () => getPublicKey,
  noble: () => noble,
  randomPrivateKey: () => randomPrivateKey,
  recoverAddress: () => recoverAddress,
  recoverPublicKey: () => recoverPublicKey,
  sign: () => sign,
  verify: () => verify
});

// node_modules/ox/_esm/core/internal/entropy.js
var extraEntropy = false;

// node_modules/ox/_esm/core/Secp256k1.js
var noble = secp256k1;
function getPublicKey(options) {
  const { privateKey } = options;
  const point = secp256k1.ProjectivePoint.fromPrivateKey(from2(privateKey).slice(2));
  return from3(point);
}
function randomPrivateKey(options = {}) {
  const { as = "Hex" } = options;
  const bytes = secp256k1.utils.randomPrivateKey();
  if (as === "Hex")
    return fromBytes(bytes);
  return bytes;
}
function recoverAddress(options) {
  return fromPublicKey(recoverPublicKey(options));
}
function recoverPublicKey(options) {
  const { payload, signature } = options;
  const { r, s, yParity } = signature;
  const signature_ = new secp256k1.Signature(BigInt(r), BigInt(s)).addRecoveryBit(yParity);
  const point = signature_.recoverPublicKey(from2(payload).substring(2));
  return from3(point);
}
function sign(options) {
  const { extraEntropy: extraEntropy2 = extraEntropy, hash, payload, privateKey } = options;
  const { r, s, recovery } = secp256k1.sign(from(payload), from(privateKey), {
    extraEntropy: typeof extraEntropy2 === "boolean" ? extraEntropy2 : from2(extraEntropy2).slice(2),
    lowS: true,
    ...hash ? { prehash: true } : {}
  });
  return {
    r,
    s,
    yParity: recovery
  };
}
function verify(options) {
  const { address, hash, payload, publicKey, signature } = options;
  if (address)
    return isEqual(address, recoverAddress({ payload, signature }));
  return secp256k1.verify(signature, from(payload), toBytes(publicKey), ...hash ? [{ prehash: true, lowS: true }] : []);
}

// node_modules/ox/_esm/core/PersonalMessage.js
var PersonalMessage_exports = {};
__export(PersonalMessage_exports, {
  encode: () => encode,
  getSignPayload: () => getSignPayload
});
function encode(data) {
  const message = from2(data);
  return concat(
    // Personal Sign Format: `0x19 ‖ "Ethereum Signed Message:\n" ‖ message.length ‖ message`
    "0x19",
    fromString("Ethereum Signed Message:\n" + size(message)),
    message
  );
}
function getSignPayload(data) {
  return keccak256(encode(data));
}

export {
  extraEntropy,
  getPublicKey,
  randomPrivateKey,
  sign,
  Secp256k1_exports,
  getSignPayload,
  PersonalMessage_exports
};
//# sourceMappingURL=chunk-X66T4DR5.js.map
