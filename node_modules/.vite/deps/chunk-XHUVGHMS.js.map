{"version": 3, "sources": ["../../thirdweb/src/utils/encoding/to-bytes.ts"], "sourcesContent": ["import * as ox__Bytes from \"ox/Bytes\";\nimport { isHex } from \"./helpers/is-hex.js\";\nimport type { Hex } from \"./hex.js\";\nimport type { NumberToHexOpts } from \"./hex.js\";\n\nexport type ToBytesParameters = {\n  /** Size of the output bytes. */\n  size?: number;\n};\n\n/**\n * Converts a value to an array of bytes.\n * @param value - The value to convert.\n * @param opts - Optional parameters for the conversion.\n * @returns The array of bytes representing the value.\n * @example\n * ```ts\n * import { toBytes } from \"thirdweb/utils\";\n * const bytes = toBytes(\"0x1a4\");\n * console.log(bytes); // Uint8Array(2) [ 1, 164 ]\n * ```\n * @utils\n */\nexport function toBytes(\n  value: string | bigint | number | boolean | Hex,\n  opts: ToBytesParameters = {},\n): Uint8Array {\n  switch (typeof value) {\n    case \"number\":\n    case \"bigint\":\n      return numberToBytes(value, opts);\n    case \"boolean\":\n      return boolToBytes(value, opts);\n    default:\n      if (isHex(value)) {\n        return hexToBytes(value, opts);\n      }\n      return stringToBytes(value, opts);\n  }\n}\n\nexport type BoolToBytesOpts = ox__Bytes.fromBoolean.Options;\n\n/**\n * Converts a boolean value to a Uint8Array of bytes.\n * @param value - The boolean value to convert.\n * @param opts - Optional parameters for the conversion.\n * @returns The Uint8Array of bytes representing the boolean value.\n * @example\n * ```ts\n * import { boolToBytes } from \"thirdweb/utils\";\n * const bytes = boolToBytes(true);\n * console.log(bytes); // Uint8Array(1) [ 1 ]\n * ```\n * @utils\n */\nexport function boolToBytes(value: boolean, opts: BoolToBytesOpts = {}) {\n  return ox__Bytes.fromBoolean(value, opts);\n}\n\nexport type HexToBytesOpts = ox__Bytes.fromHex.Options;\n\n/**\n * Converts a hexadecimal string to a Uint8Array of bytes.\n * @param hex_ The hexadecimal string to convert.\n * @param opts Options for converting the hexadecimal string.\n * @returns The Uint8Array of bytes.\n * @throws Error if the byte sequence is invalid.\n * @example\n * ```ts\n * import { hexToBytes } from \"thirdweb/utils\";\n * const bytes = hexToBytes(\"0x1a4\");\n * console.log(bytes); // Uint8Array(2) [ 1, 164 ]\n * ```\n * @utils\n */\nexport function hexToBytes(hex_: Hex, opts: HexToBytesOpts = {}): Uint8Array {\n  return ox__Bytes.fromHex(hex_, opts);\n}\n\n/**\n * Converts a number to bytes.\n * @param value - The number to convert.\n * @param opts - Options for converting the number to hex.\n * @returns The bytes representation of the number.\n * @example\n * ```ts\n * import { numberToBytes } from \"thirdweb/utils\";\n * const bytes = numberToBytes(420);\n * console.log(bytes); // Uint8Array(2) [ 1, 164 ]\n * ```\n * @utils\n */\nexport function numberToBytes(value: bigint | number, opts?: NumberToHexOpts) {\n  return ox__Bytes.fromNumber(value, opts);\n}\n\nexport type StringToBytesOpts = {\n  /** Size of the output bytes. */\n  size?: number;\n};\n\n/**\n * Converts a string to an array of bytes.\n * @param value - The string to convert.\n * @param opts - Optional parameters for the conversion.\n * @returns The array of bytes representing the string.\n * @example\n * ```ts\n * import { stringToBytes } from \"thirdweb/utils\";\n * const bytes = stringToBytes(\"Hello, world!\");\n * console.log(bytes); // Uint8Array(13) [ 72, 101, 108, 108, 111, 44, 32, 119, 111, 114, 108, 100, 33 ]\n * ```\n * @utils\n */\nexport function stringToBytes(\n  value: string,\n  opts: StringToBytesOpts = {},\n): Uint8Array {\n  return ox__Bytes.fromString(value, opts);\n}\n"], "mappings": ";;;;;;;;;;;AAuBM,SAAU,QACd,OACA,OAA0B,CAAA,GAAE;AAE5B,UAAQ,OAAO,OAAO;IACpB,KAAK;IACL,KAAK;AACH,aAAO,cAAc,OAAO,IAAI;IAClC,KAAK;AACH,aAAO,YAAY,OAAO,IAAI;IAChC;AACE,UAAI,MAAM,KAAK,GAAG;AAChB,eAAO,WAAW,OAAO,IAAI;MAC/B;AACA,aAAO,cAAc,OAAO,IAAI;EACpC;AACF;AAiBM,SAAU,YAAY,OAAgB,OAAwB,CAAA,GAAE;AACpE,SAAiB,YAAY,OAAO,IAAI;AAC1C;AAkBM,SAAU,WAAW,MAAW,OAAuB,CAAA,GAAE;AAC7D,SAAiB,QAAQ,MAAM,IAAI;AACrC;AAeM,SAAU,cAAc,OAAwB,MAAsB;AAC1E,SAAiB,WAAW,OAAO,IAAI;AACzC;AAoBM,SAAU,cACd,OACA,OAA0B,CAAA,GAAE;AAE5B,SAAiB,WAAW,OAAO,IAAI;AACzC;", "names": []}