{"version": 3, "sources": ["../../thirdweb/src/wallets/in-app/web/lib/auth/index.ts"], "sourcesContent": ["import type { ThirdwebClient } from \"../../../../../client/client.js\";\nimport type { SocialAuthOption } from \"../../../../../wallets/types.js\";\nimport type {\n  AuthArgsType,\n  GetAuthenticatedUserParams,\n  PreAuthArgsType,\n  SocialAuthArgsType,\n  UnlinkParams,\n} from \"../../../core/authentication/types.js\";\nimport { getOrCreateInAppWalletConnector } from \"../../../core/wallet/in-app-core.js\";\nimport type { Ecosystem } from \"../../../core/wallet/types.js\";\n\n// ---- KEEP IN SYNC WITH /wallets/in-app/native/auth/index.ts ---- //\n// duplication needed for separate exports between web and native\n\n/**\n * @internal\n */\nasync function getInAppWalletConnector(\n  client: ThirdwebClient,\n  ecosystem?: Ecosystem,\n) {\n  return getOrCreateInAppWalletConnector(\n    client,\n    async (client) => {\n      const { InAppWebConnector } = await import(\"../web-connector.js\");\n      return new InAppWebConnector({\n        client: client,\n        ecosystem: ecosystem,\n      });\n    },\n    ecosystem,\n  );\n}\n\n/**\n * Retrieves the authenticated user for the active in-app wallet.\n * @param options - The arguments for retrieving the authenticated user.\n * @returns The authenticated user if logged in and wallet initialized, otherwise undefined.\n * @example\n * ```ts\n * import { getAuthenticatedUser } from \"thirdweb/wallets/in-app\";\n *\n * const user = await getAuthenticatedUser({ client });\n * if (user) {\n *  console.log(user.walletAddress);\n * }\n * ```\n * @wallet\n */\nasync function getAuthenticatedUser(options: GetAuthenticatedUserParams) {\n  const { client, ecosystem } = options;\n  const connector = await getInAppWalletConnector(client, ecosystem);\n  const user = await connector.getUser();\n  switch (user.status) {\n    case \"Logged In, Wallet Initialized\": {\n      return user;\n    }\n  }\n  return undefined;\n}\n\n/**\n * Retrieves the authenticated user email for the active in-app wallet.\n * @param options - The arguments for retrieving the authenticated user.\n * @returns The authenticated user email if logged in and wallet initialized, otherwise undefined.\n * @example\n * ```ts\n * import { getUserEmail } from \"thirdweb/wallets/in-app\";\n *\n * const email = await getUserEmail({ client });\n * console.log(email);\n * ```\n * @wallet\n */\nexport async function getUserEmail(options: GetAuthenticatedUserParams) {\n  const user = await getAuthenticatedUser(options);\n  if (user && \"email\" in user.authDetails) {\n    return user.authDetails.email;\n  }\n  return undefined;\n}\n\n/**\n * Retrieves the authenticated user phone number for the active embedded wallet.\n * @param options - The arguments for retrieving the authenticated user.\n * @returns The authenticated user phone number if authenticated with phone number, otherwise undefined.\n * @example\n * ```ts\n * import { getUserPhoneNumber } from \"thirdweb/wallets/embedded\";\n *\n * const phoneNumber = await getUserPhoneNumber({ client });\n * console.log(phoneNumber);\n * ```\n * @wallet\n */\nexport async function getUserPhoneNumber(options: GetAuthenticatedUserParams) {\n  const user = await getAuthenticatedUser(options);\n  if (user && \"phoneNumber\" in user.authDetails) {\n    return user.authDetails.phoneNumber;\n  }\n  return undefined;\n}\n\n/**\n * Pre-authenticates the user based on the provided authentication strategy.\n * Use this function to send a verification code to the user's email or phone number.\n * @param args - The arguments required for pre-authentication.\n * @returns A promise that resolves to the pre-authentication result.\n * @throws An error if the provided authentication strategy doesn't require pre-authentication.\n * @example\n * ```ts\n * import { preAuthenticate } from \"thirdweb/wallets/in-app\";\n *\n * const result = await preAuthenticate({\n *  client,\n *  strategy: \"email\",\n *  email: \"<EMAIL>\",\n * });\n * ```\n * @wallet\n */\nexport async function preAuthenticate(args: PreAuthArgsType) {\n  const connector = await getInAppWalletConnector(args.client, args.ecosystem);\n  return connector.preAuthenticate(args);\n}\n\n/**\n * Authenticates the user based on the provided authentication arguments.\n * @param args - The authentication arguments.\n * @returns A promise that resolves to the authentication result.\n * @example\n * ```ts\n * import { authenticate } from \"thirdweb/wallets/in-app\";\n *\n * const result = await authenticate({\n *  client,\n *  strategy: \"email\",\n *  email: \"<EMAIL>\",\n *  verificationCode: \"123456\",\n * });\n * ```\n *\n * Authenticate to a backend account (only do this on your backend):\n * ```ts\n * import { authenticate } from \"thirdweb/wallets/in-app\";\n *\n * const result = await authenticate({\n *  client,\n *  strategy: \"backend\",\n *  walletSecret: \"...\", // Provided by your app\n * });\n * ```\n * @wallet\n */\nexport async function authenticate(args: AuthArgsType) {\n  const connector = await getInAppWalletConnector(args.client, args.ecosystem);\n  return connector.authenticate(args);\n}\n\n/**\n * Authenticates the user based on the provided authentication arguments using a redirect.\n * @param args - The authentication arguments.\n * @returns A promise that resolves to the authentication result.\n * @example\n * ```ts\n * import { authenticateWithRedirect } from \"thirdweb/wallets/in-app\";\n *\n * const result = await authenticateWithRedirect({\n *  client,\n *  strategy: \"google\",\n *  mode: \"redirect\",\n *  redirectUrl: \"https://example.org\",\n * });\n * ```\n * @wallet\n */\nexport async function authenticateWithRedirect(\n  args: SocialAuthArgsType & { client: ThirdwebClient; ecosystem?: Ecosystem },\n) {\n  const connector = await getInAppWalletConnector(args.client, args.ecosystem);\n  if (!connector.authenticateWithRedirect) {\n    throw new Error(\n      \"authenticateWithRedirect is not supported on this platform\",\n    );\n  }\n  return connector.authenticateWithRedirect(\n    args.strategy as SocialAuthOption,\n    args.mode,\n    args.redirectUrl,\n  );\n}\n\n/**\n * Connects a new profile (and new authentication method) to the current user.\n *\n * Requires a connected in-app or ecosystem account.\n *\n * **When a profile is linked to the account, that profile can then be used to sign into the same account.**\n *\n * @param auth - The authentications options to add the new profile.\n * @returns A promise that resolves to the currently linked profiles when the connection is successful.\n * @throws If the connection fails, if the profile is already linked to the account, or if the profile is already associated with another account.\n *\n * @example\n * ```ts\n * import { linkProfile } from \"thirdweb/wallets\";\n *\n * // optionally specify the ecosystem if you're linking an ecosystem wallet\n * await linkProfile({ client, strategy: \"discord\" });\n * ```\n * @wallet\n */\nexport async function linkProfile(args: AuthArgsType) {\n  const connector = await getInAppWalletConnector(args.client, args.ecosystem);\n  return await connector.linkProfile(args);\n}\n\n/**\n * Disconnects an existing profile (authentication method) from the current user. Once disconnected, that profile can no longer be used to sign into the account.\n *\n * @param args - The object containing the profile that we want to unlink.\n * @returns A promise that resolves to the updated linked profiles.\n * @throws If the unlinking fails. This can happen if the account has no other associated profiles or if the profile that is being unlinked doesn't exists for the current logged in user.\n *\n * @example\n * ```ts\n * import { inAppWallet } from \"thirdweb/wallets\";\n *\n * const wallet = inAppWallet();\n * wallet.connect({ strategy: \"google\" });\n *\n * const profiles = await getProfiles({\n *  client,\n * });\n *\n * const updatedProfiles = await unlinkProfile({\n *  client,\n *  profileToUnlink: profiles[0],\n * });\n * ```\n * @wallet\n */\nexport async function unlinkProfile(args: UnlinkParams) {\n  const connector = await getInAppWalletConnector(args.client, args.ecosystem);\n  return await connector.unlinkProfile(args.profileToUnlink);\n}\n\n/**\n * Gets the linked profiles for the connected in-app or ecosystem wallet.\n *\n * @returns An array of accounts user profiles linked to the connected wallet.\n *\n * @example\n * ```ts\n * import { getProfiles } from \"thirdweb/wallets\";\n *\n * const profiles = await getProfiles({\n *  client,\n * });\n *\n * console.log(profiles[0].type); // will be \"email\", \"phone\", \"google\", \"discord\", etc\n * console.log(profiles[0].details.email);\n * console.log(profiles[0].details.phone);\n * ```\n *\n * ### Getting profiles for a ecosystem user\n *\n * ```ts\n * import { getProfiles } from \"thirdweb/wallets/in-app\";\n *\n * const profiles = await getProfiles({\n *  client,\n *  ecosystem: {\n *    id: \"ecosystem.your-ecosystem-id\",\n *  },\n * });\n * ```\n * @wallet\n */\nexport async function getProfiles(args: GetAuthenticatedUserParams) {\n  const connector = await getInAppWalletConnector(args.client, args.ecosystem);\n  return connector.getProfiles();\n}\n"], "mappings": ";;;;;AAkBA,eAAe,wBACb,QACA,WAAqB;AAErB,SAAO,gCACL,QACA,OAAOA,YAAU;AACf,UAAM,EAAE,kBAAiB,IAAK,MAAM,OAAO,6BAAqB;AAChE,WAAO,IAAI,kBAAkB;MAC3B,QAAQA;MACR;KACD;EACH,GACA,SAAS;AAEb;AAiBA,eAAe,qBAAqB,SAAmC;AACrE,QAAM,EAAE,QAAQ,UAAS,IAAK;AAC9B,QAAM,YAAY,MAAM,wBAAwB,QAAQ,SAAS;AACjE,QAAM,OAAO,MAAM,UAAU,QAAO;AACpC,UAAQ,KAAK,QAAQ;IACnB,KAAK,iCAAiC;AACpC,aAAO;IACT;EACF;AACA,SAAO;AACT;AAeA,eAAsB,aAAa,SAAmC;AACpE,QAAM,OAAO,MAAM,qBAAqB,OAAO;AAC/C,MAAI,QAAQ,WAAW,KAAK,aAAa;AACvC,WAAO,KAAK,YAAY;EAC1B;AACA,SAAO;AACT;AAeA,eAAsB,mBAAmB,SAAmC;AAC1E,QAAM,OAAO,MAAM,qBAAqB,OAAO;AAC/C,MAAI,QAAQ,iBAAiB,KAAK,aAAa;AAC7C,WAAO,KAAK,YAAY;EAC1B;AACA,SAAO;AACT;AAoBA,eAAsB,gBAAgB,MAAqB;AACzD,QAAM,YAAY,MAAM,wBAAwB,KAAK,QAAQ,KAAK,SAAS;AAC3E,SAAO,UAAU,gBAAgB,IAAI;AACvC;AA8BA,eAAsB,aAAa,MAAkB;AACnD,QAAM,YAAY,MAAM,wBAAwB,KAAK,QAAQ,KAAK,SAAS;AAC3E,SAAO,UAAU,aAAa,IAAI;AACpC;AAmBA,eAAsB,yBACpB,MAA4E;AAE5E,QAAM,YAAY,MAAM,wBAAwB,KAAK,QAAQ,KAAK,SAAS;AAC3E,MAAI,CAAC,UAAU,0BAA0B;AACvC,UAAM,IAAI,MACR,4DAA4D;EAEhE;AACA,SAAO,UAAU,yBACf,KAAK,UACL,KAAK,MACL,KAAK,WAAW;AAEpB;AAsBA,eAAsB,YAAY,MAAkB;AAClD,QAAM,YAAY,MAAM,wBAAwB,KAAK,QAAQ,KAAK,SAAS;AAC3E,SAAO,MAAM,UAAU,YAAY,IAAI;AACzC;AA2BA,eAAsB,cAAc,MAAkB;AACpD,QAAM,YAAY,MAAM,wBAAwB,KAAK,QAAQ,KAAK,SAAS;AAC3E,SAAO,MAAM,UAAU,cAAc,KAAK,eAAe;AAC3D;AAkCA,eAAsB,YAAY,MAAgC;AAChE,QAAM,YAAY,MAAM,wBAAwB,KAAK,QAAQ,KAAK,SAAS;AAC3E,SAAO,UAAU,YAAW;AAC9B;", "names": ["client"]}