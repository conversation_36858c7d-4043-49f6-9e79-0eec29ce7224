{"version": 3, "sources": ["../../thirdweb/src/utils/hashing/keccak256.ts", "../../thirdweb/src/utils/address.ts"], "sourcesContent": ["import { keccak_256 } from \"@noble/hashes/sha3\";\nimport {\n  type Hex,\n  hexToUint8Array,\n  isHex,\n  uint8ArrayToHex,\n} from \"../encoding/hex.js\";\n\ntype To = \"hex\" | \"bytes\";\n\ntype Keccak256Hash<TTo extends To> =\n  | (TTo extends \"bytes\" ? Uint8Array : never)\n  | (TTo extends \"hex\" ? Hex : never);\n\n/**\n * Calculates the Keccak-256 hash of the given value.\n * @param value - The value to hash, either as a hexadecimal string or a Uint8Array.\n * @param to - The desired output format of the hash (optional). Defaults to 'hex'.\n * @returns The Keccak-256 hash of the value in the specified format.\n * @example\n * ```ts\n * import { keccak256 } from \"thirdweb/utils\";\n * const hash = keccak256(\"0x1234\");\n * ```\n * @utils\n */\nexport function keccak256<TTo extends To = \"hex\">(\n  value: Hex | Uint8Array,\n  to?: TTo,\n): Keccak256Hash<TTo> {\n  const bytes = keccak_256(\n    isHex(value, { strict: false }) ? hexToUint8Array(value) : value,\n  );\n  if (to === \"bytes\") {\n    return bytes as Keccak256Hash<TTo>;\n  }\n  // default fall through to hex\n  return uint8ArrayToHex(bytes) as Keccak256Hash<TTo>;\n}\n", "import { LruMap } from \"./caching/lru.js\";\nimport { stringToBytes } from \"./encoding/to-bytes.js\";\nimport { keccak256 } from \"./hashing/keccak256.js\";\n\nexport type AddressInput = string;\nexport type Address = `0x${string}`;\n\nconst ADDRESS_REGEX = /^0x[a-fA-F0-9]{40}$/;\nconst IS_ADDRESS_CACHE = new LruMap<boolean>(4096);\n\n/**\n * Checks if a given string is a valid address.\n * @param address The address to check.\n * @returns True if the address is valid, false otherwise.\n * @example\n * ```ts\n * import { isAddress } from 'thirdweb/utils';\n *\n * isAddress('0x5aAeb6053F3E94C9b9A09f33669435E7Ef1BeAed');\n * //=> true\n * ```\n * @utils\n */\nexport function isAddress(address: string): address is Address {\n  if (IS_ADDRESS_CACHE.has(address)) {\n    // biome-ignore lint/style/noNonNullAssertion: the `has` above ensures that this will always be set\n    return IS_ADDRESS_CACHE.get(address)!;\n  }\n  const result =\n    ADDRESS_REGEX.test(address) &&\n    (address.toLowerCase() === address || checksumAddress(address) === address);\n  IS_ADDRESS_CACHE.set(address, result);\n  return result;\n}\n\n/**\n * Calculates the checksum address for the given address.\n * @param address - The address to calculate the checksum for.\n * @returns The checksum address.\n * @example\n * ```ts\n * import { checksumAddress } from 'thirdweb/utils';\n *\n * checksumAddress('0x5aAeb6053F3E94C9b9A09f33669435E7Ef1BeAed');\n * //=> '0x5aAeb6053F3E94C9b9A09f33669435E7Ef1BeAed'\n * ```\n * @utils\n */\nexport function checksumAddress(address: string): Address {\n  const hexAddress = address.substring(2).toLowerCase();\n  const hash = keccak256(stringToBytes(hexAddress), \"bytes\");\n\n  const address_ = hexAddress.split(\"\");\n  for (let i = 0; i < 40; i += 2) {\n    // biome-ignore lint/style/noNonNullAssertion: <explanation>\n    if (hash[i >> 1]! >> 4 >= 8 && address[i]) {\n      // biome-ignore lint/style/noNonNullAssertion: <explanation>\n      address_[i] = address_[i]!.toUpperCase();\n    }\n\n    // biome-ignore lint/style/noNonNullAssertion: <explanation>\n    if ((hash[i >> 1]! & 0x0f) >= 8 && address[i + 1]) {\n      // biome-ignore lint/style/noNonNullAssertion: <explanation>\n      address_[i + 1] = address_[i + 1]!.toUpperCase();\n    }\n  }\n\n  return `0x${address_.join(\"\")}`;\n}\n\n/**\n * Retrieves the address after performing validation and checksumming.\n * @param address - The address to be validated and checksummed.\n * @returns The validated and checksummed address.\n * @throws Error if the address is invalid.\n * @example\n * ```ts\n * import { getAddress } from 'thirdweb/utils';\n *\n * getAddress('0x5aAeb6053F3E94C9b9A09f33669435E7Ef1BeAed');\n * //=> '0x5aAeb6053F3E94C9b9A09f33669435E7Ef1BeAed'\n * ```\n * @utils\n */\nexport function getAddress(address: string): Address {\n  if (!isAddress(address)) {\n    throw new Error(`Invalid address: ${address}`);\n  }\n  return checksumAddress(address);\n}\n\n/**\n * Checksums and formats an address if valid. Note this function does not check if the provided address is an ENS.\n * @param address - The address to shorten.\n * @param length - The number of characters to keep from the start and end of the address.\n * @returns The shortened address.\n * @example\n * ```ts\n * import { shortenAddress } from 'thirdweb/utils';\n *\n * shortenAddress('0xa0cf798816d4b9b9866b5330eea46a18382f251e');\n * //=> '0xA0Cf...251e'\n * ```\n * @utils\n */\nexport function shortenAddress(address: string, length = 4) {\n  const _address = getAddress(address);\n  return shortenHex(_address, length);\n}\n\n/**\n * Shortens a hexadecimal string without performing any validation or checksumming.\n * @param hex - The hexadecimal string to shorten.\n * @param length - The number of characters to keep from the start and end of the string.\n * @returns The shortened hexadecimal string.\n * @example\n * ```ts\n * import { shortenHex } from 'thirdweb/utils';\n *\n * shortenHex('0xa0cf798816d4b9b9866b5330eea46a18382f251e');\n * //=> '0xa0cf...251e'\n * ```\n * @utils\n */\nexport function shortenHex(hex: string, length = 4) {\n  return `${hex.slice(0, length + 2)}...${hex.slice(-length)}`;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AA0BM,SAAU,UACd,OACA,IAAQ;AAER,QAAM,QAAQ,WACZ,MAAM,OAAO,EAAE,QAAQ,MAAK,CAAE,IAAI,gBAAgB,KAAK,IAAI,KAAK;AAElE,MAAI,OAAO,SAAS;AAClB,WAAO;EACT;AAEA,SAAO,gBAAgB,KAAK;AAC9B;;;AC/BA,IAAM,gBAAgB;AACtB,IAAM,mBAAmB,IAAI,OAAgB,IAAI;AAe3C,SAAU,UAAU,SAAe;AACvC,MAAI,iBAAiB,IAAI,OAAO,GAAG;AAEjC,WAAO,iBAAiB,IAAI,OAAO;EACrC;AACA,QAAM,SACJ,cAAc,KAAK,OAAO,MACzB,QAAQ,YAAW,MAAO,WAAW,gBAAgB,OAAO,MAAM;AACrE,mBAAiB,IAAI,SAAS,MAAM;AACpC,SAAO;AACT;AAeM,SAAU,gBAAgB,SAAe;AAC7C,QAAM,aAAa,QAAQ,UAAU,CAAC,EAAE,YAAW;AACnD,QAAM,OAAO,UAAU,cAAc,UAAU,GAAG,OAAO;AAEzD,QAAM,WAAW,WAAW,MAAM,EAAE;AACpC,WAAS,IAAI,GAAG,IAAI,IAAI,KAAK,GAAG;AAE9B,QAAI,KAAK,KAAK,CAAC,KAAM,KAAK,KAAK,QAAQ,CAAC,GAAG;AAEzC,eAAS,CAAC,IAAI,SAAS,CAAC,EAAG,YAAW;IACxC;AAGA,SAAK,KAAK,KAAK,CAAC,IAAK,OAAS,KAAK,QAAQ,IAAI,CAAC,GAAG;AAEjD,eAAS,IAAI,CAAC,IAAI,SAAS,IAAI,CAAC,EAAG,YAAW;IAChD;EACF;AAEA,SAAO,KAAK,SAAS,KAAK,EAAE,CAAC;AAC/B;AAgBM,SAAU,WAAW,SAAe;AACxC,MAAI,CAAC,UAAU,OAAO,GAAG;AACvB,UAAM,IAAI,MAAM,oBAAoB,OAAO,EAAE;EAC/C;AACA,SAAO,gBAAgB,OAAO;AAChC;AAgBM,SAAU,eAAe,SAAiB,SAAS,GAAC;AACxD,QAAM,WAAW,WAAW,OAAO;AACnC,SAAO,WAAW,UAAU,MAAM;AACpC;AAgBM,SAAU,WAAW,KAAa,SAAS,GAAC;AAChD,SAAO,GAAG,IAAI,MAAM,GAAG,SAAS,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC;AAC5D;", "names": []}