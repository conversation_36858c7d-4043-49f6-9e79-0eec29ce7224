import {
  isClaimConditionSupported
} from "./chunk-2FUH4VYO.js";
import {
  isGetActiveClaimConditionSupported
} from "./chunk-K2P2ESQF.js";
import {
  getClaimParams
} from "./chunk-FCUVI6NT.js";
import {
  isContractURISupported
} from "./chunk-2MHTLDIR.js";
import "./chunk-3PSI24KF.js";
import "./chunk-TNW5OLYF.js";
import "./chunk-43F4DZXD.js";
import {
  once
} from "./chunk-NACC2RRT.js";
import {
  prepareContractCall
} from "./chunk-VRPVKFEC.js";
import "./chunk-QGXAPRFG.js";
import "./chunk-YCZ3YGMG.js";
import {
  detectMethod
} from "./chunk-54TJVF2D.js";
import "./chunk-B5KK3HNL.js";
import "./chunk-EUTXYSM6.js";
import "./chunk-EGKW4L7U.js";
import "./chunk-WDN7SIFG.js";
import "./chunk-HAADYJEF.js";
import "./chunk-AXWRVETS.js";
import "./chunk-6NM2KW2J.js";
import "./chunk-N3KXRWQX.js";
import "./chunk-7RWWVHOG.js";
import "./chunk-FFXQ6EIY.js";
import "./chunk-XHUVGHMS.js";
import "./chunk-OLGC3KE4.js";
import "./chunk-UG7W3O5D.js";
import "./chunk-7RKCPKII.js";
import "./chunk-26FWGFQH.js";
import "./chunk-AIY5L6NH.js";
import "./chunk-BJ63FHMG.js";
import "./chunk-4LB33PYO.js";
import "./chunk-6CMZOK3K.js";
import "./chunk-HXWRQBIO.js";
import "./chunk-5UQS4U7E.js";
import "./chunk-ZLUBC7IW.js";
import "./chunk-5Q2S2L2S.js";
import "./chunk-2CIJO3V3.js";
import "./chunk-BJ2DNF5Z.js";
import "./chunk-ITZPY7G6.js";
import "./chunk-MTFDOOBS.js";
import "./chunk-PWCREQPQ.js";
import "./chunk-3OXDSLPJ.js";
import "./chunk-PPP72TBL.js";
import "./chunk-OSFP2VB7.js";
import "./chunk-OS7ZSSJM.js";

// node_modules/thirdweb/dist/esm/extensions/erc721/__generated__/IDrop/write/claim.js
var FN_SELECTOR = "0x84bb1e42";
var FN_INPUTS = [
  {
    type: "address",
    name: "receiver"
  },
  {
    type: "uint256",
    name: "quantity"
  },
  {
    type: "address",
    name: "currency"
  },
  {
    type: "uint256",
    name: "pricePerToken"
  },
  {
    type: "tuple",
    name: "allowlistProof",
    components: [
      {
        type: "bytes32[]",
        name: "proof"
      },
      {
        type: "uint256",
        name: "quantityLimitPerWallet"
      },
      {
        type: "uint256",
        name: "pricePerToken"
      },
      {
        type: "address",
        name: "currency"
      }
    ]
  },
  {
    type: "bytes",
    name: "data"
  }
];
var FN_OUTPUTS = [];
function isClaimSupported(availableSelectors) {
  return detectMethod({
    availableSelectors,
    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS]
  });
}
function claim(options) {
  const asyncOptions = once(async () => {
    return "asyncParams" in options ? await options.asyncParams() : options;
  });
  return prepareContractCall({
    contract: options.contract,
    method: [FN_SELECTOR, FN_INPUTS, FN_OUTPUTS],
    params: async () => {
      const resolvedOptions = await asyncOptions();
      return [
        resolvedOptions.receiver,
        resolvedOptions.quantity,
        resolvedOptions.currency,
        resolvedOptions.pricePerToken,
        resolvedOptions.allowlistProof,
        resolvedOptions.data
      ];
    },
    value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.value;
    },
    accessList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.accessList;
    },
    gas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gas;
    },
    gasPrice: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.gasPrice;
    },
    maxFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxFeePerGas;
    },
    maxPriorityFeePerGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.maxPriorityFeePerGas;
    },
    nonce: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.nonce;
    },
    extraGas: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.extraGas;
    },
    erc20Value: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.erc20Value;
    },
    authorizationList: async () => {
      var _a;
      return (_a = (await asyncOptions()).overrides) == null ? void 0 : _a.authorizationList;
    }
  });
}

// node_modules/thirdweb/dist/esm/extensions/erc721/drops/write/claimTo.js
function claimTo(options) {
  return claim({
    contract: options.contract,
    asyncParams: () => getClaimParams({
      type: "erc721",
      contract: options.contract,
      to: options.to,
      quantity: options.quantity,
      from: options.from,
      singlePhaseDrop: options.singlePhaseDrop
    })
  });
}
function isClaimToSupported(availableSelectors) {
  return isClaimSupported(availableSelectors) && // requires contractMetadata for claimer proofs
  isContractURISupported(availableSelectors) && // required to check if the contract supports the getActiveClaimCondition method
  (isGetActiveClaimConditionSupported(availableSelectors) || isClaimConditionSupported(availableSelectors));
}
export {
  claimTo,
  isClaimToSupported
};
//# sourceMappingURL=claimTo-5WOFDNMF.js.map
