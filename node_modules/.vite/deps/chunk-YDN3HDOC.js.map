{"version": 3, "sources": ["../../thirdweb/src/wallets/in-app/core/wallet/is-in-app-signer.ts", "../../thirdweb/src/react/core/utils/isSmartWallet.ts", "../../thirdweb/src/reactive/computedStore.ts", "../../thirdweb/src/reactive/effect.ts", "../../thirdweb/src/wallets/manager/index.ts", "../../thirdweb/src/utils/timeoutPromise.ts", "../../thirdweb/src/wallets/in-app/web/lib/get-url-token.ts", "../../thirdweb/src/wallets/connection/autoConnectCore.ts"], "sourcesContent": ["import { isEcosystemWallet } from \"../../../../wallets/ecosystem/is-ecosystem-wallet.js\";\nimport type { Wallet } from \"../../../interfaces/wallet.js\";\nimport { isSmartWallet } from \"../../../smart/index.js\";\nimport { isInAppWallet } from \"./index.js\";\n\nexport function isInAppSigner(options: {\n  wallet: Wallet;\n  connectedWallets: Wallet[];\n}) {\n  const isInAppOrEcosystem = (w: Wallet) =>\n    isInAppWallet(w) || isEcosystemWallet(w);\n  const isSmartWalletWithAdmin =\n    isSmartWallet(options.wallet) &&\n    options.connectedWallets.some(\n      (w) =>\n        isInAppOrEcosystem(w) &&\n        w.getAccount()?.address?.toLowerCase() ===\n          options.wallet.getAdminAccount?.()?.address?.toLowerCase(),\n    );\n  return isInAppOrEcosystem(options.wallet) || isSmartWalletWithAdmin;\n}\n", "import { isEcosystemWallet } from \"../../../wallets/ecosystem/is-ecosystem-wallet.js\";\nimport type { Wallet } from \"../../../wallets/interfaces/wallet.js\";\n\nexport function hasSmartAccount(activeWallet?: Wallet): boolean {\n  const config = activeWallet?.getConfig();\n  return (\n    activeWallet !== undefined &&\n    (activeWallet.id === \"smart\" ||\n      (activeWallet.id === \"inApp\" && !!config && \"smartAccount\" in config) ||\n      (isEcosystemWallet(activeWallet) && !!config && \"smartAccount\" in config))\n  );\n}\n", "import type { Store } from \"./store.js\";\n\nexport type ReadonlyStore<T> = {\n  getValue(): T;\n  subscribe(listener: () => void): () => void;\n};\n\n/**\n * Create a readonly store whose value is computed from other stores\n * @param computation - The function to compute the value of the store\n * @param dependencies - The stores it depends on\n * @example\n * ```ts\n * const foo = computed(() => bar.getValue() + baz.getValue(), [bar, baz]);\n * ```\n * @returns A store object\n */\nexport function computedStore<T>(\n  // pass the values of the dependencies to the computation function\n  computation: () => T,\n  // biome-ignore lint/suspicious/noExplicitAny: library function that accepts any store type\n  dependencies: (Store<any> | ReadonlyStore<any>)[],\n): ReadonlyStore<T> {\n  type Listener = () => void;\n  const listeners = new Set<Listener>();\n\n  let value = computation();\n\n  const notify = () => {\n    for (const listener of listeners) {\n      listener();\n    }\n  };\n\n  const setValue = (newValue: T) => {\n    value = newValue;\n    notify();\n  };\n\n  // when any of the dependencies change, recompute the value and set it\n  for (const store of dependencies) {\n    store.subscribe(() => {\n      setValue(computation());\n    });\n  }\n\n  return {\n    getValue() {\n      return value;\n    },\n    subscribe(listener: Listener) {\n      listeners.add(listener);\n      return () => {\n        listeners.delete(listener);\n      };\n    },\n  };\n}\n", "import type { ReadonlyStore } from \"./computedStore.js\";\nimport type { Store } from \"./store.js\";\n\n/**\n * Run a function whenever dependencies change\n * @param effectFn - Side effect function to run\n * @param dependencies - The stores it depends on\n * @param runOnMount - Whether to run the effect function immediately or not\n * @example\n * ```ts\n * const foo = computed(() => bar.getValue() + baz.getValue(), [bar, baz]);\n * ```\n * @returns A function to stop listening to changes in the dependencies\n */\nexport function effect<T>(\n  // pass the values of the dependencies to the computation function\n  effectFn: () => T,\n  // biome-ignore lint/suspicious/noExplicitAny: library function that accepts any store type\n  dependencies: (Store<any> | ReadonlyStore<any>)[],\n  runOnMount = true,\n) {\n  if (runOnMount) {\n    effectFn();\n  }\n\n  // when any of the dependencies change, recompute the value and set it\n  const unsubscribeList = dependencies.map((store) => {\n    return store.subscribe(() => {\n      effectFn();\n    });\n  });\n\n  return () => {\n    for (const fn of unsubscribeList) {\n      fn();\n    }\n  };\n}\n", "import type { Chain } from \"../../chains/types.js\";\nimport { cacheChains } from \"../../chains/utils.js\";\nimport type { ThirdwebClient } from \"../../client/client.js\";\nimport { hasSmartAccount } from \"../../react/core/utils/isSmartWallet.js\";\nimport { computedStore } from \"../../reactive/computedStore.js\";\nimport { effect } from \"../../reactive/effect.js\";\nimport { createStore } from \"../../reactive/store.js\";\nimport { stringify } from \"../../utils/json.js\";\nimport type { AsyncStorage } from \"../../utils/storage/AsyncStorage.js\";\nimport { deleteConnectParamsFromStorage } from \"../../utils/storage/walletStorage.js\";\nimport type { Account, Wallet } from \"../interfaces/wallet.js\";\nimport { isSmartWallet } from \"../smart/index.js\";\nimport { smartWallet } from \"../smart/smart-wallet.js\";\nimport type { SmartWalletOptions } from \"../smart/types.js\";\nimport type { WalletId } from \"../wallet-types.js\";\n\ntype WalletIdToConnectedWalletMap = Map<string, Wallet>;\nexport type ConnectionStatus =\n  | \"connected\"\n  | \"disconnected\"\n  | \"connecting\"\n  | \"unknown\";\n\nconst CONNECTED_WALLET_IDS = \"thirdweb:connected-wallet-ids\";\nconst LAST_ACTIVE_EOA_ID = \"thirdweb:active-wallet-id\";\nconst LAST_ACTIVE_CHAIN = \"thirdweb:active-chain\";\n\nexport type ConnectionManager = ReturnType<typeof createConnectionManager>;\nexport type ConnectManagerOptions = {\n  client: ThirdwebClient;\n  accountAbstraction?: SmartWalletOptions;\n  setWalletAsActive?: boolean;\n  onConnect?: (wallet: Wallet) => void;\n};\n\n/**\n * Create a connection manager for Wallet connections\n * @param storage - An instance of type [`AsyncStorage`](https://portal.thirdweb.com/references/typescript/v5/AsyncStorage)\n * @example\n * ```ts\n * const manager = createConnectionManager();\n * ```\n * @returns A connection manager object\n * @walletUtils\n */\nexport function createConnectionManager(storage: AsyncStorage) {\n  // stores\n\n  // active wallet/account\n  const activeWalletStore = createStore<Wallet | undefined>(undefined);\n  const activeAccountStore = createStore<Account | undefined>(undefined);\n  const activeWalletChainStore = createStore<Chain | undefined>(undefined);\n  const activeWalletConnectionStatusStore =\n    createStore<ConnectionStatus>(\"unknown\");\n\n  const definedChainsStore = createStore<Map<number, Chain>>(new Map());\n\n  // update global cachedChains when defined Chains store updates\n  effect(() => {\n    cacheChains([...definedChainsStore.getValue().values()]);\n  }, [definedChainsStore]);\n\n  // change the active chain object to use the defined chain object\n  effect(() => {\n    const chainVal = activeWalletChainStore.getValue();\n    if (!chainVal) {\n      return;\n    }\n\n    const definedChain = definedChainsStore.getValue().get(chainVal.id);\n\n    if (!definedChain || definedChain === chainVal) {\n      return;\n    }\n\n    // update active chain store\n    activeWalletChainStore.setValue(definedChain);\n  }, [definedChainsStore, activeWalletChainStore]);\n\n  // other connected accounts\n  const walletIdToConnectedWalletMap =\n    createStore<WalletIdToConnectedWalletMap>(new Map());\n\n  const isAutoConnecting = createStore(false);\n\n  const connectedWallets = computedStore(() => {\n    return Array.from(walletIdToConnectedWalletMap.getValue().values());\n  }, [walletIdToConnectedWalletMap]);\n\n  // actions\n  const addConnectedWallet = (wallet: Wallet) => {\n    const oldValue = walletIdToConnectedWalletMap.getValue();\n    if (oldValue.has(wallet.id)) {\n      return;\n    }\n    const newValue = new Map(oldValue);\n    newValue.set(wallet.id, wallet);\n    walletIdToConnectedWalletMap.setValue(newValue);\n  };\n\n  const removeConnectedWallet = (wallet: Wallet) => {\n    const oldValue = walletIdToConnectedWalletMap.getValue();\n    const newValue = new Map(oldValue);\n    newValue.delete(wallet.id);\n    walletIdToConnectedWalletMap.setValue(newValue);\n  };\n\n  const onWalletDisconnect = (wallet: Wallet) => {\n    deleteConnectParamsFromStorage(storage, wallet.id);\n    removeConnectedWallet(wallet);\n\n    // if disconnecting the active wallet\n    if (activeWalletStore.getValue() === wallet) {\n      storage.removeItem(LAST_ACTIVE_EOA_ID);\n      activeAccountStore.setValue(undefined);\n      activeWalletChainStore.setValue(undefined);\n      activeWalletStore.setValue(undefined);\n      activeWalletConnectionStatusStore.setValue(\"disconnected\");\n    }\n  };\n\n  const disconnectWallet = (wallet: Wallet) => {\n    onWalletDisconnect(wallet);\n    wallet.disconnect();\n  };\n\n  // handle the connection logic, but don't set the wallet as active\n  const handleConnection = async (\n    wallet: Wallet,\n    options?: ConnectManagerOptions,\n  ) => {\n    const account = wallet.getAccount();\n    if (!account) {\n      throw new Error(\"Cannot set a wallet without an account as active\");\n    }\n\n    const activeWallet = await (async () => {\n      if (options?.accountAbstraction && !hasSmartAccount(wallet)) {\n        return await handleSmartWalletConnection(\n          wallet,\n          options.client,\n          options.accountAbstraction,\n          onWalletDisconnect,\n        );\n      } else {\n        return wallet;\n      }\n    })();\n\n    await storage.setItem(LAST_ACTIVE_EOA_ID, wallet.id);\n\n    // add personal wallet to connected wallets list even if it's not the active one\n    addConnectedWallet(wallet);\n\n    if (options?.setWalletAsActive !== false) {\n      handleSetActiveWallet(activeWallet);\n    }\n\n    wallet.subscribe(\"accountChanged\", async () => {\n      // We reimplement connect here to prevent memory leaks\n      const newWallet = await handleConnection(wallet, options);\n      options?.onConnect?.(newWallet);\n    });\n\n    return activeWallet;\n  };\n\n  const connect = async (wallet: Wallet, options?: ConnectManagerOptions) => {\n    // connectedWallet can be either wallet or smartWallet\n    const connectedWallet = await handleConnection(wallet, options);\n    options?.onConnect?.(connectedWallet);\n    return connectedWallet;\n  };\n\n  const handleSetActiveWallet = (activeWallet: Wallet) => {\n    const account = activeWallet.getAccount();\n    if (!account) {\n      throw new Error(\"Cannot set a wallet without an account as active\");\n    }\n\n    // also add it to connected wallets if it's not already there\n    addConnectedWallet(activeWallet);\n\n    // update active states\n    activeWalletStore.setValue(activeWallet);\n    activeAccountStore.setValue(account);\n    activeWalletChainStore.setValue(activeWallet.getChain());\n    activeWalletConnectionStatusStore.setValue(\"connected\");\n\n    // setup listeners\n\n    const onAccountsChanged = (newAccount: Account) => {\n      activeAccountStore.setValue(newAccount);\n    };\n\n    const unsubAccounts = activeWallet.subscribe(\n      \"accountChanged\",\n      onAccountsChanged,\n    );\n\n    const unsubChainChanged = activeWallet.subscribe(\"chainChanged\", (chain) =>\n      activeWalletChainStore.setValue(chain),\n    );\n    const unsubDisconnect = activeWallet.subscribe(\"disconnect\", () => {\n      handleDisconnect();\n    });\n\n    const handleDisconnect = () => {\n      onWalletDisconnect(activeWallet);\n      unsubAccounts();\n      unsubChainChanged();\n      unsubDisconnect();\n    };\n  };\n\n  const setActiveWallet = async (activeWallet: Wallet) => {\n    handleSetActiveWallet(activeWallet);\n    // do not set smart wallet as last active EOA\n    if (activeWallet.id !== \"smart\") {\n      await storage.setItem(LAST_ACTIVE_EOA_ID, activeWallet.id);\n    }\n  };\n\n  // side effects\n\n  effect(\n    () => {\n      const _chain = activeWalletChainStore.getValue();\n      if (_chain) {\n        storage.setItem(LAST_ACTIVE_CHAIN, stringify(_chain));\n      } else {\n        storage.removeItem(LAST_ACTIVE_CHAIN);\n      }\n    },\n    [activeWalletChainStore],\n    false,\n  );\n\n  // save last connected wallet ids to storage\n  effect(\n    async () => {\n      const prevAccounts = (await getStoredConnectedWalletIds(storage)) || [];\n      const accounts = connectedWallets.getValue();\n      const ids = accounts.map((acc) => acc?.id).filter((c) => !!c) as string[];\n\n      storage.setItem(\n        CONNECTED_WALLET_IDS,\n        stringify(Array.from(new Set([...prevAccounts, ...ids]))),\n      );\n    },\n    [connectedWallets],\n    false,\n  );\n\n  const switchActiveWalletChain = async (chain: Chain) => {\n    const wallet = activeWalletStore.getValue();\n    if (!wallet) {\n      throw new Error(\"No active wallet found\");\n    }\n\n    if (!wallet.switchChain) {\n      throw new Error(\"Wallet does not support switching chains\");\n    }\n\n    if (isSmartWallet(wallet)) {\n      // also switch personal wallet\n      const personalWalletId = await getStoredActiveWalletId(storage);\n      if (personalWalletId) {\n        const personalWallet = connectedWallets\n          .getValue()\n          .find((w) => w.id === personalWalletId);\n        if (personalWallet) {\n          await personalWallet.switchChain(chain);\n          await wallet.switchChain(chain);\n          // reset the active wallet as switch chain recreates a new smart account\n          handleSetActiveWallet(wallet);\n          return;\n        }\n      }\n      // If we couldn't find the personal wallet, just switch the smart wallet\n      await wallet.switchChain(chain);\n      handleSetActiveWallet(wallet);\n    } else {\n      await wallet.switchChain(chain);\n    }\n\n    // for wallets that dont implement events, just set it manually\n    activeWalletChainStore.setValue(wallet.getChain());\n  };\n\n  function defineChains(chains: Chain[]) {\n    const currentMapVal = definedChainsStore.getValue();\n\n    // if all chains to be defined are already defined, no need to update the definedChains map\n    const allChainsSame = chains.every((c) => {\n      const definedChain = currentMapVal.get(c.id);\n      // basically a deep equal check\n      return stringify(definedChain) === stringify(c);\n    });\n\n    if (allChainsSame) {\n      return;\n    }\n\n    const newMapVal = new Map(currentMapVal);\n    for (const c of chains) {\n      newMapVal.set(c.id, c);\n    }\n    definedChainsStore.setValue(newMapVal);\n  }\n\n  return {\n    activeWalletStore,\n    activeAccountStore,\n    connectedWallets,\n    addConnectedWallet,\n    disconnectWallet,\n    setActiveWallet,\n    connect,\n    handleConnection,\n    activeWalletChainStore,\n    switchActiveWalletChain,\n    activeWalletConnectionStatusStore,\n    isAutoConnecting,\n    removeConnectedWallet,\n    defineChains,\n  };\n}\n\n/**\n *\n * @internal\n */\nexport async function getStoredConnectedWalletIds(\n  storage: AsyncStorage,\n): Promise<string[] | null> {\n  try {\n    const value = await storage.getItem(CONNECTED_WALLET_IDS);\n    if (value) {\n      return JSON.parse(value) as string[];\n    }\n    return [];\n  } catch {\n    return [];\n  }\n}\n\n/**\n * @internal\n */\nexport async function getStoredActiveWalletId(\n  storage: AsyncStorage,\n): Promise<WalletId | null> {\n  try {\n    const value = await storage.getItem(LAST_ACTIVE_EOA_ID);\n    if (value) {\n      return value as WalletId;\n    }\n  } catch {}\n\n  return null;\n}\n\n/**\n * @internal\n */\nexport async function getLastConnectedChain(\n  storage: AsyncStorage,\n): Promise<Chain | null> {\n  try {\n    const value = await storage.getItem(LAST_ACTIVE_CHAIN);\n    if (value) {\n      return JSON.parse(value) as Chain;\n    }\n  } catch {}\n\n  return null;\n}\n\n/**\n * @internal\n */\nexport const handleSmartWalletConnection = async (\n  eoaWallet: Wallet,\n  client: ThirdwebClient,\n  options: SmartWalletOptions,\n  onWalletDisconnect: (wallet: Wallet) => void,\n) => {\n  const signer = eoaWallet.getAccount();\n  if (!signer) {\n    throw new Error(\"Cannot set a wallet without an account as active\");\n  }\n\n  const wallet = smartWallet(options);\n\n  await wallet.connect({\n    personalAccount: signer,\n    client: client,\n    chain: options.chain,\n  });\n\n  // Disconnect the active wallet when the EOA disconnects if it the active wallet is a smart wallet\n  const disconnectUnsub = eoaWallet.subscribe(\"disconnect\", () => {\n    handleDisconnect();\n  });\n  const handleDisconnect = () => {\n    disconnectUnsub();\n    onWalletDisconnect(wallet);\n  };\n\n  return wallet;\n};\n", "/**\n * Timeout a promise with a given Error message if the promise does not resolve in given time\n * @internal\n */\nexport function timeoutPromise<T>(\n  promise: Promise<T>,\n  option: { ms: number; message: string },\n) {\n  return new Promise<T>((resolve, reject) => {\n    const timeoutId = setTimeout(() => {\n      reject(new Error(option.message));\n    }, option.ms);\n\n    promise.then(\n      (res) => {\n        clearTimeout(timeoutId);\n        resolve(res);\n      },\n      (err) => {\n        clearTimeout(timeoutId);\n        reject(err);\n      },\n    );\n  });\n}\n", "import type { AuthOption } from \"../../../../wallets/types.js\";\nimport type { WalletId } from \"../../../wallet-types.js\";\nimport type { AuthStoredTokenWithCookieReturnType } from \"../../core/authentication/types.js\";\n\n/**\n * Checks for an auth token and associated metadata in the current URL\n */\nexport function getUrlToken():\n  | {\n      walletId?: WalletId;\n      authResult?: AuthStoredTokenWithCookieReturnType;\n      authProvider?: AuthOption;\n      authCookie?: string;\n    }\n  | undefined {\n  if (typeof document === \"undefined\") {\n    // Not in web\n    return undefined;\n  }\n\n  const queryString = window.location.search;\n  const params = new URLSearchParams(queryString);\n  const authResultString = params.get(\"authResult\");\n  const walletId = params.get(\"walletId\") as WalletId | undefined;\n  const authProvider = params.get(\"authProvider\") as AuthOption | undefined;\n  const authCookie = params.get(\"authCookie\") as string | undefined;\n\n  if ((authCookie || authResultString) && walletId) {\n    const authResult = (() => {\n      if (authResultString) {\n        params.delete(\"authResult\");\n        return JSON.parse(decodeURIComponent(authResultString));\n      }\n    })();\n    params.delete(\"walletId\");\n    params.delete(\"authProvider\");\n    params.delete(\"authCookie\");\n    window.history.pushState(\n      {},\n      \"\",\n      `${window.location.pathname}?${params.toString()}`,\n    );\n    return { walletId, authResult, authProvider, authCookie };\n  }\n  return undefined;\n}\n", "import type { Chain } from \"../../chains/types.js\";\nimport type { ThirdwebClient } from \"../../client/client.js\";\nimport type { AsyncStorage } from \"../../utils/storage/AsyncStorage.js\";\nimport { timeoutPromise } from \"../../utils/timeoutPromise.js\";\nimport { isEcosystemWallet } from \"../ecosystem/is-ecosystem-wallet.js\";\nimport { ClientScopedStorage } from \"../in-app/core/authentication/client-scoped-storage.js\";\nimport type {\n  AuthArgsType,\n  AuthStoredTokenWithCookieReturnType,\n} from \"../in-app/core/authentication/types.js\";\nimport { isInAppSigner } from \"../in-app/core/wallet/is-in-app-signer.js\";\nimport { getUrlToken } from \"../in-app/web/lib/get-url-token.js\";\nimport type { Wallet } from \"../interfaces/wallet.js\";\nimport {\n  type ConnectionManager,\n  getLastConnectedChain,\n  getStoredActiveWalletId,\n  getStoredConnectedWalletIds,\n} from \"../manager/index.js\";\nimport type { WalletId } from \"../wallet-types.js\";\nimport type { AutoConnectProps } from \"./types.js\";\n\ntype AutoConnectCoreProps = {\n  storage: AsyncStorage;\n  props: AutoConnectProps & { wallets: Wallet[] };\n  createWalletFn: (id: WalletId) => Wallet;\n  manager: ConnectionManager;\n  connectOverride?: (\n    walletOrFn: Wallet | (() => Promise<Wallet>),\n  ) => Promise<Wallet | null>;\n  getInstalledWallets?: () => Wallet[];\n  setLastAuthProvider?: (\n    authProvider: AuthArgsType[\"strategy\"],\n    storage: AsyncStorage,\n  ) => Promise<void>;\n  /**\n   * If true, the auto connect will be forced even if autoConnect has already been attempted successfully earlier.\n   *\n   * @default `false`\n   */\n  force?: boolean;\n};\n\nlet lastAutoConnectionResultPromise: Promise<boolean> | undefined = undefined;\n\n/**\n * @internal\n */\nexport const autoConnectCore = async (props: AutoConnectCoreProps) => {\n  // if an auto connect was attempted already\n  if (lastAutoConnectionResultPromise && !props.force) {\n    // wait for its resolution\n    const lastResult = await lastAutoConnectionResultPromise;\n    // if it was successful, return true\n    // if not continue with the new auto connect\n    if (lastResult) {\n      return true;\n    }\n  }\n\n  const resultPromise = _autoConnectCore(props);\n  lastAutoConnectionResultPromise = resultPromise;\n  return resultPromise;\n};\n\nconst _autoConnectCore = async ({\n  storage,\n  props,\n  createWalletFn,\n  manager,\n  connectOverride,\n  getInstalledWallets,\n  setLastAuthProvider,\n}: AutoConnectCoreProps): Promise<boolean> => {\n  const { wallets, onConnect } = props;\n  const timeout = props.timeout ?? 15000;\n\n  let autoConnected = false;\n  manager.isAutoConnecting.setValue(true);\n\n  let [lastConnectedWalletIds, lastActiveWalletId] = await Promise.all([\n    getStoredConnectedWalletIds(storage),\n    getStoredActiveWalletId(storage),\n  ]);\n\n  const urlToken = getUrlToken();\n\n  // If an auth cookie is found and this site supports the wallet, we'll set the auth cookie in the client storage\n  const wallet = wallets.find((w) => w.id === urlToken?.walletId);\n  if (urlToken?.authCookie && wallet) {\n    const clientStorage = new ClientScopedStorage({\n      storage,\n      clientId: props.client.clientId,\n      ecosystem: isEcosystemWallet(wallet)\n        ? {\n            id: wallet.id,\n            partnerId: wallet.getConfig()?.partnerId,\n          }\n        : undefined,\n    });\n    await clientStorage.saveAuthCookie(urlToken.authCookie);\n  }\n  if (urlToken?.walletId) {\n    lastActiveWalletId = urlToken.walletId;\n    lastConnectedWalletIds = lastConnectedWalletIds?.includes(urlToken.walletId)\n      ? lastConnectedWalletIds\n      : [urlToken.walletId, ...(lastConnectedWalletIds || [])];\n  }\n\n  if (urlToken?.authProvider) {\n    await setLastAuthProvider?.(urlToken.authProvider, storage);\n  }\n\n  // if no wallets were last connected or we didn't receive an auth token\n  if (!lastConnectedWalletIds) {\n    return autoConnected;\n  }\n\n  // this flow can actually be used for a first connection in the case of a redirect\n  // in that case, we default to the passed chain to connect to\n  const lastConnectedChain =\n    (await getLastConnectedChain(storage)) || props.chain;\n  const availableWallets = [...wallets, ...(getInstalledWallets?.() ?? [])];\n  const activeWallet =\n    lastActiveWalletId &&\n    (availableWallets.find((w) => w.id === lastActiveWalletId) ||\n      createWalletFn(lastActiveWalletId));\n\n  if (activeWallet) {\n    manager.activeWalletConnectionStatusStore.setValue(\"connecting\"); // only set connecting status if we are connecting the last active EOA\n    await timeoutPromise(\n      handleWalletConnection({\n        wallet: activeWallet,\n        client: props.client,\n        lastConnectedChain,\n        authResult: urlToken?.authResult,\n      }),\n      {\n        ms: timeout,\n        message: `AutoConnect timeout: ${timeout}ms limit exceeded.`,\n      },\n    ).catch((err) => {\n      console.warn(err.message);\n      if (props.onTimeout) {\n        props.onTimeout();\n      }\n    });\n\n    try {\n      // connected wallet could be activeWallet or smart wallet\n      const connectedWallet = await (connectOverride\n        ? connectOverride(activeWallet)\n        : manager.connect(activeWallet, {\n            client: props.client,\n            accountAbstraction: props.accountAbstraction,\n          }));\n      if (connectedWallet) {\n        autoConnected = true;\n        try {\n          onConnect?.(connectedWallet);\n        } catch {\n          // ignore\n        }\n      } else {\n        manager.activeWalletConnectionStatusStore.setValue(\"disconnected\");\n      }\n    } catch (e) {\n      if (e instanceof Error) {\n        console.warn(\"Error auto connecting wallet:\", e.message);\n      }\n      manager.activeWalletConnectionStatusStore.setValue(\"disconnected\");\n    }\n  } else {\n    manager.activeWalletConnectionStatusStore.setValue(\"disconnected\");\n  }\n\n  // then connect wallets that were last connected but were not set as active\n  const otherWallets = availableWallets.filter(\n    (w) => w.id !== lastActiveWalletId && lastConnectedWalletIds.includes(w.id),\n  );\n  for (const wallet of otherWallets) {\n    try {\n      await handleWalletConnection({\n        wallet,\n        client: props.client,\n        lastConnectedChain,\n        authResult: urlToken?.authResult,\n      });\n      manager.addConnectedWallet(wallet);\n    } catch {\n      // no-op\n    }\n  }\n\n  // Auto-login with SIWE\n  const isIAW =\n    activeWallet &&\n    isInAppSigner({\n      wallet: activeWallet,\n      connectedWallets: activeWallet\n        ? [activeWallet, ...otherWallets]\n        : otherWallets,\n    });\n  if (\n    isIAW &&\n    props.siweAuth?.requiresAuth &&\n    !props.siweAuth?.isLoggedIn &&\n    !props.siweAuth?.isLoggingIn\n  ) {\n    await props.siweAuth?.doLogin().catch((err) => {\n      console.warn(\"Error signing in with SIWE:\", err.message);\n    });\n  }\n  manager.isAutoConnecting.setValue(false);\n  return autoConnected; // useQuery needs a return value\n};\n\n/**\n * @internal\n */\nexport async function handleWalletConnection(props: {\n  wallet: Wallet;\n  client: ThirdwebClient;\n  authResult: AuthStoredTokenWithCookieReturnType | undefined;\n  lastConnectedChain: Chain | undefined;\n}) {\n  return props.wallet.autoConnect({\n    client: props.client,\n    chain: props.lastConnectedChain,\n    authResult: props.authResult,\n  });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKM,SAAU,cAAc,SAG7B;AACC,QAAM,qBAAqB,CAAC,MAC1B,cAAc,CAAC,KAAK,kBAAkB,CAAC;AACzC,QAAM,yBACJ,cAAc,QAAQ,MAAM,KAC5B,QAAQ,iBAAiB,KACvB,CAAC,MAAG;AAdV;AAeQ,8BAAmB,CAAC,OACpB,aAAE,WAAU,MAAZ,mBAAgB,YAAhB,mBAAyB,qBACvB,+BAAQ,QAAO,oBAAf,mDAAoC,YAApC,mBAA6C;GAAa;AAElE,SAAO,mBAAmB,QAAQ,MAAM,KAAK;AAC/C;;;ACjBM,SAAU,gBAAgB,cAAqB;AACnD,QAAM,SAAS,6CAAc;AAC7B,SACE,iBAAiB,WAChB,aAAa,OAAO,WAClB,aAAa,OAAO,WAAW,CAAC,CAAC,UAAU,kBAAkB,UAC7D,kBAAkB,YAAY,KAAK,CAAC,CAAC,UAAU,kBAAkB;AAExE;;;ACMM,SAAU,cAEd,aAEA,cAAiD;AAGjD,QAAM,YAAY,oBAAI,IAAG;AAEzB,MAAI,QAAQ,YAAW;AAEvB,QAAM,SAAS,MAAK;AAClB,eAAW,YAAY,WAAW;AAChC,eAAQ;IACV;EACF;AAEA,QAAM,WAAW,CAAC,aAAe;AAC/B,YAAQ;AACR,WAAM;EACR;AAGA,aAAW,SAAS,cAAc;AAChC,UAAM,UAAU,MAAK;AACnB,eAAS,YAAW,CAAE;IACxB,CAAC;EACH;AAEA,SAAO;IACL,WAAQ;AACN,aAAO;IACT;IACA,UAAU,UAAkB;AAC1B,gBAAU,IAAI,QAAQ;AACtB,aAAO,MAAK;AACV,kBAAU,OAAO,QAAQ;MAC3B;IACF;;AAEJ;;;AC3CM,SAAU,OAEd,UAEA,cACA,aAAa,MAAI;AAEjB,MAAI,YAAY;AACd,aAAQ;EACV;AAGA,QAAM,kBAAkB,aAAa,IAAI,CAAC,UAAS;AACjD,WAAO,MAAM,UAAU,MAAK;AAC1B,eAAQ;IACV,CAAC;EACH,CAAC;AAED,SAAO,MAAK;AACV,eAAW,MAAM,iBAAiB;AAChC,SAAE;IACJ;EACF;AACF;;;ACdA,IAAM,uBAAuB;AAC7B,IAAM,qBAAqB;AAC3B,IAAM,oBAAoB;AAoBpB,SAAU,wBAAwB,SAAqB;AAI3D,QAAM,oBAAoB,YAAgC,MAAS;AACnE,QAAM,qBAAqB,YAAiC,MAAS;AACrE,QAAM,yBAAyB,YAA+B,MAAS;AACvE,QAAM,oCACJ,YAA8B,SAAS;AAEzC,QAAM,qBAAqB,YAAgC,oBAAI,IAAG,CAAE;AAGpE,SAAO,MAAK;AACV,gBAAY,CAAC,GAAG,mBAAmB,SAAQ,EAAG,OAAM,CAAE,CAAC;EACzD,GAAG,CAAC,kBAAkB,CAAC;AAGvB,SAAO,MAAK;AACV,UAAM,WAAW,uBAAuB,SAAQ;AAChD,QAAI,CAAC,UAAU;AACb;IACF;AAEA,UAAM,eAAe,mBAAmB,SAAQ,EAAG,IAAI,SAAS,EAAE;AAElE,QAAI,CAAC,gBAAgB,iBAAiB,UAAU;AAC9C;IACF;AAGA,2BAAuB,SAAS,YAAY;EAC9C,GAAG,CAAC,oBAAoB,sBAAsB,CAAC;AAG/C,QAAM,+BACJ,YAA0C,oBAAI,IAAG,CAAE;AAErD,QAAM,mBAAmB,YAAY,KAAK;AAE1C,QAAM,mBAAmB,cAAc,MAAK;AAC1C,WAAO,MAAM,KAAK,6BAA6B,SAAQ,EAAG,OAAM,CAAE;EACpE,GAAG,CAAC,4BAA4B,CAAC;AAGjC,QAAM,qBAAqB,CAAC,WAAkB;AAC5C,UAAM,WAAW,6BAA6B,SAAQ;AACtD,QAAI,SAAS,IAAI,OAAO,EAAE,GAAG;AAC3B;IACF;AACA,UAAM,WAAW,IAAI,IAAI,QAAQ;AACjC,aAAS,IAAI,OAAO,IAAI,MAAM;AAC9B,iCAA6B,SAAS,QAAQ;EAChD;AAEA,QAAM,wBAAwB,CAAC,WAAkB;AAC/C,UAAM,WAAW,6BAA6B,SAAQ;AACtD,UAAM,WAAW,IAAI,IAAI,QAAQ;AACjC,aAAS,OAAO,OAAO,EAAE;AACzB,iCAA6B,SAAS,QAAQ;EAChD;AAEA,QAAM,qBAAqB,CAAC,WAAkB;AAC5C,mCAA+B,SAAS,OAAO,EAAE;AACjD,0BAAsB,MAAM;AAG5B,QAAI,kBAAkB,SAAQ,MAAO,QAAQ;AAC3C,cAAQ,WAAW,kBAAkB;AACrC,yBAAmB,SAAS,MAAS;AACrC,6BAAuB,SAAS,MAAS;AACzC,wBAAkB,SAAS,MAAS;AACpC,wCAAkC,SAAS,cAAc;IAC3D;EACF;AAEA,QAAM,mBAAmB,CAAC,WAAkB;AAC1C,uBAAmB,MAAM;AACzB,WAAO,WAAU;EACnB;AAGA,QAAM,mBAAmB,OACvB,QACA,YACE;AACF,UAAM,UAAU,OAAO,WAAU;AACjC,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,MAAM,kDAAkD;IACpE;AAEA,UAAM,eAAe,OAAO,YAAW;AACrC,WAAI,mCAAS,uBAAsB,CAAC,gBAAgB,MAAM,GAAG;AAC3D,eAAO,MAAM,4BACX,QACA,QAAQ,QACR,QAAQ,oBACR,kBAAkB;MAEtB,OAAO;AACL,eAAO;MACT;IACF,GAAE;AAEF,UAAM,QAAQ,QAAQ,oBAAoB,OAAO,EAAE;AAGnD,uBAAmB,MAAM;AAEzB,SAAI,mCAAS,uBAAsB,OAAO;AACxC,4BAAsB,YAAY;IACpC;AAEA,WAAO,UAAU,kBAAkB,YAAW;AA7JlD;AA+JM,YAAM,YAAY,MAAM,iBAAiB,QAAQ,OAAO;AACxD,+CAAS,cAAT,iCAAqB;IACvB,CAAC;AAED,WAAO;EACT;AAEA,QAAM,UAAU,OAAO,QAAgB,YAAmC;AAtK5E;AAwKI,UAAM,kBAAkB,MAAM,iBAAiB,QAAQ,OAAO;AAC9D,6CAAS,cAAT,iCAAqB;AACrB,WAAO;EACT;AAEA,QAAM,wBAAwB,CAAC,iBAAwB;AACrD,UAAM,UAAU,aAAa,WAAU;AACvC,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,MAAM,kDAAkD;IACpE;AAGA,uBAAmB,YAAY;AAG/B,sBAAkB,SAAS,YAAY;AACvC,uBAAmB,SAAS,OAAO;AACnC,2BAAuB,SAAS,aAAa,SAAQ,CAAE;AACvD,sCAAkC,SAAS,WAAW;AAItD,UAAM,oBAAoB,CAAC,eAAuB;AAChD,yBAAmB,SAAS,UAAU;IACxC;AAEA,UAAM,gBAAgB,aAAa,UACjC,kBACA,iBAAiB;AAGnB,UAAM,oBAAoB,aAAa,UAAU,gBAAgB,CAAC,UAChE,uBAAuB,SAAS,KAAK,CAAC;AAExC,UAAM,kBAAkB,aAAa,UAAU,cAAc,MAAK;AAChE,uBAAgB;IAClB,CAAC;AAED,UAAM,mBAAmB,MAAK;AAC5B,yBAAmB,YAAY;AAC/B,oBAAa;AACb,wBAAiB;AACjB,sBAAe;IACjB;EACF;AAEA,QAAM,kBAAkB,OAAO,iBAAwB;AACrD,0BAAsB,YAAY;AAElC,QAAI,aAAa,OAAO,SAAS;AAC/B,YAAM,QAAQ,QAAQ,oBAAoB,aAAa,EAAE;IAC3D;EACF;AAIA,SACE,MAAK;AACH,UAAM,SAAS,uBAAuB,SAAQ;AAC9C,QAAI,QAAQ;AACV,cAAQ,QAAQ,mBAAmB,UAAU,MAAM,CAAC;IACtD,OAAO;AACL,cAAQ,WAAW,iBAAiB;IACtC;EACF,GACA,CAAC,sBAAsB,GACvB,KAAK;AAIP,SACE,YAAW;AACT,UAAM,eAAgB,MAAM,4BAA4B,OAAO,KAAM,CAAA;AACrE,UAAM,WAAW,iBAAiB,SAAQ;AAC1C,UAAM,MAAM,SAAS,IAAI,CAAC,QAAQ,2BAAK,EAAE,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;AAE5D,YAAQ,QACN,sBACA,UAAU,MAAM,KAAK,oBAAI,IAAI,CAAC,GAAG,cAAc,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;EAE7D,GACA,CAAC,gBAAgB,GACjB,KAAK;AAGP,QAAM,0BAA0B,OAAO,UAAgB;AACrD,UAAM,SAAS,kBAAkB,SAAQ;AACzC,QAAI,CAAC,QAAQ;AACX,YAAM,IAAI,MAAM,wBAAwB;IAC1C;AAEA,QAAI,CAAC,OAAO,aAAa;AACvB,YAAM,IAAI,MAAM,0CAA0C;IAC5D;AAEA,QAAI,cAAc,MAAM,GAAG;AAEzB,YAAM,mBAAmB,MAAM,wBAAwB,OAAO;AAC9D,UAAI,kBAAkB;AACpB,cAAM,iBAAiB,iBACpB,SAAQ,EACR,KAAK,CAAC,MAAM,EAAE,OAAO,gBAAgB;AACxC,YAAI,gBAAgB;AAClB,gBAAM,eAAe,YAAY,KAAK;AACtC,gBAAM,OAAO,YAAY,KAAK;AAE9B,gCAAsB,MAAM;AAC5B;QACF;MACF;AAEA,YAAM,OAAO,YAAY,KAAK;AAC9B,4BAAsB,MAAM;IAC9B,OAAO;AACL,YAAM,OAAO,YAAY,KAAK;IAChC;AAGA,2BAAuB,SAAS,OAAO,SAAQ,CAAE;EACnD;AAEA,WAAS,aAAa,QAAe;AACnC,UAAM,gBAAgB,mBAAmB,SAAQ;AAGjD,UAAM,gBAAgB,OAAO,MAAM,CAAC,MAAK;AACvC,YAAM,eAAe,cAAc,IAAI,EAAE,EAAE;AAE3C,aAAO,UAAU,YAAY,MAAM,UAAU,CAAC;IAChD,CAAC;AAED,QAAI,eAAe;AACjB;IACF;AAEA,UAAM,YAAY,IAAI,IAAI,aAAa;AACvC,eAAW,KAAK,QAAQ;AACtB,gBAAU,IAAI,EAAE,IAAI,CAAC;IACvB;AACA,uBAAmB,SAAS,SAAS;EACvC;AAEA,SAAO;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;AAEJ;AAMA,eAAsB,4BACpB,SAAqB;AAErB,MAAI;AACF,UAAM,QAAQ,MAAM,QAAQ,QAAQ,oBAAoB;AACxD,QAAI,OAAO;AACT,aAAO,KAAK,MAAM,KAAK;IACzB;AACA,WAAO,CAAA;EACT,QAAQ;AACN,WAAO,CAAA;EACT;AACF;AAKA,eAAsB,wBACpB,SAAqB;AAErB,MAAI;AACF,UAAM,QAAQ,MAAM,QAAQ,QAAQ,kBAAkB;AACtD,QAAI,OAAO;AACT,aAAO;IACT;EACF,QAAQ;EAAC;AAET,SAAO;AACT;AAKA,eAAsB,sBACpB,SAAqB;AAErB,MAAI;AACF,UAAM,QAAQ,MAAM,QAAQ,QAAQ,iBAAiB;AACrD,QAAI,OAAO;AACT,aAAO,KAAK,MAAM,KAAK;IACzB;EACF,QAAQ;EAAC;AAET,SAAO;AACT;AAKO,IAAM,8BAA8B,OACzC,WACA,QACA,SACA,uBACE;AACF,QAAM,SAAS,UAAU,WAAU;AACnC,MAAI,CAAC,QAAQ;AACX,UAAM,IAAI,MAAM,kDAAkD;EACpE;AAEA,QAAM,SAAS,YAAY,OAAO;AAElC,QAAM,OAAO,QAAQ;IACnB,iBAAiB;IACjB;IACA,OAAO,QAAQ;GAChB;AAGD,QAAM,kBAAkB,UAAU,UAAU,cAAc,MAAK;AAC7D,qBAAgB;EAClB,CAAC;AACD,QAAM,mBAAmB,MAAK;AAC5B,oBAAe;AACf,uBAAmB,MAAM;EAC3B;AAEA,SAAO;AACT;;;ACvZM,SAAU,eACd,SACA,QAAuC;AAEvC,SAAO,IAAI,QAAW,CAAC,SAAS,WAAU;AACxC,UAAM,YAAY,WAAW,MAAK;AAChC,aAAO,IAAI,MAAM,OAAO,OAAO,CAAC;IAClC,GAAG,OAAO,EAAE;AAEZ,YAAQ,KACN,CAAC,QAAO;AACN,mBAAa,SAAS;AACtB,cAAQ,GAAG;IACb,GACA,CAAC,QAAO;AACN,mBAAa,SAAS;AACtB,aAAO,GAAG;IACZ,CAAC;EAEL,CAAC;AACH;;;ACjBM,SAAU,cAAW;AAQzB,MAAI,OAAO,aAAa,aAAa;AAEnC,WAAO;EACT;AAEA,QAAM,cAAc,OAAO,SAAS;AACpC,QAAM,SAAS,IAAI,gBAAgB,WAAW;AAC9C,QAAM,mBAAmB,OAAO,IAAI,YAAY;AAChD,QAAM,WAAW,OAAO,IAAI,UAAU;AACtC,QAAM,eAAe,OAAO,IAAI,cAAc;AAC9C,QAAM,aAAa,OAAO,IAAI,YAAY;AAE1C,OAAK,cAAc,qBAAqB,UAAU;AAChD,UAAM,cAAc,MAAK;AACvB,UAAI,kBAAkB;AACpB,eAAO,OAAO,YAAY;AAC1B,eAAO,KAAK,MAAM,mBAAmB,gBAAgB,CAAC;MACxD;IACF,GAAE;AACF,WAAO,OAAO,UAAU;AACxB,WAAO,OAAO,cAAc;AAC5B,WAAO,OAAO,YAAY;AAC1B,WAAO,QAAQ,UACb,CAAA,GACA,IACA,GAAG,OAAO,SAAS,QAAQ,IAAI,OAAO,SAAQ,CAAE,EAAE;AAEpD,WAAO,EAAE,UAAU,YAAY,cAAc,WAAU;EACzD;AACA,SAAO;AACT;;;ACFA,IAAI,kCAAgE;AAK7D,IAAM,kBAAkB,OAAO,UAA+B;AAEnE,MAAI,mCAAmC,CAAC,MAAM,OAAO;AAEnD,UAAM,aAAa,MAAM;AAGzB,QAAI,YAAY;AACd,aAAO;IACT;EACF;AAEA,QAAM,gBAAgB,iBAAiB,KAAK;AAC5C,oCAAkC;AAClC,SAAO;AACT;AAEA,IAAM,mBAAmB,OAAO,EAC9B,SACA,OACA,gBACA,SACA,iBACA,qBACA,oBAAmB,MACwB;AAtE7C;AAuEE,QAAM,EAAE,SAAS,UAAS,IAAK;AAC/B,QAAM,UAAU,MAAM,WAAW;AAEjC,MAAI,gBAAgB;AACpB,UAAQ,iBAAiB,SAAS,IAAI;AAEtC,MAAI,CAAC,wBAAwB,kBAAkB,IAAI,MAAM,QAAQ,IAAI;IACnE,4BAA4B,OAAO;IACnC,wBAAwB,OAAO;GAChC;AAED,QAAM,WAAW,YAAW;AAG5B,QAAM,SAAS,QAAQ,KAAK,CAAC,MAAM,EAAE,QAAO,qCAAU,SAAQ;AAC9D,OAAI,qCAAU,eAAc,QAAQ;AAClC,UAAM,gBAAgB,IAAI,oBAAoB;MAC5C;MACA,UAAU,MAAM,OAAO;MACvB,WAAW,kBAAkB,MAAM,IAC/B;QACE,IAAI,OAAO;QACX,YAAW,YAAO,UAAS,MAAhB,mBAAoB;UAEjC;KACL;AACD,UAAM,cAAc,eAAe,SAAS,UAAU;EACxD;AACA,MAAI,qCAAU,UAAU;AACtB,yBAAqB,SAAS;AAC9B,8BAAyB,iEAAwB,SAAS,SAAS,aAC/D,yBACA,CAAC,SAAS,UAAU,GAAI,0BAA0B,CAAA,CAAG;EAC3D;AAEA,MAAI,qCAAU,cAAc;AAC1B,WAAM,2DAAsB,SAAS,cAAc;EACrD;AAGA,MAAI,CAAC,wBAAwB;AAC3B,WAAO;EACT;AAIA,QAAM,qBACH,MAAM,sBAAsB,OAAO,KAAM,MAAM;AAClD,QAAM,mBAAmB,CAAC,GAAG,SAAS,IAAI,iEAA2B,CAAA,CAAG;AACxE,QAAM,eACJ,uBACC,iBAAiB,KAAK,CAAC,MAAM,EAAE,OAAO,kBAAkB,KACvD,eAAe,kBAAkB;AAErC,MAAI,cAAc;AAChB,YAAQ,kCAAkC,SAAS,YAAY;AAC/D,UAAM,eACJ,uBAAuB;MACrB,QAAQ;MACR,QAAQ,MAAM;MACd;MACA,YAAY,qCAAU;KACvB,GACD;MACE,IAAI;MACJ,SAAS,wBAAwB,OAAO;KACzC,EACD,MAAM,CAAC,QAAO;AACd,cAAQ,KAAK,IAAI,OAAO;AACxB,UAAI,MAAM,WAAW;AACnB,cAAM,UAAS;MACjB;IACF,CAAC;AAED,QAAI;AAEF,YAAM,kBAAkB,OAAO,kBAC3B,gBAAgB,YAAY,IAC5B,QAAQ,QAAQ,cAAc;QAC5B,QAAQ,MAAM;QACd,oBAAoB,MAAM;OAC3B;AACL,UAAI,iBAAiB;AACnB,wBAAgB;AAChB,YAAI;AACF,iDAAY;QACd,QAAQ;QAER;MACF,OAAO;AACL,gBAAQ,kCAAkC,SAAS,cAAc;MACnE;IACF,SAAS,GAAG;AACV,UAAI,aAAa,OAAO;AACtB,gBAAQ,KAAK,iCAAiC,EAAE,OAAO;MACzD;AACA,cAAQ,kCAAkC,SAAS,cAAc;IACnE;EACF,OAAO;AACL,YAAQ,kCAAkC,SAAS,cAAc;EACnE;AAGA,QAAM,eAAe,iBAAiB,OACpC,CAAC,MAAM,EAAE,OAAO,sBAAsB,uBAAuB,SAAS,EAAE,EAAE,CAAC;AAE7E,aAAWA,WAAU,cAAc;AACjC,QAAI;AACF,YAAM,uBAAuB;QAC3B,QAAAA;QACA,QAAQ,MAAM;QACd;QACA,YAAY,qCAAU;OACvB;AACD,cAAQ,mBAAmBA,OAAM;IACnC,QAAQ;IAER;EACF;AAGA,QAAM,QACJ,gBACA,cAAc;IACZ,QAAQ;IACR,kBAAkB,eACd,CAAC,cAAc,GAAG,YAAY,IAC9B;GACL;AACH,MACE,WACA,WAAM,aAAN,mBAAgB,iBAChB,GAAC,WAAM,aAAN,mBAAgB,eACjB,GAAC,WAAM,aAAN,mBAAgB,cACjB;AACA,YAAM,WAAM,aAAN,mBAAgB,UAAU,MAAM,CAAC,QAAO;AAC5C,cAAQ,KAAK,+BAA+B,IAAI,OAAO;IACzD;EACF;AACA,UAAQ,iBAAiB,SAAS,KAAK;AACvC,SAAO;AACT;AAKA,eAAsB,uBAAuB,OAK5C;AACC,SAAO,MAAM,OAAO,YAAY;IAC9B,QAAQ,MAAM;IACd,OAAO,MAAM;IACb,YAAY,MAAM;GACnB;AACH;", "names": ["wallet"]}