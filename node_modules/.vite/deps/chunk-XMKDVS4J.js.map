{"version": 3, "sources": ["../../ox/core/Value.ts", "../../ox/core/TransactionEnvelopeEip1559.ts", "../../ox/core/AccessList.ts", "../../ox/core/Rlp.ts", "../../ox/core/TransactionEnvelope.ts", "../../ox/core/TransactionEnvelopeEip2930.ts", "../../ox/core/Authorization.ts", "../../ox/core/TransactionEnvelopeEip7702.ts", "../../ox/core/TransactionEnvelopeLegacy.ts"], "sourcesContent": ["import * as Errors from './Errors.js'\n\n/** @see https://ethereum.github.io/yellowpaper/paper.pdf */\nexport const exponents = {\n  wei: 0,\n  gwei: 9,\n  szabo: 12,\n  finney: 15,\n  ether: 18,\n} as const\n\n/**\n * Formats a `bigint` Value to its string representation (divided by the given exponent).\n *\n * @example\n * ```ts twoslash\n * import { Value } from 'ox'\n *\n * Value.format(420_000_000_000n, 9)\n * // @log: '420'\n * ```\n *\n * @param value - The `bigint` Value to format.\n * @param decimals - The exponent to divide the `bigint` Value by.\n * @returns The string representation of the Value.\n */\nexport function format(value: bigint, decimals = 0) {\n  let display = value.toString()\n\n  const negative = display.startsWith('-')\n  if (negative) display = display.slice(1)\n\n  display = display.padStart(decimals, '0')\n\n  let [integer, fraction] = [\n    display.slice(0, display.length - decimals),\n    display.slice(display.length - decimals),\n  ]\n  fraction = fraction.replace(/(0+)$/, '')\n  return `${negative ? '-' : ''}${integer || '0'}${\n    fraction ? `.${fraction}` : ''\n  }`\n}\n\nexport declare namespace format {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Formats a `bigint` Value (default: wei) to a string representation of Ether.\n *\n * @example\n * ```ts twoslash\n * import { Value } from 'ox'\n *\n * Value.formatEther(1_000_000_000_000_000_000n)\n * // @log: '1'\n * ```\n *\n * @param wei - The Value to format.\n * @param unit - The unit to format the Value in. @default 'wei'.\n * @returns The Ether string representation of the Value.\n */\nexport function formatEther(\n  wei: bigint,\n  unit: 'wei' | 'gwei' | 'szabo' | 'finney' = 'wei',\n) {\n  return format(wei, exponents.ether - exponents[unit])\n}\n\nexport declare namespace formatEther {\n  type ErrorType = format.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Formats a `bigint` Value (default: wei) to a string representation of Gwei.\n *\n * @example\n * ```ts twoslash\n * import { Value } from 'ox'\n *\n * Value.formatGwei(1_000_000_000n)\n * // @log: '1'\n * ```\n *\n * @param wei - The Value to format.\n * @param unit - The unit to format the Value in. @default 'wei'.\n * @returns The Gwei string representation of the Value.\n */\nexport function formatGwei(wei: bigint, unit: 'wei' = 'wei') {\n  return format(wei, exponents.gwei - exponents[unit])\n}\n\nexport declare namespace formatGwei {\n  type ErrorType = format.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Parses a `string` representation of a Value to `bigint` (multiplied by the given exponent).\n *\n * @example\n * ```ts twoslash\n * import { Value } from 'ox'\n *\n * Value.from('420', 9)\n * // @log: 420000000000n\n * ```\n *\n * @param value - The string representation of the Value.\n * @param decimals - The exponent to multiply the Value by.\n * @returns The `bigint` representation of the Value.\n */\nexport function from(value: string, decimals = 0) {\n  if (!/^(-?)([0-9]*)\\.?([0-9]*)$/.test(value))\n    throw new InvalidDecimalNumberError({ value })\n\n  let [integer = '', fraction = '0'] = value.split('.')\n\n  const negative = integer.startsWith('-')\n  if (negative) integer = integer.slice(1)\n\n  // trim trailing zeros.\n  fraction = fraction.replace(/(0+)$/, '')\n\n  // round off if the fraction is larger than the number of decimals.\n  if (decimals === 0) {\n    if (Math.round(Number(`.${fraction}`)) === 1)\n      integer = `${BigInt(integer) + 1n}`\n    fraction = ''\n  } else if (fraction.length > decimals) {\n    const [left, unit, right] = [\n      fraction.slice(0, decimals - 1),\n      fraction.slice(decimals - 1, decimals),\n      fraction.slice(decimals),\n    ]\n\n    const rounded = Math.round(Number(`${unit}.${right}`))\n    if (rounded > 9)\n      fraction = `${BigInt(left) + BigInt(1)}0`.padStart(left.length + 1, '0')\n    else fraction = `${left}${rounded}`\n\n    if (fraction.length > decimals) {\n      fraction = fraction.slice(1)\n      integer = `${BigInt(integer) + 1n}`\n    }\n\n    fraction = fraction.slice(0, decimals)\n  } else {\n    fraction = fraction.padEnd(decimals, '0')\n  }\n\n  return BigInt(`${negative ? '-' : ''}${integer}${fraction}`)\n}\n\nexport declare namespace from {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Parses a string representation of Ether to a `bigint` Value (default: wei).\n *\n * @example\n * ```ts twoslash\n * import { Value } from 'ox'\n *\n * Value.fromEther('420')\n * // @log: 420000000000000000000n\n * ```\n *\n * @param ether - String representation of Ether.\n * @param unit - The unit to parse to. @default 'wei'.\n * @returns A `bigint` Value.\n */\nexport function fromEther(\n  ether: string,\n  unit: 'wei' | 'gwei' | 'szabo' | 'finney' = 'wei',\n) {\n  return from(ether, exponents.ether - exponents[unit])\n}\n\nexport declare namespace fromEther {\n  type ErrorType = from.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Parses a string representation of Gwei to a `bigint` Value (default: wei).\n *\n * @example\n * ```ts twoslash\n * import { Value } from 'ox'\n *\n * Value.fromGwei('420')\n * // @log: 420000000000n\n * ```\n *\n * @param gwei - String representation of Gwei.\n * @param unit - The unit to parse to. @default 'wei'.\n * @returns A `bigint` Value.\n */\nexport function fromGwei(gwei: string, unit: 'wei' = 'wei') {\n  return from(gwei, exponents.gwei - exponents[unit])\n}\n\nexport declare namespace fromGwei {\n  type ErrorType = from.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Thrown when a value is not a valid decimal number.\n *\n * @example\n * ```ts twoslash\n * import { Value } from 'ox'\n *\n * Value.fromEther('123.456.789')\n * // @error: Value.InvalidDecimalNumberError: Value `123.456.789` is not a valid decimal number.\n * ```\n */\nexport class InvalidDecimalNumberError extends Errors.BaseError {\n  override readonly name = 'Value.InvalidDecimalNumberError'\n  constructor({ value }: { value: string }) {\n    super(`Value \\`${value}\\` is not a valid decimal number.`)\n  }\n}\n", "import * as AccessList from './AccessList.js'\nimport * as Address from './Address.js'\nimport type * as Errors from './Errors.js'\nimport * as Hash from './Hash.js'\nimport * as Hex from './Hex.js'\nimport * as Rlp from './Rlp.js'\nimport * as Signature from './Signature.js'\nimport * as TransactionEnvelope from './TransactionEnvelope.js'\nimport type {\n  Assign,\n  Compute,\n  PartialBy,\n  UnionPartialBy,\n} from './internal/types.js'\n\nexport type TransactionEnvelopeEip1559<\n  signed extends boolean = boolean,\n  bigintType = bigint,\n  numberType = number,\n  type extends string = Type,\n> = Compute<\n  TransactionEnvelope.Base<type, signed, bigintType, numberType> & {\n    /** EIP-2930 Access List. */\n    accessList?: AccessList.AccessList | undefined\n    /** Total fee per gas in wei (gasPrice/baseFeePerGas + maxPriorityFeePerGas). */\n    maxFeePerGas?: bigintType | undefined\n    /** Max priority fee per gas (in wei). */\n    maxPriorityFeePerGas?: bigintType | undefined\n  }\n>\n\nexport type Rpc<signed extends boolean = boolean> = TransactionEnvelopeEip1559<\n  signed,\n  Hex.Hex,\n  Hex.Hex,\n  '0x2'\n>\n\nexport type Serialized = `${SerializedType}${string}`\n\nexport const serializedType = '0x02' as const\nexport type SerializedType = typeof serializedType\n\nexport type Signed = TransactionEnvelopeEip1559<true>\n\nexport const type = 'eip1559' as const\nexport type Type = typeof type\n\n/**\n * Asserts a {@link ox#TransactionEnvelopeEip1559.TransactionEnvelopeEip1559} is valid.\n *\n * @example\n * ```ts twoslash\n * import { TransactionEnvelopeEip1559, Value } from 'ox'\n *\n * TransactionEnvelopeEip1559.assert({\n *   maxFeePerGas: 2n ** 256n - 1n + 1n,\n *   chainId: 1,\n *   to: '******************************************',\n *   value: Value.fromEther('1'),\n * })\n * // @error: FeeCapTooHighError:\n * // @error: The fee cap (`masFeePerGas` = 115792089237316195423570985008687907853269984665640564039457584007913 gwei) cannot be\n * // @error: higher than the maximum allowed value (2^256-1).\n * ```\n *\n * @param envelope - The transaction envelope to assert.\n */\nexport function assert(\n  envelope: PartialBy<TransactionEnvelopeEip1559, 'type'>,\n) {\n  const { chainId, maxPriorityFeePerGas, maxFeePerGas, to } = envelope\n  if (chainId <= 0)\n    throw new TransactionEnvelope.InvalidChainIdError({ chainId })\n  if (to) Address.assert(to, { strict: false })\n  if (maxFeePerGas && BigInt(maxFeePerGas) > 2n ** 256n - 1n)\n    throw new TransactionEnvelope.FeeCapTooHighError({ feeCap: maxFeePerGas })\n  if (\n    maxPriorityFeePerGas &&\n    maxFeePerGas &&\n    maxPriorityFeePerGas > maxFeePerGas\n  )\n    throw new TransactionEnvelope.TipAboveFeeCapError({\n      maxFeePerGas,\n      maxPriorityFeePerGas,\n    })\n}\n\nexport declare namespace assert {\n  type ErrorType =\n    | Address.assert.ErrorType\n    | TransactionEnvelope.InvalidChainIdError\n    | TransactionEnvelope.FeeCapTooHighError\n    | TransactionEnvelope.TipAboveFeeCapError\n    | Errors.GlobalErrorType\n}\n\n/**\n * Deserializes a {@link ox#TransactionEnvelopeEip1559.TransactionEnvelopeEip1559} from its serialized form.\n *\n * @example\n * ```ts twoslash\n * import { TransactionEnvelopeEip1559 } from 'ox'\n *\n * const envelope = TransactionEnvelopeEip1559.deserialize('0x02ef0182031184773594008477359400809470997970c51812dc3a010c7d01b50e0d17dc79c8880de0b6b3a764000080c0')\n * // @log: {\n * // @log:   type: 'eip1559',\n * // @log:   nonce: 785n,\n * // @log:   maxFeePerGas: 2000000000n,\n * // @log:   gas: 1000000n,\n * // @log:   to: '******************************************',\n * // @log:   value: 1000000000000000000n,\n * // @log: }\n * ```\n *\n * @param serialized - The serialized transaction.\n * @returns Deserialized Transaction Envelope.\n */\nexport function deserialize(\n  serialized: Serialized,\n): Compute<TransactionEnvelopeEip1559> {\n  const transactionArray = Rlp.toHex(Hex.slice(serialized, 1))\n\n  const [\n    chainId,\n    nonce,\n    maxPriorityFeePerGas,\n    maxFeePerGas,\n    gas,\n    to,\n    value,\n    data,\n    accessList,\n    yParity,\n    r,\n    s,\n  ] = transactionArray as readonly Hex.Hex[]\n\n  if (!(transactionArray.length === 9 || transactionArray.length === 12))\n    throw new TransactionEnvelope.InvalidSerializedError({\n      attributes: {\n        chainId,\n        nonce,\n        maxPriorityFeePerGas,\n        maxFeePerGas,\n        gas,\n        to,\n        value,\n        data,\n        accessList,\n        ...(transactionArray.length > 9\n          ? {\n              yParity,\n              r,\n              s,\n            }\n          : {}),\n      },\n      serialized,\n      type,\n    })\n\n  let transaction = {\n    chainId: Number(chainId),\n    type,\n  } as TransactionEnvelopeEip1559\n  if (Hex.validate(to) && to !== '0x') transaction.to = to\n  if (Hex.validate(gas) && gas !== '0x') transaction.gas = BigInt(gas)\n  if (Hex.validate(data) && data !== '0x') transaction.data = data\n  if (Hex.validate(nonce) && nonce !== '0x') transaction.nonce = BigInt(nonce)\n  if (Hex.validate(value) && value !== '0x') transaction.value = BigInt(value)\n  if (Hex.validate(maxFeePerGas) && maxFeePerGas !== '0x')\n    transaction.maxFeePerGas = BigInt(maxFeePerGas)\n  if (Hex.validate(maxPriorityFeePerGas) && maxPriorityFeePerGas !== '0x')\n    transaction.maxPriorityFeePerGas = BigInt(maxPriorityFeePerGas)\n  if (accessList!.length !== 0 && accessList !== '0x')\n    transaction.accessList = AccessList.fromTupleList(accessList as any)\n\n  const signature =\n    r && s && yParity ? Signature.fromTuple([yParity, r, s]) : undefined\n  if (signature)\n    transaction = {\n      ...transaction,\n      ...signature,\n    } as TransactionEnvelopeEip1559\n\n  assert(transaction)\n\n  return transaction\n}\n\nexport declare namespace deserialize {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Converts an arbitrary transaction object into an EIP-1559 Transaction Envelope.\n *\n * @example\n * ```ts twoslash\n * import { TransactionEnvelopeEip1559, Value } from 'ox'\n *\n * const envelope = TransactionEnvelopeEip1559.from({\n *   chainId: 1,\n *   maxFeePerGas: Value.fromGwei('10'),\n *   maxPriorityFeePerGas: Value.fromGwei('1'),\n *   to: '******************************************',\n *   value: Value.fromEther('1'),\n * })\n * ```\n *\n * @example\n * ### Attaching Signatures\n *\n * It is possible to attach a `signature` to the transaction envelope.\n *\n * ```ts twoslash\n * import { Secp256k1, TransactionEnvelopeEip1559, Value } from 'ox'\n *\n * const envelope = TransactionEnvelopeEip1559.from({\n *   chainId: 1,\n *   maxFeePerGas: Value.fromGwei('10'),\n *   maxPriorityFeePerGas: Value.fromGwei('1'),\n *   to: '******************************************',\n *   value: Value.fromEther('1'),\n * })\n *\n * const signature = Secp256k1.sign({\n *   payload: TransactionEnvelopeEip1559.getSignPayload(envelope),\n *   privateKey: '0x...',\n * })\n *\n * const envelope_signed = TransactionEnvelopeEip1559.from(envelope, { // [!code focus]\n *   signature, // [!code focus]\n * }) // [!code focus]\n * // @log: {\n * // @log:   chainId: 1,\n * // @log:   maxFeePerGas: 10000000000n,\n * // @log:   maxPriorityFeePerGas: 1000000000n,\n * // @log:   to: '******************************************',\n * // @log:   type: 'eip1559',\n * // @log:   value: 1000000000000000000n,\n * // @log:   r: 125...n,\n * // @log:   s: 642...n,\n * // @log:   yParity: 0,\n * // @log: }\n * ```\n *\n * @example\n * ### From Serialized\n *\n * It is possible to instantiate an EIP-1559 Transaction Envelope from a {@link ox#TransactionEnvelopeEip1559.Serialized} value.\n *\n * ```ts twoslash\n * import { TransactionEnvelopeEip1559 } from 'ox'\n *\n * const envelope = TransactionEnvelopeEip1559.from('0x02f858018203118502540be4008504a817c800809470997970c51812dc3a010c7d01b50e0d17dc79c8880de0b6b3a764000080c08477359400e1a001627c687261b0e7f8638af1112efa8a77e23656f6e7945275b19e9deed80261')\n * // @log: {\n * // @log:   chainId: 1,\n * // @log:   maxFeePerGas: 10000000000n,\n * // @log:   maxPriorityFeePerGas: 1000000000n,\n * // @log:   to: '******************************************',\n * // @log:   type: 'eip1559',\n * // @log:   value: 1000000000000000000n,\n * // @log: }\n * ```\n *\n * @param envelope - The transaction object to convert.\n * @param options - Options.\n * @returns An EIP-1559 Transaction Envelope.\n */\nexport function from<\n  const envelope extends\n    | UnionPartialBy<TransactionEnvelopeEip1559, 'type'>\n    | Serialized,\n  const signature extends Signature.Signature | undefined = undefined,\n>(\n  envelope:\n    | envelope\n    | UnionPartialBy<TransactionEnvelopeEip1559, 'type'>\n    | Serialized,\n  options: from.Options<signature> = {},\n): from.ReturnType<envelope, signature> {\n  const { signature } = options\n\n  const envelope_ = (\n    typeof envelope === 'string' ? deserialize(envelope) : envelope\n  ) as TransactionEnvelopeEip1559\n\n  assert(envelope_)\n\n  return {\n    ...envelope_,\n    ...(signature ? Signature.from(signature) : {}),\n    type: 'eip1559',\n  } as never\n}\n\nexport declare namespace from {\n  type Options<signature extends Signature.Signature | undefined = undefined> =\n    {\n      signature?: signature | Signature.Signature | undefined\n    }\n\n  type ReturnType<\n    envelope extends\n      | UnionPartialBy<TransactionEnvelopeEip1559, 'type'>\n      | Hex.Hex = TransactionEnvelopeEip1559 | Hex.Hex,\n    signature extends Signature.Signature | undefined = undefined,\n  > = Compute<\n    envelope extends Hex.Hex\n      ? TransactionEnvelopeEip1559\n      : Assign<\n          envelope,\n          (signature extends Signature.Signature ? Readonly<signature> : {}) & {\n            readonly type: 'eip1559'\n          }\n        >\n  >\n\n  type ErrorType =\n    | deserialize.ErrorType\n    | assert.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Returns the payload to sign for a {@link ox#TransactionEnvelopeEip1559.TransactionEnvelopeEip1559}.\n *\n * @example\n * The example below demonstrates how to compute the sign payload which can be used\n * with ECDSA signing utilities like {@link ox#Secp256k1.(sign:function)}.\n *\n * ```ts twoslash\n * import { Secp256k1, TransactionEnvelopeEip1559 } from 'ox'\n *\n * const envelope = TransactionEnvelopeEip1559.from({\n *   chainId: 1,\n *   nonce: 0n,\n *   maxFeePerGas: 1000000000n,\n *   gas: 21000n,\n *   to: '******************************************',\n *   value: 1000000000000000000n,\n * })\n *\n * const payload = TransactionEnvelopeEip1559.getSignPayload(envelope) // [!code focus]\n * // @log: '0x...'\n *\n * const signature = Secp256k1.sign({ payload, privateKey: '0x...' })\n * ```\n *\n * @param envelope - The transaction envelope to get the sign payload for.\n * @returns The sign payload.\n */\nexport function getSignPayload(\n  envelope: TransactionEnvelopeEip1559,\n): getSignPayload.ReturnType {\n  return hash(envelope, { presign: true })\n}\n\nexport declare namespace getSignPayload {\n  type ReturnType = Hex.Hex\n\n  type ErrorType = hash.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Hashes a {@link ox#TransactionEnvelopeEip1559.TransactionEnvelopeEip1559}. This is the \"transaction hash\".\n *\n * @example\n * ```ts twoslash\n * import { Secp256k1, TransactionEnvelopeEip1559 } from 'ox'\n *\n * const envelope = TransactionEnvelopeEip1559.from({\n *   chainId: 1,\n *   nonce: 0n,\n *   maxFeePerGas: 1000000000n,\n *   gas: 21000n,\n *   to: '******************************************',\n *   value: 1000000000000000000n,\n * })\n *\n * const signature = Secp256k1.sign({\n *   payload: TransactionEnvelopeEip1559.getSignPayload(envelope),\n *   privateKey: '0x...'\n * })\n *\n * const envelope_signed = TransactionEnvelopeEip1559.from(envelope, { signature })\n *\n * const hash = TransactionEnvelopeEip1559.hash(envelope_signed) // [!code focus]\n * ```\n *\n * @param envelope - The EIP-1559 Transaction Envelope to hash.\n * @param options - Options.\n * @returns The hash of the transaction envelope.\n */\nexport function hash<presign extends boolean = false>(\n  envelope: TransactionEnvelopeEip1559<presign extends true ? false : true>,\n  options: hash.Options<presign> = {},\n): hash.ReturnType {\n  const { presign } = options\n  return Hash.keccak256(\n    serialize({\n      ...envelope,\n      ...(presign\n        ? {\n            r: undefined,\n            s: undefined,\n            yParity: undefined,\n            v: undefined,\n          }\n        : {}),\n    }),\n  )\n}\n\nexport declare namespace hash {\n  type Options<presign extends boolean = false> = {\n    /** Whether to hash this transaction for signing. @default false */\n    presign?: presign | boolean | undefined\n  }\n\n  type ReturnType = Hex.Hex\n\n  type ErrorType =\n    | Hash.keccak256.ErrorType\n    | serialize.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Serializes a {@link ox#TransactionEnvelopeEip1559.TransactionEnvelopeEip1559}.\n *\n * @example\n * ```ts twoslash\n * import { TransactionEnvelopeEip1559, Value } from 'ox'\n *\n * const envelope = TransactionEnvelopeEip1559.from({\n *   chainId: 1,\n *   maxFeePerGas: Value.fromGwei('10'),\n *   maxPriorityFeePerGas: Value.fromGwei('1'),\n *   to: '******************************************',\n *   value: Value.fromEther('1'),\n * })\n *\n * const serialized = TransactionEnvelopeEip1559.serialize(envelope) // [!code focus]\n * ```\n *\n * @example\n * ### Attaching Signatures\n *\n * It is possible to attach a `signature` to the serialized Transaction Envelope.\n *\n * ```ts twoslash\n * import { Secp256k1, TransactionEnvelopeEip1559, Value } from 'ox'\n *\n * const envelope = TransactionEnvelopeEip1559.from({\n *   chainId: 1,\n *   maxFeePerGas: Value.fromGwei('10'),\n *   maxPriorityFeePerGas: Value.fromGwei('1'),\n *   to: '******************************************',\n *   value: Value.fromEther('1'),\n * })\n *\n * const signature = Secp256k1.sign({\n *   payload: TransactionEnvelopeEip1559.getSignPayload(envelope),\n *   privateKey: '0x...',\n * })\n *\n * const serialized = TransactionEnvelopeEip1559.serialize(envelope, { // [!code focus]\n *   signature, // [!code focus]\n * }) // [!code focus]\n *\n * // ... send `serialized` transaction to JSON-RPC `eth_sendRawTransaction`\n * ```\n *\n * @param envelope - The Transaction Envelope to serialize.\n * @param options - Options.\n * @returns The serialized Transaction Envelope.\n */\nexport function serialize(\n  envelope: PartialBy<TransactionEnvelopeEip1559, 'type'>,\n  options: serialize.Options = {},\n): Serialized {\n  const {\n    chainId,\n    gas,\n    nonce,\n    to,\n    value,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    accessList,\n    data,\n    input,\n  } = envelope\n\n  assert(envelope)\n\n  const accessTupleList = AccessList.toTupleList(accessList)\n\n  const signature = Signature.extract(options.signature || envelope)\n\n  const serialized = [\n    Hex.fromNumber(chainId),\n    nonce ? Hex.fromNumber(nonce) : '0x',\n    maxPriorityFeePerGas ? Hex.fromNumber(maxPriorityFeePerGas) : '0x',\n    maxFeePerGas ? Hex.fromNumber(maxFeePerGas) : '0x',\n    gas ? Hex.fromNumber(gas) : '0x',\n    to ?? '0x',\n    value ? Hex.fromNumber(value) : '0x',\n    data ?? input ?? '0x',\n    accessTupleList,\n    ...(signature ? Signature.toTuple(signature) : []),\n  ]\n\n  return Hex.concat(serializedType, Rlp.fromHex(serialized)) as Serialized\n}\n\nexport declare namespace serialize {\n  type Options = {\n    /** Signature to append to the serialized Transaction Envelope. */\n    signature?: Signature.Signature | undefined\n  }\n\n  type ErrorType =\n    | assert.ErrorType\n    | Hex.fromNumber.ErrorType\n    | Signature.toTuple.ErrorType\n    | Hex.concat.ErrorType\n    | Rlp.fromHex.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Converts an {@link ox#TransactionEnvelopeEip1559.TransactionEnvelopeEip1559} to an {@link ox#TransactionEnvelopeEip1559.Rpc}.\n *\n * @example\n * ```ts twoslash\n * import { RpcRequest, TransactionEnvelopeEip1559, Value } from 'ox'\n *\n * const envelope = TransactionEnvelopeEip1559.from({\n *   chainId: 1,\n *   nonce: 0n,\n *   gas: 21000n,\n *   to: '******************************************',\n *   value: Value.fromEther('1'),\n * })\n *\n * const envelope_rpc = TransactionEnvelopeEip1559.toRpc(envelope) // [!code focus]\n *\n * const request = RpcRequest.from({\n *   id: 0,\n *   method: 'eth_sendTransaction',\n *   params: [envelope_rpc],\n * })\n * ```\n *\n * @param envelope - The EIP-1559 transaction envelope to convert.\n * @returns An RPC-formatted EIP-1559 transaction envelope.\n */\nexport function toRpc(envelope: Omit<TransactionEnvelopeEip1559, 'type'>): Rpc {\n  const signature = Signature.extract(envelope)\n\n  return {\n    ...envelope,\n    chainId: Hex.fromNumber(envelope.chainId),\n    data: envelope.data ?? envelope.input,\n    type: '0x2',\n    ...(typeof envelope.gas === 'bigint'\n      ? { gas: Hex.fromNumber(envelope.gas) }\n      : {}),\n    ...(typeof envelope.nonce === 'bigint'\n      ? { nonce: Hex.fromNumber(envelope.nonce) }\n      : {}),\n    ...(typeof envelope.value === 'bigint'\n      ? { value: Hex.fromNumber(envelope.value) }\n      : {}),\n    ...(typeof envelope.maxFeePerGas === 'bigint'\n      ? { maxFeePerGas: Hex.fromNumber(envelope.maxFeePerGas) }\n      : {}),\n    ...(typeof envelope.maxPriorityFeePerGas === 'bigint'\n      ? {\n          maxPriorityFeePerGas: Hex.fromNumber(envelope.maxPriorityFeePerGas),\n        }\n      : {}),\n    ...(signature ? Signature.toRpc(signature) : {}),\n  } as never\n}\n\nexport declare namespace toRpc {\n  export type ErrorType = Signature.extract.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Validates a {@link ox#TransactionEnvelopeEip1559.TransactionEnvelopeEip1559}. Returns `true` if the envelope is valid, `false` otherwise.\n *\n * @example\n * ```ts twoslash\n * import { TransactionEnvelopeEip1559, Value } from 'ox'\n *\n * const valid = TransactionEnvelopeEip1559.assert({\n *   maxFeePerGas: 2n ** 256n - 1n + 1n,\n *   chainId: 1,\n *   to: '******************************************',\n *   value: Value.fromEther('1'),\n * })\n * // @log: false\n * ```\n *\n * @param envelope - The transaction envelope to validate.\n */\nexport function validate(\n  envelope: PartialBy<TransactionEnvelopeEip1559, 'type'>,\n) {\n  try {\n    assert(envelope)\n    return true\n  } catch {\n    return false\n  }\n}\n\nexport declare namespace validate {\n  type ErrorType = Errors.GlobalErrorType\n}\n", "import * as Address from './Address.js'\nimport * as Errors from './Errors.js'\nimport * as Hash from './Hash.js'\nimport * as Hex from './Hex.js'\nimport type { Compute, Mutable } from './internal/types.js'\n\nexport type AccessList = Compute<readonly Item[]>\n\nexport type Item = Compute<{\n  address: Address.Address\n  storageKeys: readonly Hex.Hex[]\n}>\n\nexport type ItemTuple = Compute<\n  [address: Address.Address, storageKeys: readonly Hex.Hex[]]\n>\n\nexport type Tuple = readonly ItemTuple[]\n\n/**\n * Converts a list of Access List tuples into a object-formatted list.\n *\n * @example\n * ```ts twoslash\n * import { AccessList } from 'ox'\n *\n * const accessList = AccessList.fromTupleList([\n *   [\n *     '******************************************',\n *     [\n *       '******************************************000000000000000000000001',\n *       '0x60fdd29ff912ce880cd3edaf9f932dc61d3dae823ea77e0323f94adb9f6a72fe',\n *     ],\n *   ],\n * ])\n * // @log: [\n * // @log:   {\n * // @log:     address: '******************************************',\n * // @log:     storageKeys: [\n * // @log:       '******************************************000000000000000000000001',\n * // @log:       '0x60fdd29ff912ce880cd3edaf9f932dc61d3dae823ea77e0323f94adb9f6a72fe',\n * // @log:     ],\n * // @log:   },\n * // @log: ]\n * ```\n *\n * @param accessList - List of tuples.\n * @returns Access list.\n */\nexport function fromTupleList(accessList: Tuple): AccessList {\n  const list: Mutable<AccessList> = []\n  for (let i = 0; i < accessList.length; i++) {\n    const [address, storageKeys] = accessList[i] as [Hex.Hex, Hex.Hex[]]\n\n    if (address) Address.assert(address, { strict: false })\n\n    list.push({\n      address: address,\n      storageKeys: storageKeys.map((key) =>\n        Hash.validate(key) ? key : Hex.trimLeft(key),\n      ),\n    })\n  }\n  return list\n}\n\n/**\n * Converts a structured Access List into a list of tuples.\n *\n * @example\n * ```ts twoslash\n * import { AccessList } from 'ox'\n *\n * const accessList = AccessList.toTupleList([\n *   {\n *     address: '******************************************',\n *     storageKeys: [\n *       '******************************************000000000000000000000001',\n *       '0x60fdd29ff912ce880cd3edaf9f932dc61d3dae823ea77e0323f94adb9f6a72fe'],\n *   },\n * ])\n * // @log: [\n * // @log:   [\n * // @log:     '******************************************',\n * // @log:     [\n * // @log:       '******************************************000000000000000000000001',\n * // @log:       '0x60fdd29ff912ce880cd3edaf9f932dc61d3dae823ea77e0323f94adb9f6a72fe',\n * // @log:     ],\n * // @log:   ],\n * // @log: ]\n * ```\n *\n * @param accessList - Access list.\n * @returns List of tuples.\n */\nexport function toTupleList(\n  accessList?: AccessList | undefined,\n): Compute<Tuple> {\n  if (!accessList || accessList.length === 0) return []\n\n  const tuple: Mutable<Tuple> = []\n  for (const { address, storageKeys } of accessList) {\n    for (let j = 0; j < storageKeys.length; j++)\n      if (Hex.size(storageKeys[j]!) !== 32)\n        throw new InvalidStorageKeySizeError({\n          storageKey: storageKeys[j]!,\n        })\n\n    if (address) Address.assert(address, { strict: false })\n\n    tuple.push([address, storageKeys])\n  }\n  return tuple\n}\n\n/** Thrown when the size of a storage key is invalid. */\nexport class InvalidStorageKeySizeError extends Errors.BaseError {\n  override readonly name = 'AccessList.InvalidStorageKeySizeError'\n  constructor({ storageKey }: { storageKey: Hex.Hex }) {\n    super(\n      `Size for storage key \"${storageKey}\" is invalid. Expected 32 bytes. Got ${Hex.size(storageKey)} bytes.`,\n    )\n  }\n}\n", "import * as Bytes from './Bytes.js'\nimport * as Errors from './Errors.js'\nimport * as Hex from './Hex.js'\nimport * as Cursor from './internal/cursor.js'\nimport type { ExactPartial, RecursiveArray } from './internal/types.js'\n\n/**\n * Decodes a Recursive-Length Prefix (RLP) value into a {@link ox#Bytes.Bytes} value.\n *\n * @example\n * ```ts twoslash\n * import { Rlp } from 'ox'\n * Rlp.toBytes('0x8b68656c6c6f20776f726c64')\n * // Uint8Array([139, 104, 101, 108, 108, 111,  32, 119, 111, 114, 108, 100])\n * ```\n *\n * @param value - The value to decode.\n * @returns The decoded {@link ox#Bytes.Bytes} value.\n */\nexport function toBytes(\n  value: Bytes.Bytes | Hex.Hex,\n): RecursiveArray<Bytes.Bytes> {\n  return to(value, 'Bytes')\n}\n\nexport declare namespace toBytes {\n  type ErrorType = to.ErrorType\n}\n\n/**\n * Decodes a Recursive-Length Prefix (RLP) value into a {@link ox#Hex.Hex} value.\n *\n * @example\n * ```ts twoslash\n * import { Rlp } from 'ox'\n * Rlp.toHex('0x8b68656c6c6f20776f726c64')\n * // 0x68656c6c6f20776f726c64\n * ```\n *\n * @param value - The value to decode.\n * @returns The decoded {@link ox#Hex.Hex} value.\n */\nexport function toHex(value: Bytes.Bytes | Hex.Hex): RecursiveArray<Hex.Hex> {\n  return to(value, 'Hex')\n}\n\nexport declare namespace toHex {\n  type ErrorType = to.ErrorType\n}\n\n/////////////////////////////////////////////////////////////////////////////////\n// Internal\n/////////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport function to<\n  value extends Bytes.Bytes | Hex.Hex,\n  to extends 'Hex' | 'Bytes',\n>(value: value, to: to | 'Hex' | 'Bytes'): to.ReturnType<to> {\n  const to_ = to ?? (typeof value === 'string' ? 'Hex' : 'Bytes')\n\n  const bytes = (() => {\n    if (typeof value === 'string') {\n      if (value.length > 3 && value.length % 2 !== 0)\n        throw new Hex.InvalidLengthError(value)\n      return Bytes.fromHex(value)\n    }\n    return value as Bytes.Bytes\n  })()\n\n  const cursor = Cursor.create(bytes, {\n    recursiveReadLimit: Number.POSITIVE_INFINITY,\n  })\n  const result = decodeRlpCursor(cursor, to_)\n\n  return result as to.ReturnType<to>\n}\n\n/** @internal */\nexport declare namespace to {\n  type ReturnType<to extends 'Hex' | 'Bytes' = 'Hex' | 'Bytes'> =\n    | (to extends 'Bytes' ? RecursiveArray<Bytes.Bytes> : never)\n    | (to extends 'Hex' ? RecursiveArray<Hex.Hex> : never)\n\n  type ErrorType =\n    | Bytes.fromHex.ErrorType\n    | decodeRlpCursor.ErrorType\n    | Cursor.create.ErrorType\n    | Hex.InvalidLengthError\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\n\n/** @internal */\nexport function decodeRlpCursor<to extends 'Hex' | 'Bytes' = 'Hex'>(\n  cursor: Cursor.Cursor,\n  to: to | 'Hex' | 'Bytes' | undefined = 'Hex',\n): decodeRlpCursor.ReturnType<to> {\n  if (cursor.bytes.length === 0)\n    return (\n      to === 'Hex' ? Hex.fromBytes(cursor.bytes) : cursor.bytes\n    ) as decodeRlpCursor.ReturnType<to>\n\n  const prefix = cursor.readByte()\n  if (prefix < 0x80) cursor.decrementPosition(1)\n\n  // bytes\n  if (prefix < 0xc0) {\n    const length = readLength(cursor, prefix, 0x80)\n    const bytes = cursor.readBytes(length)\n    return (\n      to === 'Hex' ? Hex.fromBytes(bytes) : bytes\n    ) as decodeRlpCursor.ReturnType<to>\n  }\n\n  // list\n  const length = readLength(cursor, prefix, 0xc0)\n  return readList(cursor, length, to) as {} as decodeRlpCursor.ReturnType<to>\n}\n\n/** @internal */\nexport declare namespace decodeRlpCursor {\n  type ReturnType<to extends 'Hex' | 'Bytes' = 'Hex'> = to.ReturnType<to>\n  type ErrorType =\n    | Hex.fromBytes.ErrorType\n    | readLength.ErrorType\n    | readList.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function readLength(\n  cursor: Cursor.Cursor,\n  prefix: number,\n  offset: number,\n) {\n  if (offset === 0x80 && prefix < 0x80) return 1\n  if (prefix <= offset + 55) return prefix - offset\n  if (prefix === offset + 55 + 1) return cursor.readUint8()\n  if (prefix === offset + 55 + 2) return cursor.readUint16()\n  if (prefix === offset + 55 + 3) return cursor.readUint24()\n  if (prefix === offset + 55 + 4) return cursor.readUint32()\n  throw new Errors.BaseError('Invalid RLP prefix')\n}\n\n/** @internal */\nexport declare namespace readLength {\n  type ErrorType = Errors.BaseError | Errors.GlobalErrorType\n}\n\n/** @internal */\nexport function readList<to extends 'Hex' | 'Bytes'>(\n  cursor: Cursor.Cursor,\n  length: number,\n  to: to | 'Hex' | 'Bytes',\n) {\n  const position = cursor.position\n  const value: decodeRlpCursor.ReturnType<to>[] = []\n  while (cursor.position - position < length)\n    value.push(decodeRlpCursor(cursor, to))\n  return value\n}\n\n/** @internal */\nexport declare namespace readList {\n  type ErrorType = Errors.GlobalErrorType\n}\n\ntype Encodable = {\n  length: number\n  encode(cursor: Cursor.Cursor): void\n}\n\n/**\n * Encodes a {@link ox#Bytes.Bytes} or {@link ox#Hex.Hex} value into a Recursive-Length Prefix (RLP) value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes, Rlp } from 'ox'\n *\n * Rlp.from('0x68656c6c6f20776f726c64', { as: 'Hex' })\n * // @log: 0x8b68656c6c6f20776f726c64\n *\n * Rlp.from(Bytes.from([139, 104, 101, 108, 108, 111,  32, 119, 111, 114, 108, 100]), { as: 'Bytes' })\n * // @log: Uint8Array([104, 101, 108, 108, 111,  32, 119, 111, 114, 108, 100])\n * ```\n *\n * @param value - The {@link ox#Bytes.Bytes} or {@link ox#Hex.Hex} value to encode.\n * @param options - Options.\n * @returns The RLP value.\n */\nexport function from<as extends 'Hex' | 'Bytes'>(\n  value: RecursiveArray<Bytes.Bytes> | RecursiveArray<Hex.Hex>,\n  options: from.Options<as>,\n): from.ReturnType<as> {\n  const { as } = options\n\n  const encodable = getEncodable(value)\n  const cursor = Cursor.create(new Uint8Array(encodable.length))\n  encodable.encode(cursor)\n\n  if (as === 'Hex') return Hex.fromBytes(cursor.bytes) as from.ReturnType<as>\n  return cursor.bytes as from.ReturnType<as>\n}\n\nexport declare namespace from {\n  type Options<as extends 'Hex' | 'Bytes'> = {\n    /** The type to convert the RLP value to. */\n    as: as | 'Hex' | 'Bytes'\n  }\n\n  type ReturnType<as extends 'Hex' | 'Bytes'> =\n    | (as extends 'Bytes' ? Bytes.Bytes : never)\n    | (as extends 'Hex' ? Hex.Hex : never)\n\n  type ErrorType =\n    | Cursor.create.ErrorType\n    | Hex.fromBytes.ErrorType\n    | Bytes.fromHex.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Encodes a {@link ox#Bytes.Bytes} value into a Recursive-Length Prefix (RLP) value.\n *\n * @example\n * ```ts twoslash\n * import { Bytes, Rlp } from 'ox'\n *\n * Rlp.fromBytes(Bytes.from([139, 104, 101, 108, 108, 111,  32, 119, 111, 114, 108, 100]))\n * // @log: Uint8Array([104, 101, 108, 108, 111,  32, 119, 111, 114, 108, 100])\n * ```\n *\n * @param bytes - The {@link ox#Bytes.Bytes} value to encode.\n * @param options - Options.\n * @returns The RLP value.\n */\nexport function fromBytes<as extends 'Hex' | 'Bytes' = 'Bytes'>(\n  bytes: RecursiveArray<Bytes.Bytes>,\n  options: fromBytes.Options<as> = {},\n): fromBytes.ReturnType<as> {\n  const { as = 'Bytes' } = options\n  return from(bytes, { as }) as never\n}\n\nexport declare namespace fromBytes {\n  type Options<as extends 'Hex' | 'Bytes' = 'Bytes'> = ExactPartial<\n    from.Options<as>\n  >\n\n  type ReturnType<as extends 'Hex' | 'Bytes' = 'Bytes'> = from.ReturnType<as>\n\n  type ErrorType = from.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Encodes a {@link ox#Hex.Hex} value into a Recursive-Length Prefix (RLP) value.\n *\n * @example\n * ```ts twoslash\n * import { Rlp } from 'ox'\n *\n * Rlp.fromHex('0x68656c6c6f20776f726c64')\n * // @log: 0x8b68656c6c6f20776f726c64\n * ```\n *\n * @param hex - The {@link ox#Hex.Hex} value to encode.\n * @param options - Options.\n * @returns The RLP value.\n */\nexport function fromHex<as extends 'Hex' | 'Bytes' = 'Hex'>(\n  hex: RecursiveArray<Hex.Hex>,\n  options: fromHex.Options<as> = {},\n): fromHex.ReturnType<as> {\n  const { as = 'Hex' } = options\n  return from(hex, { as }) as never\n}\n\nexport declare namespace fromHex {\n  type Options<as extends 'Hex' | 'Bytes' = 'Hex'> = ExactPartial<\n    from.Options<as>\n  >\n\n  type ReturnType<as extends 'Hex' | 'Bytes' = 'Hex'> = from.ReturnType<as>\n\n  type ErrorType = from.ErrorType | Errors.GlobalErrorType\n}\n\n/////////////////////////////////////////////////////////////////////////////////\n// Internal\n/////////////////////////////////////////////////////////////////////////////////\n\nfunction getEncodable(\n  bytes: RecursiveArray<Bytes.Bytes> | RecursiveArray<Hex.Hex>,\n): Encodable {\n  if (Array.isArray(bytes))\n    return getEncodableList(bytes.map((x) => getEncodable(x)))\n  return getEncodableBytes(bytes as any)\n}\n\nfunction getEncodableList(list: Encodable[]): Encodable {\n  const bodyLength = list.reduce((acc, x) => acc + x.length, 0)\n\n  const sizeOfBodyLength = getSizeOfLength(bodyLength)\n  const length = (() => {\n    if (bodyLength <= 55) return 1 + bodyLength\n    return 1 + sizeOfBodyLength + bodyLength\n  })()\n\n  return {\n    length,\n    encode(cursor: Cursor.Cursor) {\n      if (bodyLength <= 55) {\n        cursor.pushByte(0xc0 + bodyLength)\n      } else {\n        cursor.pushByte(0xc0 + 55 + sizeOfBodyLength)\n        if (sizeOfBodyLength === 1) cursor.pushUint8(bodyLength)\n        else if (sizeOfBodyLength === 2) cursor.pushUint16(bodyLength)\n        else if (sizeOfBodyLength === 3) cursor.pushUint24(bodyLength)\n        else cursor.pushUint32(bodyLength)\n      }\n      for (const { encode } of list) {\n        encode(cursor)\n      }\n    },\n  }\n}\n\nfunction getEncodableBytes(bytesOrHex: Bytes.Bytes | Hex.Hex): Encodable {\n  const bytes =\n    typeof bytesOrHex === 'string' ? Bytes.fromHex(bytesOrHex) : bytesOrHex\n\n  const sizeOfBytesLength = getSizeOfLength(bytes.length)\n  const length = (() => {\n    if (bytes.length === 1 && bytes[0]! < 0x80) return 1\n    if (bytes.length <= 55) return 1 + bytes.length\n    return 1 + sizeOfBytesLength + bytes.length\n  })()\n\n  return {\n    length,\n    encode(cursor: Cursor.Cursor) {\n      if (bytes.length === 1 && bytes[0]! < 0x80) {\n        cursor.pushBytes(bytes)\n      } else if (bytes.length <= 55) {\n        cursor.pushByte(0x80 + bytes.length)\n        cursor.pushBytes(bytes)\n      } else {\n        cursor.pushByte(0x80 + 55 + sizeOfBytesLength)\n        if (sizeOfBytesLength === 1) cursor.pushUint8(bytes.length)\n        else if (sizeOfBytesLength === 2) cursor.pushUint16(bytes.length)\n        else if (sizeOfBytesLength === 3) cursor.pushUint24(bytes.length)\n        else cursor.pushUint32(bytes.length)\n        cursor.pushBytes(bytes)\n      }\n    },\n  }\n}\n\nfunction getSizeOfLength(length: number) {\n  if (length < 2 ** 8) return 1\n  if (length < 2 ** 16) return 2\n  if (length < 2 ** 24) return 3\n  if (length < 2 ** 32) return 4\n  throw new Errors.BaseError('Length is too large.')\n}\n", "import type * as Address from './Address.js'\nimport * as Errors from './Errors.js'\nimport type * as Hex from './Hex.js'\nimport * as Value from './Value.js'\nimport type { Compute } from './internal/types.js'\n\n/** Base type for a Transaction Envelope. Transaction Envelopes inherit this type. */\nexport type Base<\n  type extends string = string,\n  signed extends boolean = boolean,\n  bigintType = bigint,\n  numberType = number,\n> = Compute<\n  {\n    /** EIP-155 Chain ID. */\n    chainId: numberType\n    /** Contract code or a hashed method call with encoded args */\n    data?: Hex.Hex | undefined\n    /** @alias `data` – added for TransactionEnvelope - Transaction compatibility. */\n    input?: Hex.Hex | undefined\n    /** Sender of the transaction. */\n    from?: Address.Address | undefined\n    /** Gas provided for transaction execution */\n    gas?: bigintType | undefined\n    /** Unique number identifying this transaction */\n    nonce?: bigintType | undefined\n    /** Transaction recipient */\n    to?: Address.Address | null | undefined\n    /** Transaction type */\n    type: type\n    /** Value in wei sent with this transaction */\n    value?: bigintType | undefined\n    /** ECDSA signature r. */\n    r?: bigintType | undefined\n    /** ECDSA signature s. */\n    s?: bigintType | undefined\n    /** ECDSA signature yParity. */\n    yParity?: numberType | undefined\n    /** @deprecated ECDSA signature v (for backwards compatibility). */\n    v?: numberType | undefined\n  } & (signed extends true ? { r: bigintType; s: bigintType } : {})\n>\n\n/** RPC representation of a {@link ox#(TransactionEnvelope:namespace).Base}. */\nexport type BaseRpc<\n  type extends string = string,\n  signed extends boolean = boolean,\n> = Base<type, signed, Hex.Hex, Hex.Hex>\n\n/** Signed representation of a {@link ox#(TransactionEnvelope:namespace).Base}. */\nexport type BaseSigned<type extends string = string> = Base<type, true>\n\n/**\n * Thrown when a fee cap is too high.\n *\n * @example\n * ```ts twoslash\n * import { TransactionEnvelopeEip1559 } from 'ox'\n *\n * TransactionEnvelopeEip1559.assert({\n *   maxFeePerGas: 2n ** 256n - 1n + 1n,\n *   chainId: 1,\n * })\n * // @error: TransactionEnvelope.FeeCapTooHighError: The fee cap (`maxFeePerGas`/`maxPriorityFeePerGas` = 115792089237316195423570985008687907853269984665640564039457584007913.129639936 gwei) cannot be higher than the maximum allowed value (2^256-1).\n * ```\n */\nexport class FeeCapTooHighError extends Errors.BaseError {\n  override readonly name = 'TransactionEnvelope.FeeCapTooHighError'\n  constructor({\n    feeCap,\n  }: {\n    feeCap?: bigint | undefined\n  } = {}) {\n    super(\n      `The fee cap (\\`maxFeePerGas\\`/\\`maxPriorityFeePerGas\\`${\n        feeCap ? ` = ${Value.formatGwei(feeCap)} gwei` : ''\n      }) cannot be higher than the maximum allowed value (2^256-1).`,\n    )\n  }\n}\n\n/**\n * Thrown when a gas price is too high.\n *\n * @example\n * ```ts twoslash\n * import { TransactionEnvelopeLegacy } from 'ox'\n *\n * TransactionEnvelopeLegacy.assert({\n *   gasPrice: 2n ** 256n - 1n + 1n,\n *   chainId: 1,\n * })\n * // @error: TransactionEnvelope.GasPriceTooHighError: The gas price (`gasPrice` = 115792089237316195423570985008687907853269984665640564039457584007913.129639936 gwei) cannot be higher than the maximum allowed value (2^256-1).\n * ```\n */\nexport class GasPriceTooHighError extends Errors.BaseError {\n  override readonly name = 'TransactionEnvelope.GasPriceTooHighError'\n  constructor({\n    gasPrice,\n  }: {\n    gasPrice?: bigint | undefined\n  } = {}) {\n    super(\n      `The gas price (\\`gasPrice\\`${\n        gasPrice ? ` = ${Value.formatGwei(gasPrice)} gwei` : ''\n      }) cannot be higher than the maximum allowed value (2^256-1).`,\n    )\n  }\n}\n\n/**\n * Thrown when a chain ID is invalid.\n *\n * @example\n * ```ts twoslash\n * import { TransactionEnvelopeEip1559 } from 'ox'\n *\n * TransactionEnvelopeEip1559.assert({ chainId: 0 })\n * // @error: TransactionEnvelope.InvalidChainIdError: Chain ID \"0\" is invalid.\n * ```\n */\nexport class InvalidChainIdError extends Errors.BaseError {\n  override readonly name = 'TransactionEnvelope.InvalidChainIdError'\n  constructor({ chainId }: { chainId?: number | undefined }) {\n    super(\n      typeof chainId !== 'undefined'\n        ? `Chain ID \"${chainId}\" is invalid.`\n        : 'Chain ID is invalid.',\n    )\n  }\n}\n\n/**\n * Thrown when a serialized transaction is invalid.\n *\n * @example\n * ```ts twoslash\n * import { TransactionEnvelopeEip1559 } from 'ox'\n *\n * TransactionEnvelopeEip1559.deserialize('0x02c0')\n * // @error: TransactionEnvelope.InvalidSerializedError: Invalid serialized transaction of type \"eip1559\" was provided.\n * // @error: Serialized Transaction: \"0x02c0\"\n * // @error: Missing Attributes: chainId, nonce, maxPriorityFeePerGas, maxFeePerGas, gas, to, value, data, accessList\n * ```\n */\nexport class InvalidSerializedError extends Errors.BaseError {\n  override readonly name = 'TransactionEnvelope.InvalidSerializedError'\n  constructor({\n    attributes,\n    serialized,\n    type,\n  }: {\n    attributes: Record<string, unknown>\n    serialized: Hex.Hex\n    type: string\n  }) {\n    const missing = Object.entries(attributes)\n      .map(([key, value]) => (typeof value === 'undefined' ? key : undefined))\n      .filter(Boolean)\n    super(`Invalid serialized transaction of type \"${type}\" was provided.`, {\n      metaMessages: [\n        `Serialized Transaction: \"${serialized}\"`,\n        missing.length > 0 ? `Missing Attributes: ${missing.join(', ')}` : '',\n      ].filter(Boolean),\n    })\n  }\n}\n\n/**\n * Thrown when a tip is higher than a fee cap.\n *\n * @example\n * ```ts twoslash\n * import { TransactionEnvelopeEip1559 } from 'ox'\n *\n * TransactionEnvelopeEip1559.assert({\n *   chainId: 1,\n *   maxFeePerGas: 10n,\n *   maxPriorityFeePerGas: 11n,\n * })\n * // @error: TransactionEnvelope.TipAboveFeeCapError: The provided tip (`maxPriorityFeePerGas` = 11 gwei) cannot be higher than the fee cap (`maxFeePerGas` = 10 gwei).\n * ```\n */\nexport class TipAboveFeeCapError extends Errors.BaseError {\n  override readonly name = 'TransactionEnvelope.TipAboveFeeCapError'\n  constructor({\n    maxPriorityFeePerGas,\n    maxFeePerGas,\n  }: {\n    maxPriorityFeePerGas?: bigint | undefined\n    maxFeePerGas?: bigint | undefined\n  } = {}) {\n    super(\n      [\n        `The provided tip (\\`maxPriorityFeePerGas\\`${\n          maxPriorityFeePerGas\n            ? ` = ${Value.formatGwei(maxPriorityFeePerGas)} gwei`\n            : ''\n        }) cannot be higher than the fee cap (\\`maxFeePerGas\\`${\n          maxFeePerGas ? ` = ${Value.formatGwei(maxFeePerGas)} gwei` : ''\n        }).`,\n      ].join('\\n'),\n    )\n  }\n}\n", "import * as AccessList from './AccessList.js'\nimport * as Address from './Address.js'\nimport type * as Errors from './Errors.js'\nimport * as Hash from './Hash.js'\nimport * as Hex from './Hex.js'\nimport * as Rlp from './Rlp.js'\nimport * as Signature from './Signature.js'\nimport * as TransactionEnvelope from './TransactionEnvelope.js'\nimport type {\n  Assign,\n  Compute,\n  PartialBy,\n  UnionPartialBy,\n} from './internal/types.js'\n\nexport type TransactionEnvelopeEip2930<\n  signed extends boolean = boolean,\n  bigintType = bigint,\n  numberType = number,\n  type extends string = Type,\n> = Compute<\n  TransactionEnvelope.Base<type, signed, bigintType, numberType> & {\n    /** EIP-2930 Access List. */\n    accessList?: AccessList.AccessList | undefined\n    /** Base fee per gas. */\n    gasPrice?: bigintType | undefined\n  }\n>\n\nexport type Rpc<signed extends boolean = boolean> = TransactionEnvelopeEip2930<\n  signed,\n  Hex.Hex,\n  Hex.Hex,\n  '0x1'\n>\n\nexport type Serialized = `${SerializedType}${string}`\n\nexport const serializedType = '0x01' as const\nexport type SerializedType = typeof serializedType\n\nexport type Signed = TransactionEnvelopeEip2930<true>\n\nexport const type = 'eip2930' as const\nexport type Type = typeof type\n\n/**\n * Asserts a {@link ox#TransactionEnvelopeEip2930.TransactionEnvelopeEip2930} is valid.\n *\n * @example\n * ```ts twoslash\n * import { TransactionEnvelopeEip2930, Value } from 'ox'\n *\n * TransactionEnvelopeEip2930.assert({\n *   gasPrice: 2n ** 256n - 1n + 1n,\n *   chainId: 1,\n *   to: '******************************************',\n *   value: Value.fromEther('1'),\n * })\n * // @error: GasPriceTooHighError:\n * // @error: The gas price (`gasPrice` = 115792089237316195423570985008687907853269984665640564039457584007913 gwei) cannot be\n * // @error: higher than the maximum allowed value (2^256-1).\n * ```\n *\n * @param envelope - The transaction envelope to assert.\n */\nexport function assert(\n  envelope: PartialBy<TransactionEnvelopeEip2930, 'type'>,\n) {\n  const { chainId, gasPrice, to } = envelope\n  if (chainId <= 0)\n    throw new TransactionEnvelope.InvalidChainIdError({ chainId })\n  if (to) Address.assert(to, { strict: false })\n  if (gasPrice && BigInt(gasPrice) > 2n ** 256n - 1n)\n    throw new TransactionEnvelope.GasPriceTooHighError({ gasPrice })\n}\n\nexport declare namespace assert {\n  type ErrorType =\n    | Address.assert.ErrorType\n    | TransactionEnvelope.InvalidChainIdError\n    | TransactionEnvelope.GasPriceTooHighError\n    | Errors.GlobalErrorType\n}\n\n/**\n * Deserializes a {@link ox#TransactionEnvelopeEip2930.TransactionEnvelopeEip2930} from its serialized form.\n *\n * @example\n * ```ts twoslash\n * import { TransactionEnvelopeEip2930 } from 'ox'\n *\n * const envelope = TransactionEnvelopeEip2930.deserialize('0x01ef0182031184773594008477359400809470997970c51812dc3a010c7d01b50e0d17dc79c8880de0b6b3a764000080c0')\n * // @log: {\n * // @log:   type: 'eip2930',\n * // @log:   nonce: 785n,\n * // @log:   gasPrice: 2000000000n,\n * // @log:   gas: 1000000n,\n * // @log:   to: '******************************************',\n * // @log:   value: 1000000000000000000n,\n * // @log: }\n * ```\n *\n * @param serialized - The serialized transaction.\n * @returns Deserialized Transaction Envelope.\n */\nexport function deserialize(\n  serialized: Serialized,\n): TransactionEnvelopeEip2930 {\n  const transactionArray = Rlp.toHex(Hex.slice(serialized, 1))\n\n  const [\n    chainId,\n    nonce,\n    gasPrice,\n    gas,\n    to,\n    value,\n    data,\n    accessList,\n    yParity,\n    r,\n    s,\n  ] = transactionArray as readonly Hex.Hex[]\n\n  if (!(transactionArray.length === 8 || transactionArray.length === 11))\n    throw new TransactionEnvelope.InvalidSerializedError({\n      attributes: {\n        chainId,\n        nonce,\n        gasPrice,\n        gas,\n        to,\n        value,\n        data,\n        accessList,\n        ...(transactionArray.length > 8\n          ? {\n              yParity,\n              r,\n              s,\n            }\n          : {}),\n      },\n      serialized,\n      type,\n    })\n\n  let transaction = {\n    chainId: Number(chainId as Hex.Hex),\n    type,\n  } as TransactionEnvelopeEip2930\n  if (Hex.validate(to) && to !== '0x') transaction.to = to\n  if (Hex.validate(gas) && gas !== '0x') transaction.gas = BigInt(gas)\n  if (Hex.validate(data) && data !== '0x') transaction.data = data\n  if (Hex.validate(nonce) && nonce !== '0x') transaction.nonce = BigInt(nonce)\n  if (Hex.validate(value) && value !== '0x') transaction.value = BigInt(value)\n  if (Hex.validate(gasPrice) && gasPrice !== '0x')\n    transaction.gasPrice = BigInt(gasPrice)\n  if (accessList!.length !== 0 && accessList !== '0x')\n    transaction.accessList = AccessList.fromTupleList(accessList as any)\n\n  const signature =\n    r && s && yParity ? Signature.fromTuple([yParity, r, s]) : undefined\n  if (signature)\n    transaction = {\n      ...transaction,\n      ...signature,\n    } as TransactionEnvelopeEip2930\n\n  assert(transaction)\n\n  return transaction\n}\n\nexport declare namespace deserialize {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Converts an arbitrary transaction object into an EIP-2930 Transaction Envelope.\n *\n * @example\n * ```ts twoslash\n * // @noErrors\n * import { TransactionEnvelopeEip2930, Value } from 'ox'\n *\n * const envelope = TransactionEnvelopeEip2930.from({\n *   chainId: 1,\n *   accessList: [...],\n *   gasPrice: Value.fromGwei('10'),\n *   to: '******************************************',\n *   value: Value.fromEther('1'),\n * })\n * ```\n *\n * @example\n * ### Attaching Signatures\n *\n * It is possible to attach a `signature` to the transaction envelope.\n *\n * ```ts twoslash\n * import { Secp256k1, TransactionEnvelopeEip2930, Value } from 'ox'\n *\n * const envelope = TransactionEnvelopeEip2930.from({\n *   chainId: 1,\n *   gasPrice: Value.fromGwei('10'),\n *   to: '******************************************',\n *   value: Value.fromEther('1'),\n * })\n *\n * const signature = Secp256k1.sign({\n *   payload: TransactionEnvelopeEip2930.getSignPayload(envelope),\n *   privateKey: '0x...',\n * })\n *\n * const envelope_signed = TransactionEnvelopeEip2930.from(envelope, { // [!code focus]\n *   signature, // [!code focus]\n * }) // [!code focus]\n * // @log: {\n * // @log:   chainId: 1,\n * // @log:   gasPrice: 10000000000n,\n * // @log:   to: '******************************************',\n * // @log:   type: 'eip2930',\n * // @log:   value: 1000000000000000000n,\n * // @log:   r: 125...n,\n * // @log:   s: 642...n,\n * // @log:   yParity: 0,\n * // @log: }\n * ```\n *\n * @example\n * ### From Serialized\n *\n * It is possible to instantiate an EIP-2930 Transaction Envelope from a {@link ox#TransactionEnvelopeEip2930.Serialized} value.\n *\n * ```ts twoslash\n * import { TransactionEnvelopeEip2930 } from 'ox'\n *\n * const envelope = TransactionEnvelopeEip2930.from('0x01f858018203118502540be4008504a817c800809470997970c51812dc3a010c7d01b50e0d17dc79c8880de0b6b3a764000080c08477359400e1a001627c687261b0e7f8638af1112efa8a77e23656f6e7945275b19e9deed80261')\n * // @log: {\n * // @log:   chainId: 1,\n * // @log:   gasPrice: 10000000000n,\n * // @log:   to: '******************************************',\n * // @log:   type: 'eip2930',\n * // @log:   value: 1000000000000000000n,\n * // @log: }\n * ```\n *\n * @param envelope - The transaction object to convert.\n * @param options - Options.\n * @returns A {@link ox#TransactionEnvelopeEip2930.TransactionEnvelopeEip2930}\n */\nexport function from<\n  const envelope extends\n    | UnionPartialBy<TransactionEnvelopeEip2930, 'type'>\n    | Serialized,\n  const signature extends Signature.Signature | undefined = undefined,\n>(\n  envelope:\n    | envelope\n    | UnionPartialBy<TransactionEnvelopeEip2930, 'type'>\n    | Serialized,\n  options: from.Options<signature> = {},\n): from.ReturnType<envelope, signature> {\n  const { signature } = options\n\n  const envelope_ = (\n    typeof envelope === 'string' ? deserialize(envelope) : envelope\n  ) as TransactionEnvelopeEip2930\n\n  assert(envelope_)\n\n  return {\n    ...envelope_,\n    ...(signature ? Signature.from(signature) : {}),\n    type: 'eip2930',\n  } as never\n}\n\nexport declare namespace from {\n  type Options<signature extends Signature.Signature | undefined = undefined> =\n    {\n      signature?: signature | Signature.Signature | undefined\n    }\n\n  type ReturnType<\n    envelope extends\n      | UnionPartialBy<TransactionEnvelopeEip2930, 'type'>\n      | Hex.Hex = TransactionEnvelopeEip2930 | Hex.Hex,\n    signature extends Signature.Signature | undefined = undefined,\n  > = Compute<\n    envelope extends Hex.Hex\n      ? TransactionEnvelopeEip2930\n      : Assign<\n          envelope,\n          (signature extends Signature.Signature ? Readonly<signature> : {}) & {\n            readonly type: 'eip2930'\n          }\n        >\n  >\n\n  type ErrorType =\n    | deserialize.ErrorType\n    | assert.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Returns the payload to sign for a {@link ox#TransactionEnvelopeEip2930.TransactionEnvelopeEip2930}.\n *\n * @example\n * The example below demonstrates how to compute the sign payload which can be used\n * with ECDSA signing utilities like {@link ox#Secp256k1.(sign:function)}.\n *\n * ```ts twoslash\n * import { Secp256k1, TransactionEnvelopeEip2930 } from 'ox'\n *\n * const envelope = TransactionEnvelopeEip2930.from({\n *   chainId: 1,\n *   nonce: 0n,\n *   gasPrice: 1000000000n,\n *   gas: 21000n,\n *   to: '******************************************',\n *   value: 1000000000000000000n,\n * })\n *\n * const payload = TransactionEnvelopeEip2930.getSignPayload(envelope) // [!code focus]\n * // @log: '0x...'\n *\n * const signature = Secp256k1.sign({ payload, privateKey: '0x...' })\n * ```\n *\n * @param envelope - The transaction envelope to get the sign payload for.\n * @returns The sign payload.\n */\nexport function getSignPayload(\n  envelope: TransactionEnvelopeEip2930,\n): getSignPayload.ReturnType {\n  return hash(envelope, { presign: true })\n}\n\nexport declare namespace getSignPayload {\n  type ReturnType = Hex.Hex\n\n  type ErrorType = hash.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Hashes a {@link ox#TransactionEnvelopeEip2930.TransactionEnvelopeEip2930}. This is the \"transaction hash\".\n *\n * @example\n * ```ts twoslash\n * import { Secp256k1, TransactionEnvelopeEip2930 } from 'ox'\n *\n * const envelope = TransactionEnvelopeEip2930.from({\n *   chainId: 1,\n *   nonce: 0n,\n *   gasPrice: 1000000000n,\n *   gas: 21000n,\n *   to: '******************************************',\n *   value: 1000000000000000000n,\n * })\n *\n * const signature = Secp256k1.sign({\n *   payload: TransactionEnvelopeEip2930.getSignPayload(envelope),\n *   privateKey: '0x...',\n * })\n *\n * const envelope_signed = TransactionEnvelopeEip2930.from(envelope, {\n *   signature,\n * })\n *\n * const hash = TransactionEnvelopeEip2930.hash(envelope_signed) // [!code focus]\n * ```\n *\n * @param envelope - The EIP-2930 Transaction Envelope to hash.\n * @param options - Options.\n * @returns The hash of the transaction envelope.\n */\nexport function hash<presign extends boolean = false>(\n  envelope: TransactionEnvelopeEip2930<presign extends true ? false : true>,\n  options: hash.Options<presign> = {},\n): hash.ReturnType {\n  const { presign } = options\n  return Hash.keccak256(\n    serialize({\n      ...envelope,\n      ...(presign\n        ? {\n            r: undefined,\n            s: undefined,\n            yParity: undefined,\n            v: undefined,\n          }\n        : {}),\n    }),\n  )\n}\n\nexport declare namespace hash {\n  type Options<presign extends boolean = false> = {\n    /** Whether to hash this transaction for signing. @default false */\n    presign?: presign | boolean | undefined\n  }\n\n  type ReturnType = Hex.Hex\n\n  type ErrorType =\n    | Hash.keccak256.ErrorType\n    | serialize.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Serializes a {@link ox#TransactionEnvelopeEip2930.TransactionEnvelopeEip2930}.\n *\n * @example\n * ```ts twoslash\n * import { TransactionEnvelopeEip2930, Value } from 'ox'\n *\n * const envelope = TransactionEnvelopeEip2930.from({\n *   chainId: 1,\n *   gasPrice: Value.fromGwei('10'),\n *   to: '******************************************',\n *   value: Value.fromEther('1'),\n * })\n *\n * const serialized = TransactionEnvelopeEip2930.serialize(envelope) // [!code focus]\n * ```\n *\n * @example\n * ### Attaching Signatures\n *\n * It is possible to attach a `signature` to the serialized Transaction Envelope.\n *\n * ```ts twoslash\n * import { Secp256k1, TransactionEnvelopeEip2930, Value } from 'ox'\n *\n * const envelope = TransactionEnvelopeEip2930.from({\n *   chainId: 1,\n *   gasPrice: Value.fromGwei('10'),\n *   to: '******************************************',\n *   value: Value.fromEther('1'),\n * })\n *\n * const signature = Secp256k1.sign({\n *   payload: TransactionEnvelopeEip2930.getSignPayload(envelope),\n *   privateKey: '0x...',\n * })\n *\n * const serialized = TransactionEnvelopeEip2930.serialize(envelope, { // [!code focus]\n *   signature, // [!code focus]\n * }) // [!code focus]\n *\n * // ... send `serialized` transaction to JSON-RPC `eth_sendRawTransaction`\n * ```\n *\n * @param envelope - The Transaction Envelope to serialize.\n * @param options - Options.\n * @returns The serialized Transaction Envelope.\n */\nexport function serialize(\n  envelope: PartialBy<TransactionEnvelopeEip2930, 'type'>,\n  options: serialize.Options = {},\n): Serialized {\n  const { chainId, gas, data, input, nonce, to, value, accessList, gasPrice } =\n    envelope\n\n  assert(envelope)\n\n  const accessTupleList = AccessList.toTupleList(accessList)\n\n  const signature = Signature.extract(options.signature || (envelope as any))\n\n  const serialized = [\n    Hex.fromNumber(chainId),\n    nonce ? Hex.fromNumber(nonce) : '0x',\n    gasPrice ? Hex.fromNumber(gasPrice) : '0x',\n    gas ? Hex.fromNumber(gas) : '0x',\n    to ?? '0x',\n    value ? Hex.fromNumber(value) : '0x',\n    data ?? input ?? '0x',\n    accessTupleList,\n    ...(signature ? Signature.toTuple(signature) : []),\n  ] as const\n\n  return Hex.concat('0x01', Rlp.fromHex(serialized)) as Serialized\n}\n\nexport declare namespace serialize {\n  type Options = {\n    /** Signature to append to the serialized Transaction Envelope. */\n    signature?: Signature.Signature | undefined\n  }\n\n  type ErrorType =\n    | assert.ErrorType\n    | Hex.fromNumber.ErrorType\n    | Signature.toTuple.ErrorType\n    | Hex.concat.ErrorType\n    | Rlp.fromHex.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Converts an {@link ox#TransactionEnvelopeEip2930.TransactionEnvelopeEip2930} to an {@link ox#TransactionEnvelopeEip2930.Rpc}.\n *\n * @example\n * ```ts twoslash\n * import { RpcRequest, TransactionEnvelopeEip2930, Value } from 'ox'\n *\n * const envelope = TransactionEnvelopeEip2930.from({\n *   chainId: 1,\n *   nonce: 0n,\n *   gas: 21000n,\n *   maxFeePerGas: Value.fromGwei('20'),\n *   to: '******************************************',\n *   value: Value.fromEther('1'),\n * })\n *\n * const envelope_rpc = TransactionEnvelopeEip2930.toRpc(envelope) // [!code focus]\n *\n * const request = RpcRequest.from({\n *   id: 0,\n *   method: 'eth_sendTransaction',\n *   params: [envelope_rpc],\n * })\n * ```\n *\n * @param envelope - The EIP-2930 transaction envelope to convert.\n * @returns An RPC-formatted EIP-2930 transaction envelope.\n */\nexport function toRpc(envelope: Omit<TransactionEnvelopeEip2930, 'type'>): Rpc {\n  const signature = Signature.extract(envelope)!\n\n  return {\n    ...envelope,\n    chainId: Hex.fromNumber(envelope.chainId),\n    data: envelope.data ?? envelope.input,\n    ...(typeof envelope.gas === 'bigint'\n      ? { gas: Hex.fromNumber(envelope.gas) }\n      : {}),\n    ...(typeof envelope.nonce === 'bigint'\n      ? { nonce: Hex.fromNumber(envelope.nonce) }\n      : {}),\n    ...(typeof envelope.value === 'bigint'\n      ? { value: Hex.fromNumber(envelope.value) }\n      : {}),\n    ...(typeof envelope.gasPrice === 'bigint'\n      ? { gasPrice: Hex.fromNumber(envelope.gasPrice) }\n      : {}),\n    type: '0x1',\n    ...(signature ? Signature.toRpc(signature) : {}),\n  } as never\n}\n\nexport declare namespace toRpc {\n  export type ErrorType = Signature.extract.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Validates a {@link ox#TransactionEnvelopeEip2930.TransactionEnvelopeEip2930}. Returns `true` if the envelope is valid, `false` otherwise.\n *\n * @example\n * ```ts twoslash\n * import { TransactionEnvelopeEip2930, Value } from 'ox'\n *\n * const valid = TransactionEnvelopeEip2930.assert({\n *   gasPrice: 2n ** 256n - 1n + 1n,\n *   chainId: 1,\n *   to: '******************************************',\n *   value: Value.fromEther('1'),\n * })\n * // @log: false\n * ```\n *\n * @param envelope - The transaction envelope to validate.\n */\nexport function validate(\n  envelope: PartialBy<TransactionEnvelopeEip2930, 'type'>,\n) {\n  try {\n    assert(envelope)\n    return true\n  } catch {\n    return false\n  }\n}\n\nexport declare namespace validate {\n  type ErrorType = Errors.GlobalErrorType\n}\n", "import type * as Address from './Address.js'\nimport type * as Errors from './Errors.js'\nimport * as Hash from './Hash.js'\nimport * as Hex from './Hex.js'\nimport * as Rlp from './Rlp.js'\nimport * as Signature from './Signature.js'\nimport type { Compute, Mutable, Undefined } from './internal/types.js'\n\n/** Root type for an EIP-7702 Authorization. */\nexport type Authorization<\n  signed extends boolean = boolean,\n  bigintType = bigint,\n  numberType = number,\n> = Compute<\n  {\n    /** Address of the contract to set as code for the Authority. */\n    address: Address.Address\n    /** Chain ID to authorize. */\n    chainId: numberType\n    /** Nonce of the Authority to authorize. */\n    nonce: bigintType\n  } & (signed extends true\n    ? Signature.Signature<true, bigintType, numberType>\n    : Undefined<Signature.Signature>)\n>\n\n/** RPC representation of an {@link ox#Authorization.Authorization}. */\nexport type Rpc = Authorization<true, Hex.Hex, Hex.Hex>\n\n/** List of {@link ox#Authorization.Authorization}. */\nexport type List<\n  signed extends boolean = boolean,\n  bigintType = bigint,\n  numberType = number,\n> = Compute<readonly Authorization<signed, bigintType, numberType>[]>\n\n/** RPC representation of an {@link ox#Authorization.List}. */\nexport type ListRpc = List<true, Hex.Hex, Hex.Hex>\n\n/** Signed representation of a list of {@link ox#Authorization.Authorization}. */\nexport type ListSigned<bigintType = bigint, numberType = number> = List<\n  true,\n  bigintType,\n  numberType\n>\n\n/** Signed representation of an {@link ox#Authorization.Authorization}. */\nexport type Signed<bigintType = bigint, numberType = number> = Authorization<\n  true,\n  bigintType,\n  numberType\n>\n\n/** Tuple representation of an {@link ox#Authorization.Authorization}. */\nexport type Tuple<signed extends boolean = boolean> = signed extends true\n  ? readonly [\n      chainId: Hex.Hex,\n      address: Hex.Hex,\n      nonce: Hex.Hex,\n      yParity: Hex.Hex,\n      r: Hex.Hex,\n      s: Hex.Hex,\n    ]\n  : readonly [chainId: Hex.Hex, address: Hex.Hex, nonce: Hex.Hex]\n\n/** Tuple representation of a signed {@link ox#Authorization.Authorization}. */\nexport type TupleSigned = Tuple<true>\n\n/** Tuple representation of a list of {@link ox#Authorization.Authorization}. */\nexport type TupleList<signed extends boolean = boolean> =\n  readonly Tuple<signed>[]\n\n/** Tuple representation of a list of signed {@link ox#Authorization.Authorization}. */\nexport type TupleListSigned = TupleList<true>\n\n/**\n * Converts an [EIP-7702](https://eips.ethereum.org/EIPS/eip-7702) Authorization object into a typed {@link ox#Authorization.Authorization}.\n *\n * @example\n * An Authorization can be instantiated from an [EIP-7702](https://eips.ethereum.org/EIPS/eip-7702) Authorization tuple in object format.\n *\n * ```ts twoslash\n * import { Authorization } from 'ox'\n *\n * const authorization = Authorization.from({\n *   address: '******************************************',\n *   chainId: 1,\n *   nonce: 69n,\n * })\n * ```\n *\n * @example\n * ### Attaching Signatures\n *\n * A {@link ox#Signature.Signature} can be attached with the `signature` option. The example below demonstrates signing\n * an Authorization with {@link ox#Secp256k1.(sign:function)}.\n *\n * ```ts twoslash\n * import { Authorization, Secp256k1 } from 'ox'\n *\n * const authorization = Authorization.from({\n *   address: '******************************************',\n *   chainId: 1,\n *   nonce: 40n,\n * })\n *\n * const signature = Secp256k1.sign({\n *   payload: Authorization.getSignPayload(authorization),\n *   privateKey: '0x...',\n * })\n *\n * const authorization_signed = Authorization.from(authorization, { signature }) // [!code focus]\n * ```\n *\n * @param authorization - An [EIP-7702](https://eips.ethereum.org/EIPS/eip-7702) Authorization tuple in object format.\n * @param options - Authorization options.\n * @returns The {@link ox#Authorization.Authorization}.\n */\nexport function from<\n  const authorization extends Authorization | Rpc,\n  const signature extends Signature.Signature | undefined = undefined,\n>(\n  authorization: authorization | Authorization,\n  options: from.Options<signature> = {},\n): from.ReturnType<authorization, signature> {\n  if (typeof authorization.chainId === 'string')\n    return fromRpc(authorization) as never\n  return { ...authorization, ...options.signature } as never\n}\n\nexport declare namespace from {\n  type Options<\n    signature extends Signature.Signature | undefined =\n      | Signature.Signature\n      | undefined,\n  > = {\n    /** The {@link ox#Signature.Signature} to attach to the Authorization. */\n    signature?: signature | Signature.Signature | undefined\n  }\n\n  type ReturnType<\n    authorization extends Authorization | Rpc = Authorization,\n    signature extends Signature.Signature | undefined =\n      | Signature.Signature\n      | undefined,\n  > = Compute<\n    authorization extends Rpc\n      ? Signed\n      : authorization &\n          (signature extends Signature.Signature ? Readonly<signature> : {})\n  >\n\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Converts an {@link ox#Authorization.Rpc} to an {@link ox#Authorization.Authorization}.\n *\n * @example\n * ```ts twoslash\n * import { Authorization } from 'ox'\n *\n * const authorization = Authorization.fromRpc({\n *   address: '******************************************',\n *   chainId: '0x1',\n *   nonce: '0x1',\n *   r: '0x635dc2033e60185bb36709c29c75d64ea51dfbd91c32ef4be198e4ceb169fb4d',\n *   s: '0x50c2667ac4c771072746acfdcf1f1483336dcca8bd2df47cd83175dbe60f0540',\n *   yParity: '0x0',\n * })\n * ```\n *\n * @param authorization - The RPC-formatted Authorization.\n * @returns A signed {@link ox#Authorization.Authorization}.\n */\nexport function fromRpc(authorization: Rpc): Signed {\n  const { address, chainId, nonce } = authorization\n  const signature = Signature.extract(authorization)!\n\n  return {\n    address,\n    chainId: Number(chainId),\n    nonce: BigInt(nonce),\n    ...signature,\n  }\n}\n\nexport declare namespace fromRpc {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Converts an {@link ox#Authorization.ListRpc} to an {@link ox#Authorization.List}.\n *\n * @example\n * ```ts twoslash\n * import { Authorization } from 'ox'\n *\n * const authorizationList = Authorization.fromRpcList([{\n *   address: '******************************************',\n *   chainId: '0x1',\n *   nonce: '0x1',\n *   r: '0x635dc2033e60185bb36709c29c75d64ea51dfbd91c32ef4be198e4ceb169fb4d',\n *   s: '0x50c2667ac4c771072746acfdcf1f1483336dcca8bd2df47cd83175dbe60f0540',\n *   yParity: '0x0',\n * }])\n * ```\n *\n * @param authorizationList - The RPC-formatted Authorization list.\n * @returns A signed {@link ox#Authorization.List}.\n */\nexport function fromRpcList(authorizationList: ListRpc): ListSigned {\n  return authorizationList.map(fromRpc)\n}\n\nexport declare namespace fromRpcList {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Converts an {@link ox#Authorization.Tuple} to an {@link ox#Authorization.Authorization}.\n *\n * @example\n * ```ts twoslash\n * import { Authorization } from 'ox'\n *\n * const authorization = Authorization.fromTuple([\n *   '0x1',\n *   '******************************************',\n *   '0x3'\n * ])\n * // @log: {\n * // @log:   address: '******************************************',\n * // @log:   chainId: 1,\n * // @log:   nonce: 3n\n * // @log: }\n * ```\n *\n * @example\n * It is also possible to append a Signature tuple to the end of an Authorization tuple.\n *\n * ```ts twoslash\n * import { Authorization } from 'ox'\n *\n * const authorization = Authorization.fromTuple([\n *   '0x1',\n *   '******************************************',\n *   '0x3',\n *   '0x1',\n *   '0x68a020a209d3d56c46f38cc50a33f704f4a9a10a59377f8dd762ac66910e9b90',\n *   '0x7e865ad05c4035ab5792787d4a0297a43617ae897930a6fe4d822b8faea52064',\n * ])\n * // @log: {\n * // @log:   address: '******************************************',\n * // @log:   chainId: 1,\n * // @log:   nonce: 3n\n * // @log:   r: BigInt('0x68a020a209d3d56c46f38cc50a33f704f4a9a10a59377f8dd762ac66910e9b90'),\n * // @log:   s: BigInt('0x7e865ad05c4035ab5792787d4a0297a43617ae897930a6fe4d822b8faea52064'),\n * // @log:   yParity: 0,\n * // @log: }\n * ```\n *\n * @param tuple - The [EIP-7702](https://eips.ethereum.org/EIPS/eip-7702) Authorization tuple.\n * @returns The {@link ox#Authorization.Authorization}.\n */\nexport function fromTuple<const tuple extends Tuple>(\n  tuple: tuple,\n): fromTuple.ReturnType<tuple> {\n  const [chainId, address, nonce, yParity, r, s] = tuple\n  const signature =\n    yParity && r && s ? Signature.fromTuple([yParity, r, s]) : undefined\n  return from({\n    address,\n    chainId: Number(chainId),\n    nonce: BigInt(nonce),\n    ...signature,\n  }) as never\n}\n\nexport declare namespace fromTuple {\n  type ReturnType<authorization extends Tuple = Tuple> = Compute<\n    Authorization<authorization extends Tuple<true> ? true : false>\n  >\n\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Converts an {@link ox#Authorization.TupleList} to an {@link ox#Authorization.List}.\n *\n * @example\n * ```ts twoslash\n * import { Authorization } from 'ox'\n *\n * const authorizationList = Authorization.fromTupleList([\n *   ['0x1', '******************************************', '0x3'],\n *   ['0x3', '******************************************', '0x14'],\n * ])\n * // @log: [\n * // @log:   {\n * // @log:     address: '******************************************',\n * // @log:     chainId: 1,\n * // @log:     nonce: 3n,\n * // @log:   },\n * // @log:   {\n * // @log:     address: '******************************************',\n * // @log:     chainId: 3,\n * // @log:     nonce: 20n,\n * // @log:   },\n * // @log: ]\n * ```\n *\n * @example\n * It is also possible to append a Signature tuple to the end of an Authorization tuple.\n *\n * ```ts twoslash\n * import { Authorization } from 'ox'\n *\n * const authorizationList = Authorization.fromTupleList([\n *   ['0x1', '******************************************', '0x3', '0x1', '0x68a020a209d3d56c46f38cc50a33f704f4a9a10a59377f8dd762ac66910e9b90', '0x7e865ad05c4035ab5792787d4a0297a43617ae897930a6fe4d822b8faea52064'],\n *   ['0x3', '******************************************', '0x14', '0x1', '0x68a020a209d3d56c46f38cc50a33f704f4a9a10a59377f8dd762ac66910e9b90', '0x7e865ad05c4035ab5792787d4a0297a43617ae897930a6fe4d822b8faea52064'],\n * ])\n * // @log: [\n * // @log:   {\n * // @log:     address: '******************************************',\n * // @log:     chainId: 1,\n * // @log:     nonce: 3n,\n * // @log:     r: BigInt('0x68a020a209d3d56c46f38cc50a33f704f4a9a10a59377f8dd762ac66910e9b90'),\n * // @log:     s: BigInt('0x7e865ad05c4035ab5792787d4a0297a43617ae897930a6fe4d822b8faea52064'),\n * // @log:     yParity: 0,\n * // @log:   },\n * // @log:   {\n * // @log:     address: '******************************************',\n * // @log:     chainId: 3,\n * // @log:     nonce: 20n,\n * // @log:     r: BigInt('0x68a020a209d3d56c46f38cc50a33f704f4a9a10a59377f8dd762ac66910e9b90'),\n * // @log:     s: BigInt('0x7e865ad05c4035ab5792787d4a0297a43617ae897930a6fe4d822b8faea52064'),\n * // @log:     yParity: 0,\n * // @log:   },\n * // @log: ]\n * ```\n *\n * @param tupleList - The [EIP-7702](https://eips.ethereum.org/EIPS/eip-7702) Authorization tuple list.\n * @returns An {@link ox#Authorization.List}.\n */\nexport function fromTupleList<const tupleList extends TupleList>(\n  tupleList: tupleList,\n): fromTupleList.ReturnType<tupleList> {\n  const list: Mutable<List> = []\n  for (const tuple of tupleList) list.push(fromTuple(tuple))\n  return list as never\n}\n\nexport declare namespace fromTupleList {\n  type ReturnType<tupleList extends TupleList> = Compute<\n    TupleList<tupleList extends TupleList<true> ? true : false>\n  >\n\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Computes the sign payload for an {@link ox#Authorization.Authorization} in [EIP-7702 format](https://eips.ethereum.org/EIPS/eip-7702): `keccak256('0x05' || rlp([chain_id, address, nonce]))`.\n *\n * @example\n * The example below demonstrates computing the sign payload for an {@link ox#Authorization.Authorization}. This payload\n * can then be passed to signing functions like {@link ox#Secp256k1.(sign:function)}.\n *\n * ```ts twoslash\n * import { Authorization, Secp256k1 } from 'ox'\n *\n * const authorization = Authorization.from({\n *   address: '******************************************',\n *   chainId: 1,\n *   nonce: 69n,\n * })\n *\n * const payload = Authorization.getSignPayload(authorization) // [!code focus]\n *\n * const signature = Secp256k1.sign({\n *   payload,\n *   privateKey: '0x...',\n * })\n * ```\n *\n * @param authorization - The {@link ox#Authorization.Authorization}.\n * @returns The sign payload.\n */\nexport function getSignPayload(authorization: Authorization): Hex.Hex {\n  return hash(authorization)\n}\n\nexport declare namespace getSignPayload {\n  type ErrorType = hash.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Computes the hash for an {@link ox#Authorization.Authorization} in [EIP-7702 format](https://eips.ethereum.org/EIPS/eip-7702): `keccak256('0x05' || rlp([chain_id, address, nonce]))`.\n *\n * @example\n * ```ts twoslash\n * import { Authorization } from 'ox'\n *\n * const authorization = Authorization.from({\n *   address: '******************************************',\n *   chainId: 1,\n *   nonce: 69n,\n * })\n *\n * const hash = Authorization.hash(authorization) // [!code focus]\n * ```\n *\n * @param authorization - The {@link ox#Authorization.Authorization}.\n * @returns The hash.\n */\nexport function hash(authorization: Authorization): Hex.Hex {\n  return Hash.keccak256(Hex.concat('0x05', Rlp.fromHex(toTuple(authorization))))\n}\n\nexport declare namespace hash {\n  type ErrorType =\n    | toTuple.ErrorType\n    | Hash.keccak256.ErrorType\n    | Hex.concat.ErrorType\n    | Rlp.fromHex.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Converts an {@link ox#Authorization.Authorization} to an {@link ox#Authorization.Rpc}.\n *\n * @example\n * ```ts twoslash\n * import { Authorization } from 'ox'\n *\n * const authorization = Authorization.toRpc({\n *   address: '******************************************',\n *   chainId: 1,\n *   nonce: 1n,\n *   r: 44944627813007772897391531230081695102703289123332187696115181104739239197517n,\n *   s: 36528503505192438307355164441104001310566505351980369085208178712678799181120n,\n *   yParity: 0,\n * })\n * ```\n *\n * @param authorization - An Authorization.\n * @returns An RPC-formatted Authorization.\n */\nexport function toRpc(authorization: Signed): Rpc {\n  const { address, chainId, nonce, ...signature } = authorization\n\n  return {\n    address,\n    chainId: Hex.fromNumber(chainId),\n    nonce: Hex.fromNumber(nonce),\n    ...Signature.toRpc(signature),\n  }\n}\n\nexport declare namespace toRpc {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Converts an {@link ox#Authorization.List} to an {@link ox#Authorization.ListRpc}.\n *\n * @example\n * ```ts twoslash\n * import { Authorization } from 'ox'\n *\n * const authorization = Authorization.toRpcList([{\n *   address: '******************************************',\n *   chainId: 1,\n *   nonce: 1n,\n *   r: 44944627813007772897391531230081695102703289123332187696115181104739239197517n,\n *   s: 36528503505192438307355164441104001310566505351980369085208178712678799181120n,\n *   yParity: 0,\n * }])\n * ```\n *\n * @param authorizationList - An Authorization List.\n * @returns An RPC-formatted Authorization List.\n */\nexport function toRpcList(authorizationList: ListSigned): ListRpc {\n  return authorizationList.map(toRpc)\n}\n\nexport declare namespace toRpcList {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Converts an {@link ox#Authorization.Authorization} to an {@link ox#Authorization.Tuple}.\n *\n * @example\n * ```ts twoslash\n * import { Authorization } from 'ox'\n *\n * const authorization = Authorization.from({\n *   address: '******************************************',\n *   chainId: 1,\n *   nonce: 69n,\n * })\n *\n * const tuple = Authorization.toTuple(authorization) // [!code focus]\n * // @log: [\n * // @log:   address: '******************************************',\n * // @log:   chainId: 1,\n * // @log:   nonce: 69n,\n * // @log: ]\n * ```\n *\n * @param authorization - The {@link ox#Authorization.Authorization}.\n * @returns An [EIP-7702](https://eips.ethereum.org/EIPS/eip-7702) Authorization tuple.\n */\nexport function toTuple<const authorization extends Authorization>(\n  authorization: authorization,\n): toTuple.ReturnType<authorization> {\n  const { address, chainId, nonce } = authorization\n  const signature = Signature.extract(authorization)\n  return [\n    chainId ? Hex.fromNumber(chainId) : '0x',\n    address,\n    nonce ? Hex.fromNumber(nonce) : '0x',\n    ...(signature ? Signature.toTuple(signature) : []),\n  ] as never\n}\n\nexport declare namespace toTuple {\n  type ReturnType<authorization extends Authorization = Authorization> =\n    Compute<Tuple<authorization extends Signature.Signature ? true : false>>\n\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Converts an {@link ox#Authorization.List} to an {@link ox#Authorization.TupleList}.\n *\n * @example\n * ```ts twoslash\n * import { Authorization } from 'ox'\n *\n * const authorization_1 = Authorization.from({\n *   address: '******************************************',\n *   chainId: 1,\n *   nonce: 69n,\n * })\n * const authorization_2 = Authorization.from({\n *   address: '******************************************',\n *   chainId: 3,\n *   nonce: 20n,\n * })\n *\n * const tuple = Authorization.toTupleList([authorization_1, authorization_2]) // [!code focus]\n * // @log: [\n * // @log:   [\n * // @log:     address: '******************************************',\n * // @log:     chainId: 1,\n * // @log:     nonce: 69n,\n * // @log:   ],\n * // @log:   [\n * // @log:     address: '******************************************',\n * // @log:     chainId: 3,\n * // @log:     nonce: 20n,\n * // @log:   ],\n * // @log: ]\n * ```\n *\n * @param list - An {@link ox#Authorization.List}.\n * @returns An [EIP-7702](https://eips.ethereum.org/EIPS/eip-7702) Authorization tuple list.\n */\nexport function toTupleList<\n  const list extends\n    | readonly Authorization<true>[]\n    | readonly Authorization<false>[],\n>(list?: list | undefined): toTupleList.ReturnType<list> {\n  if (!list || list.length === 0) return []\n\n  const tupleList: Mutable<TupleList> = []\n  for (const authorization of list) tupleList.push(toTuple(authorization))\n\n  return tupleList as never\n}\n\nexport declare namespace toTupleList {\n  type ReturnType<\n    list extends\n      | readonly Authorization<true>[]\n      | readonly Authorization<false>[],\n  > = Compute<\n    TupleList<list extends readonly Authorization<true>[] ? true : false>\n  >\n\n  type ErrorType = Errors.GlobalErrorType\n}\n", "import type { Assign } from './internal/types.js'\n\nimport type { PartialBy, UnionPartialBy } from './internal/types.js'\n\nimport * as AccessList from './AccessList.js'\nimport * as Address from './Address.js'\nimport * as Authorization from './Authorization.js'\nimport type * as Errors from './Errors.js'\nimport * as Hash from './Hash.js'\nimport * as Hex from './Hex.js'\nimport * as Rlp from './Rlp.js'\nimport * as Signature from './Signature.js'\nimport * as TransactionEnvelope from './TransactionEnvelope.js'\nimport * as TransactionEnvelopeEip1559 from './TransactionEnvelopeEip1559.js'\nimport type { Compute } from './internal/types.js'\n\nexport type TransactionEnvelopeEip7702<\n  signed extends boolean = boolean,\n  bigintType = bigint,\n  numberType = number,\n  type extends string = Type,\n> = Compute<\n  TransactionEnvelope.Base<type, signed, bigintType, numberType> & {\n    /** EIP-2930 Access List. */\n    accessList?: AccessList.AccessList | undefined\n    /** EIP-7702 Authorization List. */\n    authorizationList: Authorization.ListSigned<bigintType, numberType>\n    /** Total fee per gas in wei (gasPrice/baseFeePerGas + maxPriorityFeePerGas). */\n    maxFeePerGas?: bigintType | undefined\n    /** Max priority fee per gas (in wei). */\n    maxPriorityFeePerGas?: bigintType | undefined\n  }\n>\n\nexport type Rpc<signed extends boolean = boolean> = TransactionEnvelopeEip7702<\n  signed,\n  Hex.Hex,\n  Hex.Hex,\n  '0x4'\n>\n\nexport type Serialized = `${SerializedType}${string}`\n\nexport type Signed = TransactionEnvelopeEip7702<true>\n\nexport const serializedType = '0x04' as const\nexport type SerializedType = typeof serializedType\n\nexport const type = 'eip7702' as const\nexport type Type = typeof type\n\n/**\n * Asserts a {@link ox#TransactionEnvelopeEip7702.TransactionEnvelopeEip7702} is valid.\n *\n * @example\n * ```ts twoslash\n * import { TransactionEnvelopeEip7702, Value } from 'ox'\n *\n * TransactionEnvelopeEip7702.assert({\n *   authorizationList: [],\n *   maxFeePerGas: 2n ** 256n - 1n + 1n,\n *   chainId: 1,\n *   to: '******************************************',\n *   value: Value.fromEther('1'),\n * })\n * // @error: FeeCapTooHighError:\n * // @error: The fee cap (`masFeePerGas` = 115792089237316195423570985008687907853269984665640564039457584007913 gwei) cannot be\n * // @error: higher than the maximum allowed value (2^256-1).\n * ```\n *\n * @param envelope - The transaction envelope to assert.\n */\nexport function assert(\n  envelope: PartialBy<TransactionEnvelopeEip7702, 'type'>,\n) {\n  const { authorizationList } = envelope\n  if (authorizationList) {\n    for (const authorization of authorizationList) {\n      const { address, chainId } = authorization\n      if (address) Address.assert(address, { strict: false })\n      if (Number(chainId) < 0)\n        throw new TransactionEnvelope.InvalidChainIdError({ chainId })\n    }\n  }\n  TransactionEnvelopeEip1559.assert(\n    envelope as {} as TransactionEnvelopeEip1559.TransactionEnvelopeEip1559,\n  )\n}\n\nexport declare namespace assert {\n  type ErrorType =\n    | Address.assert.ErrorType\n    | TransactionEnvelope.InvalidChainIdError\n    | Errors.GlobalErrorType\n}\n\n/**\n * Deserializes a {@link ox#TransactionEnvelopeEip7702.TransactionEnvelopeEip7702} from its serialized form.\n *\n * @example\n * ```ts twoslash\n * import { TransactionEnvelopeEip7702 } from 'ox'\n *\n * const envelope = TransactionEnvelopeEip7702.deserialize('0x04ef0182031184773594008477359400809470997970c51812dc3a010c7d01b50e0d17dc79c8880de0b6b3a764000080c0')\n * // @log: {\n * // @log:   authorizationList: [...],\n * // @log:   type: 'eip7702',\n * // @log:   nonce: 785n,\n * // @log:   maxFeePerGas: 2000000000n,\n * // @log:   gas: 1000000n,\n * // @log:   to: '******************************************',\n * // @log:   value: 1000000000000000000n,\n * // @log: }\n * ```\n *\n * @param serialized - The serialized transaction.\n * @returns Deserialized Transaction Envelope.\n */\nexport function deserialize(\n  serialized: Serialized,\n): Compute<TransactionEnvelopeEip7702> {\n  const transactionArray = Rlp.toHex(Hex.slice(serialized, 1))\n\n  const [\n    chainId,\n    nonce,\n    maxPriorityFeePerGas,\n    maxFeePerGas,\n    gas,\n    to,\n    value,\n    data,\n    accessList,\n    authorizationList,\n    yParity,\n    r,\n    s,\n  ] = transactionArray as readonly Hex.Hex[]\n\n  if (!(transactionArray.length === 10 || transactionArray.length === 13))\n    throw new TransactionEnvelope.InvalidSerializedError({\n      attributes: {\n        chainId,\n        nonce,\n        maxPriorityFeePerGas,\n        maxFeePerGas,\n        gas,\n        to,\n        value,\n        data,\n        accessList,\n        authorizationList,\n        ...(transactionArray.length > 9\n          ? {\n              yParity,\n              r,\n              s,\n            }\n          : {}),\n      },\n      serialized,\n      type,\n    })\n\n  let transaction = {\n    chainId: Number(chainId),\n    type,\n  } as TransactionEnvelopeEip7702\n  if (Hex.validate(to) && to !== '0x') transaction.to = to\n  if (Hex.validate(gas) && gas !== '0x') transaction.gas = BigInt(gas)\n  if (Hex.validate(data) && data !== '0x') transaction.data = data\n  if (Hex.validate(nonce) && nonce !== '0x') transaction.nonce = BigInt(nonce)\n  if (Hex.validate(value) && value !== '0x') transaction.value = BigInt(value)\n  if (Hex.validate(maxFeePerGas) && maxFeePerGas !== '0x')\n    transaction.maxFeePerGas = BigInt(maxFeePerGas)\n  if (Hex.validate(maxPriorityFeePerGas) && maxPriorityFeePerGas !== '0x')\n    transaction.maxPriorityFeePerGas = BigInt(maxPriorityFeePerGas)\n  if (accessList!.length !== 0 && accessList !== '0x')\n    transaction.accessList = AccessList.fromTupleList(accessList as never)\n  if (authorizationList !== '0x')\n    transaction.authorizationList = Authorization.fromTupleList(\n      authorizationList as never,\n    )\n\n  const signature =\n    r && s && yParity ? Signature.fromTuple([yParity, r, s]) : undefined\n  if (signature)\n    transaction = {\n      ...transaction,\n      ...signature,\n    } as TransactionEnvelopeEip7702\n\n  assert(transaction)\n\n  return transaction\n}\n\nexport declare namespace deserialize {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Converts an arbitrary transaction object into an EIP-7702 Transaction Envelope.\n *\n * @example\n * ```ts twoslash\n * import { Authorization, Secp256k1, TransactionEnvelopeEip7702, Value } from 'ox'\n *\n * const authorization = Authorization.from({\n *   address: '******************************************',\n *   chainId: 1,\n *   nonce: 0n,\n * })\n *\n * const signature = Secp256k1.sign({\n *   payload: Authorization.getSignPayload(authorization),\n *   privateKey: '0x...',\n * })\n *\n * const authorizationList = [Authorization.from(authorization, { signature })]\n *\n * const envelope = TransactionEnvelopeEip7702.from({ // [!code focus]\n *   authorizationList, // [!code focus]\n *   chainId: 1, // [!code focus]\n *   maxFeePerGas: Value.fromGwei('10'), // [!code focus]\n *   maxPriorityFeePerGas: Value.fromGwei('1'), // [!code focus]\n *   to: '******************************************', // [!code focus]\n *   value: Value.fromEther('1'), // [!code focus]\n * }) // [!code focus]\n * ```\n *\n * @example\n * ### Attaching Signatures\n *\n * It is possible to attach a `signature` to the transaction envelope.\n *\n * ```ts twoslash\n * // @noErrors\n * import { Secp256k1, TransactionEnvelopeEip7702, Value } from 'ox'\n *\n * const envelope = TransactionEnvelopeEip7702.from({\n *   authorizationList: [...],\n *   chainId: 1,\n *   maxFeePerGas: Value.fromGwei('10'),\n *   maxPriorityFeePerGas: Value.fromGwei('1'),\n *   to: '******************************************',\n *   value: Value.fromEther('1'),\n * })\n *\n * const signature = Secp256k1.sign({\n *   payload: TransactionEnvelopeEip7702.getSignPayload(envelope),\n *   privateKey: '0x...',\n * })\n *\n * const envelope_signed = TransactionEnvelopeEip7702.from(envelope, { // [!code focus]\n *   signature, // [!code focus]\n * }) // [!code focus]\n * // @log: {\n * // @log:   authorizationList: [...],\n * // @log:   chainId: 1,\n * // @log:   maxFeePerGas: 10000000000n,\n * // @log:   maxPriorityFeePerGas: 1000000000n,\n * // @log:   to: '******************************************',\n * // @log:   type: 'eip7702',\n * // @log:   value: 1000000000000000000n,\n * // @log:   r: 125...n,\n * // @log:   s: 642...n,\n * // @log:   yParity: 0,\n * // @log: }\n * ```\n *\n * @example\n * ### From Serialized\n *\n * It is possible to instantiate an EIP-7702 Transaction Envelope from a {@link ox#TransactionEnvelopeEip7702.Serialized} value.\n *\n * ```ts twoslash\n * import { TransactionEnvelopeEip7702 } from 'ox'\n *\n * const envelope = TransactionEnvelopeEip7702.from('0x04f858018203118502540be4008504a817c800809470997970c51812dc3a010c7d01b50e0d17dc79c8880de0b6b3a764000080c08477359400e1a001627c687261b0e7f8638af1112efa8a77e23656f6e7945275b19e9deed80261')\n * // @log: {\n * // @log:   authorizationList: [...],\n * // @log:   chainId: 1,\n * // @log:   maxFeePerGas: 10000000000n,\n * // @log:   to: '******************************************',\n * // @log:   type: 'eip7702',\n * // @log:   value: 1000000000000000000n,\n * // @log: }\n * ```\n *\n * @param envelope - The transaction object to convert.\n * @param options - Options.\n * @returns An EIP-7702 Transaction Envelope.\n */\nexport function from<\n  const envelope extends\n    | UnionPartialBy<TransactionEnvelopeEip7702, 'type'>\n    | Serialized,\n  const signature extends Signature.Signature | undefined = undefined,\n>(\n  envelope:\n    | envelope\n    | UnionPartialBy<TransactionEnvelopeEip7702, 'type'>\n    | Serialized,\n  options: from.Options<signature> = {},\n): from.ReturnType<envelope, signature> {\n  const { signature } = options\n\n  const envelope_ = (\n    typeof envelope === 'string' ? deserialize(envelope) : envelope\n  ) as TransactionEnvelopeEip7702\n\n  assert(envelope_)\n\n  return {\n    ...envelope_,\n    ...(signature ? Signature.from(signature) : {}),\n    type: 'eip7702',\n  } as never\n}\n\nexport declare namespace from {\n  type Options<signature extends Signature.Signature | undefined = undefined> =\n    {\n      signature?: signature | Signature.Signature | undefined\n    }\n\n  type ReturnType<\n    envelope extends\n      | UnionPartialBy<TransactionEnvelopeEip7702, 'type'>\n      | Hex.Hex = TransactionEnvelopeEip7702 | Hex.Hex,\n    signature extends Signature.Signature | undefined = undefined,\n  > = Compute<\n    envelope extends Hex.Hex\n      ? TransactionEnvelopeEip7702\n      : Assign<\n          envelope,\n          (signature extends Signature.Signature ? Readonly<signature> : {}) & {\n            readonly type: 'eip7702'\n          }\n        >\n  >\n\n  type ErrorType =\n    | deserialize.ErrorType\n    | assert.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Returns the payload to sign for a {@link ox#TransactionEnvelopeEip7702.TransactionEnvelopeEip7702}.\n *\n * @example\n * The example below demonstrates how to compute the sign payload which can be used\n * with ECDSA signing utilities like {@link ox#Secp256k1.(sign:function)}.\n *\n * ```ts twoslash\n * // @noErrors\n * import { Secp256k1, TransactionEnvelopeEip7702 } from 'ox'\n *\n * const envelope = TransactionEnvelopeEip7702.from({\n *   authorizationList: [...],\n *   chainId: 1,\n *   nonce: 0n,\n *   maxFeePerGas: 1000000000n,\n *   gas: 21000n,\n *   to: '******************************************',\n *   value: 1000000000000000000n,\n * })\n *\n * const payload = TransactionEnvelopeEip7702.getSignPayload(envelope) // [!code focus]\n * // @log: '0x...'\n *\n * const signature = Secp256k1.sign({ payload, privateKey: '0x...' })\n * ```\n *\n * @param envelope - The transaction envelope to get the sign payload for.\n * @returns The sign payload.\n */\nexport function getSignPayload(\n  envelope: TransactionEnvelopeEip7702,\n): getSignPayload.ReturnType {\n  return hash(envelope, { presign: true })\n}\n\nexport declare namespace getSignPayload {\n  type ReturnType = Hex.Hex\n\n  type ErrorType = hash.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Hashes a {@link ox#TransactionEnvelopeEip7702.TransactionEnvelopeEip7702}. This is the \"transaction hash\".\n *\n * @example\n * ```ts twoslash\n * // @noErrors\n * import { Secp256k1, TransactionEnvelopeEip7702 } from 'ox'\n *\n * const envelope = TransactionEnvelopeEip7702.from({\n *   authorizationList: [...],\n *   chainId: 1,\n *   nonce: 0n,\n *   maxFeePerGas: 1000000000n,\n *   gas: 21000n,\n *   to: '******************************************',\n *   value: 1000000000000000000n,\n * })\n *\n * const signature = Secp256k1.sign({\n *   payload: TransactionEnvelopeEip7702.getSignPayload(envelope),\n *   privateKey: '0x...'\n * })\n *\n * const envelope_signed = TransactionEnvelopeEip7702.from(envelope, { signature })\n *\n * const hash = TransactionEnvelopeEip7702.hash(envelope_signed) // [!code focus]\n * ```\n *\n * @param envelope - The EIP-7702 Transaction Envelope to hash.\n * @param options - Options.\n * @returns The hash of the transaction envelope.\n */\nexport function hash<presign extends boolean = false>(\n  envelope: TransactionEnvelopeEip7702<presign extends true ? false : true>,\n  options: hash.Options<presign> = {},\n): hash.ReturnType {\n  const { presign } = options\n  return Hash.keccak256(\n    serialize({\n      ...envelope,\n      ...(presign\n        ? {\n            r: undefined,\n            s: undefined,\n            yParity: undefined,\n          }\n        : {}),\n    }),\n  )\n}\n\nexport declare namespace hash {\n  type Options<presign extends boolean = false> = {\n    /** Whether to hash this transaction for signing. @default false */\n    presign?: presign | boolean | undefined\n  }\n\n  type ReturnType = Hex.Hex\n\n  type ErrorType =\n    | Hash.keccak256.ErrorType\n    | serialize.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Serializes a {@link ox#TransactionEnvelopeEip7702.TransactionEnvelopeEip7702}.\n *\n * @example\n * ```ts twoslash\n * // @noErrors\n * import { Authorization, Secp256k1, TransactionEnvelopeEip7702, Value } from 'ox'\n *\n * const authorization = Authorization.from({\n *   address: '******************************************',\n *   chainId: 1,\n *   nonce: 0n,\n * })\n *\n * const signature = Secp256k1.sign({\n *   payload: Authorization.getSignPayload(authorization),\n *   privateKey: '0x...',\n * })\n *\n * const authorizationList = [Authorization.from(authorization, { signature })]\n *\n * const envelope = TransactionEnvelopeEip7702.from({\n *   authorizationList,\n *   chainId: 1,\n *   maxFeePerGas: Value.fromGwei('10'),\n *   to: '******************************************',\n *   value: Value.fromEther('1'),\n * })\n *\n * const serialized = TransactionEnvelopeEip7702.serialize(envelope) // [!code focus]\n * ```\n *\n * @example\n * ### Attaching Signatures\n *\n * It is possible to attach a `signature` to the serialized Transaction Envelope.\n *\n * ```ts twoslash\n * // @noErrors\n * import { Secp256k1, TransactionEnvelopeEip7702, Value } from 'ox'\n *\n * const envelope = TransactionEnvelopeEip7702.from({\n *   authorizationList: [...],\n *   chainId: 1,\n *   maxFeePerGas: Value.fromGwei('10'),\n *   to: '******************************************',\n *   value: Value.fromEther('1'),\n * })\n *\n * const signature = Secp256k1.sign({\n *   payload: TransactionEnvelopeEip7702.getSignPayload(envelope),\n *   privateKey: '0x...',\n * })\n *\n * const serialized = TransactionEnvelopeEip7702.serialize(envelope, { // [!code focus]\n *   signature, // [!code focus]\n * }) // [!code focus]\n *\n * // ... send `serialized` transaction to JSON-RPC `eth_sendRawTransaction`\n * ```\n *\n * @param envelope - The Transaction Envelope to serialize.\n * @param options - Options.\n * @returns The serialized Transaction Envelope.\n */\nexport function serialize(\n  envelope: PartialBy<TransactionEnvelopeEip7702, 'type'>,\n  options: serialize.Options = {},\n): Serialized {\n  const {\n    authorizationList,\n    chainId,\n    gas,\n    nonce,\n    to,\n    value,\n    maxFeePerGas,\n    maxPriorityFeePerGas,\n    accessList,\n    data,\n    input,\n  } = envelope\n\n  assert(envelope)\n\n  const accessTupleList = AccessList.toTupleList(accessList)\n  const authorizationTupleList = Authorization.toTupleList(authorizationList)\n\n  const signature = Signature.extract(options.signature || envelope)\n\n  const serialized = [\n    Hex.fromNumber(chainId),\n    nonce ? Hex.fromNumber(nonce) : '0x',\n    maxPriorityFeePerGas ? Hex.fromNumber(maxPriorityFeePerGas) : '0x',\n    maxFeePerGas ? Hex.fromNumber(maxFeePerGas) : '0x',\n    gas ? Hex.fromNumber(gas) : '0x',\n    to ?? '0x',\n    value ? Hex.fromNumber(value) : '0x',\n    data ?? input ?? '0x',\n    accessTupleList,\n    authorizationTupleList,\n    ...(signature ? Signature.toTuple(signature) : []),\n  ]\n\n  return Hex.concat(serializedType, Rlp.fromHex(serialized)) as Serialized\n}\n\nexport declare namespace serialize {\n  type Options = {\n    /** Signature to append to the serialized Transaction Envelope. */\n    signature?: Signature.Signature | undefined\n  }\n\n  type ErrorType =\n    | assert.ErrorType\n    | Hex.fromNumber.ErrorType\n    | Signature.toTuple.ErrorType\n    | Hex.concat.ErrorType\n    | Rlp.fromHex.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Validates a {@link ox#TransactionEnvelopeEip7702.TransactionEnvelopeEip7702}. Returns `true` if the envelope is valid, `false` otherwise.\n *\n * @example\n * ```ts twoslash\n * import { TransactionEnvelopeEip7702, Value } from 'ox'\n *\n * const valid = TransactionEnvelopeEip7702.validate({\n *   authorizationList: [],\n *   maxFeePerGas: 2n ** 256n - 1n + 1n,\n *   chainId: 1,\n *   to: '******************************************',\n *   value: Value.fromEther('1'),\n * })\n * // @log: false\n * ```\n *\n * @param envelope - The transaction envelope to validate.\n */\nexport function validate(\n  envelope: PartialBy<TransactionEnvelopeEip7702, 'type'>,\n) {\n  try {\n    assert(envelope)\n    return true\n  } catch {\n    return false\n  }\n}\n\nexport declare namespace validate {\n  type ErrorType = Errors.GlobalErrorType\n}\n", "import * as Address from './Address.js'\nimport type * as Errors from './Errors.js'\nimport * as Hash from './Hash.js'\nimport * as Hex from './Hex.js'\nimport * as Rlp from './Rlp.js'\nimport * as Signature from './Signature.js'\nimport * as TransactionEnvelope from './TransactionEnvelope.js'\nimport type {\n  Assign,\n  Branded,\n  Compute,\n  PartialBy,\n  UnionPartialBy,\n} from './internal/types.js'\n\nexport type TransactionEnvelopeLegacy<\n  signed extends boolean = boolean,\n  bigintType = bigint,\n  numberType = number,\n  type extends string = Type,\n> = Compute<\n  PartialBy<\n    TransactionEnvelope.Base<type, signed, bigintType, numberType>,\n    'chainId'\n  > & {\n    /** Base fee per gas. */\n    gasPrice?: bigintType | undefined\n  }\n>\n\nexport type Rpc<signed extends boolean = boolean> = TransactionEnvelopeLegacy<\n  signed,\n  Hex.Hex,\n  Hex.Hex,\n  '0x0'\n>\n\nexport type Serialized = Branded<`0x${string}`, 'legacy'>\n\nexport type Signed = TransactionEnvelopeLegacy<true>\n\nexport const type = 'legacy'\nexport type Type = typeof type\n\n/**\n * Asserts a {@link ox#TransactionEnvelopeLegacy.TransactionEnvelopeLegacy} is valid.\n *\n * @example\n * ```ts twoslash\n * import { TransactionEnvelopeLegacy, Value } from 'ox'\n *\n * TransactionEnvelopeLegacy.assert({\n *   gasPrice: 2n ** 256n - 1n + 1n,\n *   chainId: 1,\n *   to: '******************************************',\n *   value: Value.fromEther('1'),\n * })\n * // @error: GasPriceTooHighError:\n * // @error: The gas price (`gasPrice` = 115792089237316195423570985008687907853269984665640564039457584007913 gwei) cannot be\n * // @error: higher than the maximum allowed value (2^256-1).\n * ```\n *\n * @param envelope - The transaction envelope to assert.\n */\nexport function assert(envelope: PartialBy<TransactionEnvelopeLegacy, 'type'>) {\n  const { chainId, gasPrice, to } = envelope\n  if (to) Address.assert(to, { strict: false })\n  if (typeof chainId !== 'undefined' && chainId <= 0)\n    throw new TransactionEnvelope.InvalidChainIdError({ chainId })\n  if (gasPrice && BigInt(gasPrice) > 2n ** 256n - 1n)\n    throw new TransactionEnvelope.GasPriceTooHighError({ gasPrice })\n}\n\nexport declare namespace assert {\n  type ErrorType =\n    | Address.assert.ErrorType\n    | TransactionEnvelope.InvalidChainIdError\n    | TransactionEnvelope.GasPriceTooHighError\n    | Errors.GlobalErrorType\n}\n\n/**\n * Deserializes a {@link ox#TransactionEnvelopeLegacy.TransactionEnvelopeLegacy} from its serialized form.\n *\n * @example\n * ```ts twoslash\n * import { TransactionEnvelopeLegacy } from 'ox'\n *\n * const envelope = TransactionEnvelopeLegacy.deserialize('0x01ef0182031184773594008477359400809470997970c51812dc3a010c7d01b50e0d17dc79c8880de0b6b3a764000080c0')\n * // @log: {\n * // @log:   type: 'legacy',\n * // @log:   nonce: 785n,\n * // @log:   gasPrice: 2000000000n,\n * // @log:   gas: 1000000n,\n * // @log:   to: '******************************************',\n * // @log:   value: 1000000000000000000n,\n * // @log: }\n * ```\n *\n * @param serialized - The serialized transaction.\n * @returns Deserialized Transaction Envelope.\n */\nexport function deserialize(\n  serialized: Hex.Hex,\n): Compute<TransactionEnvelopeLegacy> {\n  const tuple = Rlp.toHex(serialized)\n\n  const [nonce, gasPrice, gas, to, value, data, chainIdOrV_, r, s] =\n    tuple as readonly Hex.Hex[]\n\n  if (!(tuple.length === 6 || tuple.length === 9))\n    throw new TransactionEnvelope.InvalidSerializedError({\n      attributes: {\n        nonce,\n        gasPrice,\n        gas,\n        to,\n        value,\n        data,\n        ...(tuple.length > 6\n          ? {\n              v: chainIdOrV_,\n              r,\n              s,\n            }\n          : {}),\n      },\n      serialized,\n      type,\n    })\n\n  const transaction = {\n    type,\n  } as TransactionEnvelopeLegacy\n  if (Hex.validate(to) && to !== '0x') transaction.to = to\n  if (Hex.validate(gas) && gas !== '0x') transaction.gas = BigInt(gas)\n  if (Hex.validate(data) && data !== '0x') transaction.data = data\n  if (Hex.validate(nonce) && nonce !== '0x') transaction.nonce = BigInt(nonce)\n  if (Hex.validate(value) && value !== '0x') transaction.value = BigInt(value)\n  if (Hex.validate(gasPrice) && gasPrice !== '0x')\n    transaction.gasPrice = BigInt(gasPrice)\n\n  if (tuple.length === 6) return transaction\n\n  const chainIdOrV =\n    Hex.validate(chainIdOrV_) && chainIdOrV_ !== '0x'\n      ? Number(chainIdOrV_ as Hex.Hex)\n      : 0\n\n  if (s === '0x' && r === '0x') {\n    if (chainIdOrV > 0) transaction.chainId = Number(chainIdOrV)\n    return transaction\n  }\n\n  const v = chainIdOrV\n  const chainId: number | undefined = Math.floor((v - 35) / 2)\n  if (chainId > 0) transaction.chainId = chainId\n  else if (v !== 27 && v !== 28) throw new Signature.InvalidVError({ value: v })\n\n  transaction.yParity = Signature.vToYParity(v)\n  transaction.v = v\n  transaction.s = s === '0x' ? 0n : BigInt(s!)\n  transaction.r = r === '0x' ? 0n : BigInt(r!)\n\n  assert(transaction)\n\n  return transaction\n}\n\nexport declare namespace deserialize {\n  type ErrorType = Errors.GlobalErrorType\n}\n\n/**\n * Converts an arbitrary transaction object into a legacy Transaction Envelope.\n *\n * @example\n * ```ts twoslash\n * import { TransactionEnvelopeLegacy, Value } from 'ox'\n *\n * const envelope = TransactionEnvelopeLegacy.from({\n *   gasPrice: Value.fromGwei('10'),\n *   to: '******************************************',\n *   value: Value.fromEther('1'),\n * })\n * ```\n *\n * @example\n * ### Attaching Signatures\n *\n * It is possible to attach a `signature` to the transaction envelope.\n *\n * ```ts twoslash\n * import { Secp256k1, TransactionEnvelopeLegacy, Value } from 'ox'\n *\n * const envelope = TransactionEnvelopeLegacy.from({\n *   chainId: 1,\n *   gasPrice: Value.fromGwei('10'),\n *   to: '******************************************',\n *   value: Value.fromEther('1'),\n * })\n *\n * const signature = Secp256k1.sign({\n *   payload: TransactionEnvelopeLegacy.getSignPayload(envelope),\n *   privateKey: '0x...',\n * })\n *\n * const envelope_signed = TransactionEnvelopeLegacy.from(envelope, { // [!code focus]\n *   signature, // [!code focus]\n * }) // [!code focus]\n * // @log: {\n * // @log:   authorizationList: [...],\n * // @log:   chainId: 1,\n * // @log:   gasPrice: 10000000000n,\n * // @log:   to: '******************************************',\n * // @log:   type: 'eip7702',\n * // @log:   value: 1000000000000000000n,\n * // @log:   r: 125...n,\n * // @log:   s: 642...n,\n * // @log:   yParity: 0,\n * // @log: }\n * ```\n *\n * @example\n * ### From Serialized\n *\n * It is possible to instantiate an legacy Transaction Envelope from a {@link ox#TransactionEnvelopeLegacy.Serialized} value.\n *\n * ```ts twoslash\n * import { TransactionEnvelopeLegacy } from 'ox'\n *\n * const envelope = TransactionEnvelopeLegacy.from('0xf858018203118502540be4008504a817c800809470997970c51812dc3a010c7d01b50e0d17dc79c8880de0b6b3a764000080c08477359400e1a001627c687261b0e7f8638af1112efa8a77e23656f6e7945275b19e9deed80261')\n * // @log: {\n * // @log:   chainId: 1,\n * // @log:   gasPrice: 10000000000n,\n * // @log:   to: '******************************************',\n * // @log:   type: 'legacy',\n * // @log:   value: 1000000000000000000n,\n * // @log: }\n * ```\n *\n * @param envelope - The transaction object to convert.\n * @param options - Options.\n * @returns A legacy Transaction Envelope.\n */\nexport function from<\n  const envelope extends\n    | UnionPartialBy<TransactionEnvelopeLegacy, 'type'>\n    | Hex.Hex,\n  const signature extends Signature.Signature | undefined = undefined,\n>(\n  envelope:\n    | envelope\n    | UnionPartialBy<TransactionEnvelopeLegacy, 'type'>\n    | Hex.Hex,\n  options: from.Options<signature> = {},\n): from.ReturnType<envelope, signature> {\n  const { signature } = options\n\n  const envelope_ = (\n    typeof envelope === 'string' ? deserialize(envelope) : envelope\n  ) as TransactionEnvelopeLegacy\n\n  assert(envelope_)\n\n  const signature_ = (() => {\n    if (!signature) return {}\n    const s = Signature.from(signature) as any\n    s.v = Signature.yParityToV(s.yParity)\n    return s\n  })()\n\n  return {\n    ...envelope_,\n    ...signature_,\n    type: 'legacy',\n  } as never\n}\n\nexport declare namespace from {\n  type Options<signature extends Signature.Signature | undefined = undefined> =\n    {\n      signature?: signature | Signature.Signature | undefined\n    }\n\n  type ReturnType<\n    envelope extends\n      | UnionPartialBy<TransactionEnvelopeLegacy, 'type'>\n      | Hex.Hex = TransactionEnvelopeLegacy | Hex.Hex,\n    signature extends Signature.Signature | undefined = undefined,\n  > = Compute<\n    envelope extends Hex.Hex\n      ? TransactionEnvelopeLegacy\n      : Assign<\n          envelope,\n          (signature extends Signature.Signature\n            ? Readonly<\n                signature & {\n                  v: signature['yParity'] extends 0 ? 27 : 28\n                }\n              >\n            : {}) & {\n            readonly type: 'legacy'\n          }\n        >\n  >\n\n  type ErrorType =\n    | deserialize.ErrorType\n    | assert.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Returns the payload to sign for a {@link ox#TransactionEnvelopeLegacy.TransactionEnvelopeLegacy}.\n *\n * @example\n * The example below demonstrates how to compute the sign payload which can be used\n * with ECDSA signing utilities like {@link ox#Secp256k1.(sign:function)}.\n *\n * ```ts twoslash\n * // @noErrors\n * import { Secp256k1, TransactionEnvelopeLegacy } from 'ox'\n *\n * const envelope = TransactionEnvelopeLegacy.from({\n *   nonce: 0n,\n *   gasPrice: 1000000000n,\n *   gas: 21000n,\n *   to: '******************************************',\n *   value: 1000000000000000000n,\n * })\n *\n * const payload = TransactionEnvelopeLegacy.getSignPayload(envelope) // [!code focus]\n * // @log: '0x...'\n *\n * const signature = Secp256k1.sign({ payload, privateKey: '0x...' })\n * ```\n *\n * @param envelope - The transaction envelope to get the sign payload for.\n * @returns The sign payload.\n */\nexport function getSignPayload(\n  envelope: TransactionEnvelopeLegacy<false>,\n): getSignPayload.ReturnType {\n  return hash(envelope, { presign: true })\n}\n\nexport declare namespace getSignPayload {\n  type ReturnType = Hex.Hex\n\n  type ErrorType = hash.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Hashes a {@link ox#TransactionEnvelopeLegacy.TransactionEnvelopeLegacy}. This is the \"transaction hash\".\n *\n * @example\n * ```ts twoslash\n * import { Secp256k1, TransactionEnvelopeLegacy } from 'ox'\n *\n * const envelope = TransactionEnvelopeLegacy.from({\n *   chainId: 1,\n *   nonce: 0n,\n *   gasPrice: 1000000000n,\n *   gas: 21000n,\n *   to: '******************************************',\n *   value: 1000000000000000000n,\n * })\n *\n * const signature = Secp256k1.sign({\n *   payload: TransactionEnvelopeLegacy.getSignPayload(envelope),\n *   privateKey: '0x...'\n * })\n *\n * const envelope_signed = TransactionEnvelopeLegacy.from(envelope, { signature })\n *\n * const hash = TransactionEnvelopeLegacy.hash(envelope_signed) // [!code focus]\n * ```\n *\n * @param envelope - The Legacy Transaction Envelope to hash.\n * @param options - Options.\n * @returns The hash of the transaction envelope.\n */\nexport function hash<presign extends boolean = false>(\n  envelope: TransactionEnvelopeLegacy<presign extends true ? false : true>,\n  options: hash.Options<presign> = {},\n): hash.ReturnType {\n  const { presign } = options\n  return Hash.keccak256(\n    serialize({\n      ...envelope,\n      ...(presign\n        ? {\n            r: undefined,\n            s: undefined,\n            yParity: undefined,\n            v: undefined,\n          }\n        : {}),\n    }),\n  )\n}\n\nexport declare namespace hash {\n  type Options<presign extends boolean = false> = {\n    /** Whether to hash this transaction for signing. @default false */\n    presign?: presign | boolean | undefined\n  }\n\n  type ReturnType = Hex.Hex\n\n  type ErrorType =\n    | Hash.keccak256.ErrorType\n    | serialize.ErrorType\n    | Errors.GlobalErrorType\n}\n\n/**\n * Serializes a {@link ox#TransactionEnvelopeLegacy.TransactionEnvelopeLegacy}.\n *\n * @example\n * ```ts twoslash\n * // @noErrors\n * import { TransactionEnvelopeLegacy } from 'ox'\n *\n * const envelope = TransactionEnvelopeLegacy.from({\n *   chainId: 1,\n *   gasPrice: Value.fromGwei('10'),\n *   to: '******************************************',\n *   value: Value.fromEther('1'),\n * })\n *\n * const serialized = TransactionEnvelopeLegacy.serialize(envelope) // [!code focus]\n * ```\n *\n * @example\n * ### Attaching Signatures\n *\n * It is possible to attach a `signature` to the serialized Transaction Envelope.\n *\n * ```ts twoslash\n * // @noErrors\n * import { Secp256k1, TransactionEnvelopeLegacy, Value } from 'ox'\n *\n * const envelope = TransactionEnvelopeLegacy.from({\n *   chainId: 1,\n *   gasPrice: Value.fromGwei('10'),\n *   to: '******************************************',\n *   value: Value.fromEther('1'),\n * })\n *\n * const signature = Secp256k1.sign({\n *   payload: TransactionEnvelopeLegacy.getSignPayload(envelope),\n *   privateKey: '0x...',\n * })\n *\n * const serialized = TransactionEnvelopeLegacy.serialize(envelope, { // [!code focus]\n *   signature, // [!code focus]\n * }) // [!code focus]\n *\n * // ... send `serialized` transaction to JSON-RPC `eth_sendRawTransaction`\n * ```\n *\n * @param envelope - The Transaction Envelope to serialize.\n * @param options - Options.\n * @returns The serialized Transaction Envelope.\n */\nexport function serialize(\n  envelope: PartialBy<TransactionEnvelopeLegacy, 'type'>,\n  options: serialize.Options = {},\n): Serialized {\n  const { chainId = 0, gas, data, input, nonce, to, value, gasPrice } = envelope\n\n  assert(envelope)\n\n  let serialized = [\n    nonce ? Hex.fromNumber(nonce) : '0x',\n    gasPrice ? Hex.fromNumber(gasPrice) : '0x',\n    gas ? Hex.fromNumber(gas) : '0x',\n    to ?? '0x',\n    value ? Hex.fromNumber(value) : '0x',\n    data ?? input ?? '0x',\n  ]\n\n  const signature = (() => {\n    if (options.signature)\n      return {\n        r: options.signature.r,\n        s: options.signature.s,\n        v: Signature.yParityToV(options.signature.yParity),\n      }\n\n    if (typeof envelope.r === 'undefined' || typeof envelope.s === 'undefined')\n      return undefined\n    return {\n      r: envelope.r,\n      s: envelope.s,\n      v: envelope.v!,\n    }\n  })()\n\n  if (signature) {\n    const v = (() => {\n      // EIP-155 (inferred chainId)\n      if (signature.v >= 35) {\n        const inferredChainId = Math.floor((signature.v - 35) / 2)\n        if (inferredChainId > 0) return signature.v\n        return 27 + (signature.v === 35 ? 0 : 1)\n      }\n\n      // EIP-155 (explicit chainId)\n      if (chainId > 0) return chainId * 2 + 35 + signature.v - 27\n\n      // Pre-EIP-155 (no chainId)\n      const v = 27 + (signature.v === 27 ? 0 : 1)\n      if (signature.v !== v)\n        throw new Signature.InvalidVError({ value: signature.v })\n      return v\n    })()\n\n    serialized = [\n      ...serialized,\n      Hex.fromNumber(v),\n      signature.r === 0n ? '0x' : Hex.trimLeft(Hex.fromNumber(signature.r)),\n      signature.s === 0n ? '0x' : Hex.trimLeft(Hex.fromNumber(signature.s)),\n    ]\n  } else if (chainId > 0)\n    serialized = [...serialized, Hex.fromNumber(chainId), '0x', '0x']\n\n  return Rlp.fromHex(serialized) as never\n}\n\nexport declare namespace serialize {\n  type Options = {\n    /** Signature to append to the serialized Transaction Envelope. */\n    signature?: Signature.Signature | undefined\n  }\n\n  type ErrorType =\n    | assert.ErrorType\n    | Hex.fromNumber.ErrorType\n    | Hex.trimLeft.ErrorType\n    | Rlp.fromHex.ErrorType\n    | Signature.InvalidVError\n    | Errors.GlobalErrorType\n}\n\n/**\n * Converts an {@link ox#TransactionEnvelopeLegacy.TransactionEnvelopeLegacy} to an {@link ox#TransactionEnvelopeLegacy.Rpc}.\n *\n * @example\n * ```ts twoslash\n * import { RpcRequest, TransactionEnvelopeLegacy, Value } from 'ox'\n *\n * const envelope = TransactionEnvelopeLegacy.from({\n *   chainId: 1,\n *   nonce: 0n,\n *   gas: 21000n,\n *   to: '******************************************',\n *   value: Value.fromEther('1'),\n * })\n *\n * const envelope_rpc = TransactionEnvelopeLegacy.toRpc(envelope) // [!code focus]\n *\n * const request = RpcRequest.from({\n *   id: 0,\n *   method: 'eth_sendTransaction',\n *   params: [envelope_rpc],\n * })\n * ```\n *\n * @param envelope - The legacy transaction envelope to convert.\n * @returns An RPC-formatted legacy transaction envelope.\n */\nexport function toRpc(envelope: Omit<TransactionEnvelopeLegacy, 'type'>): Rpc {\n  const signature = Signature.extract(envelope)!\n\n  return {\n    ...envelope,\n    chainId:\n      typeof envelope.chainId === 'number'\n        ? Hex.fromNumber(envelope.chainId)\n        : undefined,\n    data: envelope.data ?? envelope.input,\n    type: '0x0',\n    ...(typeof envelope.gas === 'bigint'\n      ? { gas: Hex.fromNumber(envelope.gas) }\n      : {}),\n    ...(typeof envelope.nonce === 'bigint'\n      ? { nonce: Hex.fromNumber(envelope.nonce) }\n      : {}),\n    ...(typeof envelope.value === 'bigint'\n      ? { value: Hex.fromNumber(envelope.value) }\n      : {}),\n    ...(typeof envelope.gasPrice === 'bigint'\n      ? { gasPrice: Hex.fromNumber(envelope.gasPrice) }\n      : {}),\n    ...(signature\n      ? {\n          ...Signature.toRpc(signature),\n          v: signature.yParity === 0 ? '0x1b' : '0x1c',\n        }\n      : {}),\n  } as never\n}\n\nexport declare namespace toRpc {\n  export type ErrorType = Signature.extract.ErrorType | Errors.GlobalErrorType\n}\n\n/**\n * Validates a {@link ox#TransactionEnvelopeLegacy.TransactionEnvelopeLegacy}. Returns `true` if the envelope is valid, `false` otherwise.\n *\n * @example\n * ```ts twoslash\n * import { TransactionEnvelopeLegacy, Value } from 'ox'\n *\n * const valid = TransactionEnvelopeLegacy.assert({\n *   gasPrice: 2n ** 256n - 1n + 1n,\n *   chainId: 1,\n *   to: '******************************************',\n *   value: Value.fromEther('1'),\n * })\n * // @log: false\n * ```\n *\n * @param envelope - The transaction envelope to validate.\n */\nexport function validate(\n  envelope: PartialBy<TransactionEnvelopeLegacy, 'type'>,\n) {\n  try {\n    assert(envelope)\n    return true\n  } catch {\n    return false\n  }\n}\n\nexport declare namespace validate {\n  type ErrorType = Errors.GlobalErrorType\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;cAAAA;EAAA;;;AAGO,IAAM,YAAY;EACvB,KAAK;EACL,MAAM;EACN,OAAO;EACP,QAAQ;EACR,OAAO;;AAkBH,SAAU,OAAO,OAAe,WAAW,GAAC;AAChD,MAAI,UAAU,MAAM,SAAQ;AAE5B,QAAM,WAAW,QAAQ,WAAW,GAAG;AACvC,MAAI;AAAU,cAAU,QAAQ,MAAM,CAAC;AAEvC,YAAU,QAAQ,SAAS,UAAU,GAAG;AAExC,MAAI,CAAC,SAAS,QAAQ,IAAI;IACxB,QAAQ,MAAM,GAAG,QAAQ,SAAS,QAAQ;IAC1C,QAAQ,MAAM,QAAQ,SAAS,QAAQ;;AAEzC,aAAW,SAAS,QAAQ,SAAS,EAAE;AACvC,SAAO,GAAG,WAAW,MAAM,EAAE,GAAG,WAAW,GAAG,GAC5C,WAAW,IAAI,QAAQ,KAAK,EAC9B;AACF;AAqBM,SAAU,YACd,KACA,OAA4C,OAAK;AAEjD,SAAO,OAAO,KAAK,UAAU,QAAQ,UAAU,IAAI,CAAC;AACtD;AAqBM,SAAU,WAAW,KAAa,OAAc,OAAK;AACzD,SAAO,OAAO,KAAK,UAAU,OAAO,UAAU,IAAI,CAAC;AACrD;AAqBM,SAAUC,MAAK,OAAe,WAAW,GAAC;AAC9C,MAAI,CAAC,4BAA4B,KAAK,KAAK;AACzC,UAAM,IAAI,0BAA0B,EAAE,MAAK,CAAE;AAE/C,MAAI,CAAC,UAAU,IAAI,WAAW,GAAG,IAAI,MAAM,MAAM,GAAG;AAEpD,QAAM,WAAW,QAAQ,WAAW,GAAG;AACvC,MAAI;AAAU,cAAU,QAAQ,MAAM,CAAC;AAGvC,aAAW,SAAS,QAAQ,SAAS,EAAE;AAGvC,MAAI,aAAa,GAAG;AAClB,QAAI,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC,MAAM;AACzC,gBAAU,GAAG,OAAO,OAAO,IAAI,EAAE;AACnC,eAAW;EACb,WAAW,SAAS,SAAS,UAAU;AACrC,UAAM,CAAC,MAAM,MAAM,KAAK,IAAI;MAC1B,SAAS,MAAM,GAAG,WAAW,CAAC;MAC9B,SAAS,MAAM,WAAW,GAAG,QAAQ;MACrC,SAAS,MAAM,QAAQ;;AAGzB,UAAM,UAAU,KAAK,MAAM,OAAO,GAAG,IAAI,IAAI,KAAK,EAAE,CAAC;AACrD,QAAI,UAAU;AACZ,iBAAW,GAAG,OAAO,IAAI,IAAI,OAAO,CAAC,CAAC,IAAI,SAAS,KAAK,SAAS,GAAG,GAAG;;AACpE,iBAAW,GAAG,IAAI,GAAG,OAAO;AAEjC,QAAI,SAAS,SAAS,UAAU;AAC9B,iBAAW,SAAS,MAAM,CAAC;AAC3B,gBAAU,GAAG,OAAO,OAAO,IAAI,EAAE;IACnC;AAEA,eAAW,SAAS,MAAM,GAAG,QAAQ;EACvC,OAAO;AACL,eAAW,SAAS,OAAO,UAAU,GAAG;EAC1C;AAEA,SAAO,OAAO,GAAG,WAAW,MAAM,EAAE,GAAG,OAAO,GAAG,QAAQ,EAAE;AAC7D;AAqBM,SAAU,UACd,OACA,OAA4C,OAAK;AAEjD,SAAOA,MAAK,OAAO,UAAU,QAAQ,UAAU,IAAI,CAAC;AACtD;AAqBM,SAAU,SAAS,MAAc,OAAc,OAAK;AACxD,SAAOA,MAAK,MAAM,UAAU,OAAO,UAAU,IAAI,CAAC;AACpD;AAiBM,IAAO,4BAAP,cAAgD,UAAS;EAE7D,YAAY,EAAE,MAAK,GAAqB;AACtC,UAAM,WAAW,KAAK,mCAAmC;AAFzC,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAGzB;;;;AC9NF;;gBAAAC;EAAA;cAAAC;EAAA;;;;eAAAC;EAAA;kBAAAC;;;;ACAA;;;;;;AAiDM,SAAU,cAAc,YAAiB;AAC7C,QAAM,OAA4B,CAAA;AAClC,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC1C,UAAM,CAAC,SAAS,WAAW,IAAI,WAAW,CAAC;AAE3C,QAAI;AAAS,MAAQ,OAAO,SAAS,EAAE,QAAQ,MAAK,CAAE;AAEtD,SAAK,KAAK;MACR;MACA,aAAa,YAAY,IAAI,CAAC,QACvBC,UAAS,GAAG,IAAI,MAAU,SAAS,GAAG,CAAC;KAE/C;EACH;AACA,SAAO;AACT;AA+BM,SAAU,YACd,YAAmC;AAEnC,MAAI,CAAC,cAAc,WAAW,WAAW;AAAG,WAAO,CAAA;AAEnD,QAAM,QAAwB,CAAA;AAC9B,aAAW,EAAE,SAAS,YAAW,KAAM,YAAY;AACjD,aAAS,IAAI,GAAG,IAAI,YAAY,QAAQ;AACtC,UAAQ,KAAK,YAAY,CAAC,CAAE,MAAM;AAChC,cAAM,IAAI,2BAA2B;UACnC,YAAY,YAAY,CAAC;SAC1B;AAEL,QAAI;AAAS,MAAQ,OAAO,SAAS,EAAE,QAAQ,MAAK,CAAE;AAEtD,UAAM,KAAK,CAAC,SAAS,WAAW,CAAC;EACnC;AACA,SAAO;AACT;AAGM,IAAO,6BAAP,cAAiD,UAAS;EAE9D,YAAY,EAAE,WAAU,GAA2B;AACjD,UACE,yBAAyB,UAAU,wCAA4C,KAAK,UAAU,CAAC,SAAS;AAH1F,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAKzB;;;;AC1HF;;;cAAAC;EAAA,iBAAAC;EAAA,eAAAC;EAAA;;;;;;AAmBM,SAAU,QACd,OAA4B;AAE5B,SAAO,GAAG,OAAO,OAAO;AAC1B;AAmBM,SAAU,MAAM,OAA4B;AAChD,SAAO,GAAG,OAAO,KAAK;AACxB;AAWM,SAAU,GAGd,OAAcC,KAAwB;AACtC,QAAM,MAAMA,QAAO,OAAO,UAAU,WAAW,QAAQ;AAEvD,QAAM,SAAS,MAAK;AAClB,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,MAAM,SAAS,KAAK,MAAM,SAAS,MAAM;AAC3C,cAAM,IAAQ,mBAAmB,KAAK;AACxC,aAAa,QAAQ,KAAK;IAC5B;AACA,WAAO;EACT,GAAE;AAEF,QAAM,SAAgB,OAAO,OAAO;IAClC,oBAAoB,OAAO;GAC5B;AACD,QAAM,SAAS,gBAAgB,QAAQ,GAAG;AAE1C,SAAO;AACT;AAmBM,SAAU,gBACd,QACAA,MAAuC,OAAK;AAE5C,MAAI,OAAO,MAAM,WAAW;AAC1B,WACEA,QAAO,QAAY,UAAU,OAAO,KAAK,IAAI,OAAO;AAGxD,QAAM,SAAS,OAAO,SAAQ;AAC9B,MAAI,SAAS;AAAM,WAAO,kBAAkB,CAAC;AAG7C,MAAI,SAAS,KAAM;AACjB,UAAMC,UAAS,WAAW,QAAQ,QAAQ,GAAI;AAC9C,UAAM,QAAQ,OAAO,UAAUA,OAAM;AACrC,WACED,QAAO,QAAY,UAAU,KAAK,IAAI;EAE1C;AAGA,QAAM,SAAS,WAAW,QAAQ,QAAQ,GAAI;AAC9C,SAAO,SAAS,QAAQ,QAAQA,GAAE;AACpC;AAaM,SAAU,WACd,QACA,QACA,QAAc;AAEd,MAAI,WAAW,OAAQ,SAAS;AAAM,WAAO;AAC7C,MAAI,UAAU,SAAS;AAAI,WAAO,SAAS;AAC3C,MAAI,WAAW,SAAS,KAAK;AAAG,WAAO,OAAO,UAAS;AACvD,MAAI,WAAW,SAAS,KAAK;AAAG,WAAO,OAAO,WAAU;AACxD,MAAI,WAAW,SAAS,KAAK;AAAG,WAAO,OAAO,WAAU;AACxD,MAAI,WAAW,SAAS,KAAK;AAAG,WAAO,OAAO,WAAU;AACxD,QAAM,IAAW,UAAU,oBAAoB;AACjD;AAQM,SAAU,SACd,QACA,QACAA,KAAwB;AAExB,QAAM,WAAW,OAAO;AACxB,QAAM,QAA0C,CAAA;AAChD,SAAO,OAAO,WAAW,WAAW;AAClC,UAAM,KAAK,gBAAgB,QAAQA,GAAE,CAAC;AACxC,SAAO;AACT;AA8BM,SAAUE,MACd,OACA,SAAyB;AAEzB,QAAM,EAAE,GAAE,IAAK;AAEf,QAAM,YAAY,aAAa,KAAK;AACpC,QAAM,SAAgB,OAAO,IAAI,WAAW,UAAU,MAAM,CAAC;AAC7D,YAAU,OAAO,MAAM;AAEvB,MAAI,OAAO;AAAO,WAAW,UAAU,OAAO,KAAK;AACnD,SAAO,OAAO;AAChB;AAkCM,SAAUC,WACd,OACA,UAAiC,CAAA,GAAE;AAEnC,QAAM,EAAE,KAAK,QAAO,IAAK;AACzB,SAAOD,MAAK,OAAO,EAAE,GAAE,CAAE;AAC3B;AA2BM,SAAUE,SACd,KACA,UAA+B,CAAA,GAAE;AAEjC,QAAM,EAAE,KAAK,MAAK,IAAK;AACvB,SAAOF,MAAK,KAAK,EAAE,GAAE,CAAE;AACzB;AAgBA,SAAS,aACP,OAA4D;AAE5D,MAAI,MAAM,QAAQ,KAAK;AACrB,WAAO,iBAAiB,MAAM,IAAI,CAAC,MAAM,aAAa,CAAC,CAAC,CAAC;AAC3D,SAAO,kBAAkB,KAAY;AACvC;AAEA,SAAS,iBAAiB,MAAiB;AACzC,QAAM,aAAa,KAAK,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,QAAQ,CAAC;AAE5D,QAAM,mBAAmB,gBAAgB,UAAU;AACnD,QAAM,UAAU,MAAK;AACnB,QAAI,cAAc;AAAI,aAAO,IAAI;AACjC,WAAO,IAAI,mBAAmB;EAChC,GAAE;AAEF,SAAO;IACL;IACA,OAAO,QAAqB;AAC1B,UAAI,cAAc,IAAI;AACpB,eAAO,SAAS,MAAO,UAAU;MACnC,OAAO;AACL,eAAO,SAAS,MAAO,KAAK,gBAAgB;AAC5C,YAAI,qBAAqB;AAAG,iBAAO,UAAU,UAAU;iBAC9C,qBAAqB;AAAG,iBAAO,WAAW,UAAU;iBACpD,qBAAqB;AAAG,iBAAO,WAAW,UAAU;;AACxD,iBAAO,WAAW,UAAU;MACnC;AACA,iBAAW,EAAE,OAAM,KAAM,MAAM;AAC7B,eAAO,MAAM;MACf;IACF;;AAEJ;AAEA,SAAS,kBAAkB,YAAiC;AAC1D,QAAM,QACJ,OAAO,eAAe,WAAiB,QAAQ,UAAU,IAAI;AAE/D,QAAM,oBAAoB,gBAAgB,MAAM,MAAM;AACtD,QAAM,UAAU,MAAK;AACnB,QAAI,MAAM,WAAW,KAAK,MAAM,CAAC,IAAK;AAAM,aAAO;AACnD,QAAI,MAAM,UAAU;AAAI,aAAO,IAAI,MAAM;AACzC,WAAO,IAAI,oBAAoB,MAAM;EACvC,GAAE;AAEF,SAAO;IACL;IACA,OAAO,QAAqB;AAC1B,UAAI,MAAM,WAAW,KAAK,MAAM,CAAC,IAAK,KAAM;AAC1C,eAAO,UAAU,KAAK;MACxB,WAAW,MAAM,UAAU,IAAI;AAC7B,eAAO,SAAS,MAAO,MAAM,MAAM;AACnC,eAAO,UAAU,KAAK;MACxB,OAAO;AACL,eAAO,SAAS,MAAO,KAAK,iBAAiB;AAC7C,YAAI,sBAAsB;AAAG,iBAAO,UAAU,MAAM,MAAM;iBACjD,sBAAsB;AAAG,iBAAO,WAAW,MAAM,MAAM;iBACvD,sBAAsB;AAAG,iBAAO,WAAW,MAAM,MAAM;;AAC3D,iBAAO,WAAW,MAAM,MAAM;AACnC,eAAO,UAAU,KAAK;MACxB;IACF;;AAEJ;AAEA,SAAS,gBAAgB,QAAc;AACrC,MAAI,SAAS,KAAK;AAAG,WAAO;AAC5B,MAAI,SAAS,KAAK;AAAI,WAAO;AAC7B,MAAI,SAAS,KAAK;AAAI,WAAO;AAC7B,MAAI,SAAS,KAAK;AAAI,WAAO;AAC7B,QAAM,IAAW,UAAU,sBAAsB;AACnD;;;AC7WA;;;;;;;;AAiEM,IAAO,qBAAP,cAAyC,UAAS;EAEtD,YAAY,EACV,OAAM,IAGJ,CAAA,GAAE;AACJ,UACE,yDACE,SAAS,MAAY,WAAW,MAAM,CAAC,UAAU,EACnD,8DAA8D;AAThD,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAWzB;;AAiBI,IAAO,uBAAP,cAA2C,UAAS;EAExD,YAAY,EACV,SAAQ,IAGN,CAAA,GAAE;AACJ,UACE,8BACE,WAAW,MAAY,WAAW,QAAQ,CAAC,UAAU,EACvD,8DAA8D;AAThD,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAWzB;;AAcI,IAAO,sBAAP,cAA0C,UAAS;EAEvD,YAAY,EAAE,QAAO,GAAoC;AACvD,UACE,OAAO,YAAY,cACf,aAAa,OAAO,kBACpB,sBAAsB;AALZ,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAOzB;;AAgBI,IAAO,yBAAP,cAA6C,UAAS;EAE1D,YAAY,EACV,YACA,YACA,MAAAG,MAAI,GAKL;AACC,UAAM,UAAU,OAAO,QAAQ,UAAU,EACtC,IAAI,CAAC,CAAC,KAAK,KAAK,MAAO,OAAO,UAAU,cAAc,MAAM,MAAU,EACtE,OAAO,OAAO;AACjB,UAAM,2CAA2CA,KAAI,mBAAmB;MACtE,cAAc;QACZ,4BAA4B,UAAU;QACtC,QAAQ,SAAS,IAAI,uBAAuB,QAAQ,KAAK,IAAI,CAAC,KAAK;QACnE,OAAO,OAAO;KACjB;AAlBe,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAmBzB;;AAkBI,IAAO,sBAAP,cAA0C,UAAS;EAEvD,YAAY,EACV,sBACA,aAAY,IAIV,CAAA,GAAE;AACJ,UACE;MACE,6CACE,uBACI,MAAY,WAAW,oBAAoB,CAAC,UAC5C,EACN,wDACE,eAAe,MAAY,WAAW,YAAY,CAAC,UAAU,EAC/D;MACA,KAAK,IAAI,CAAC;AAjBE,WAAA,eAAA,MAAA,QAAA;;;;aAAO;;EAmBzB;;;;AHnKK,IAAM,iBAAiB;AAKvB,IAAM,OAAO;AAuBd,SAAUC,QACd,UAAuD;AAEvD,QAAM,EAAE,SAAS,sBAAsB,cAAc,IAAAC,IAAE,IAAK;AAC5D,MAAI,WAAW;AACb,UAAM,IAAwB,oBAAoB,EAAE,QAAO,CAAE;AAC/D,MAAIA;AAAI,IAAQ,OAAOA,KAAI,EAAE,QAAQ,MAAK,CAAE;AAC5C,MAAI,gBAAgB,OAAO,YAAY,IAAI,MAAM,OAAO;AACtD,UAAM,IAAwB,mBAAmB,EAAE,QAAQ,aAAY,CAAE;AAC3E,MACE,wBACA,gBACA,uBAAuB;AAEvB,UAAM,IAAwB,oBAAoB;MAChD;MACA;KACD;AACL;AAgCM,SAAU,YACd,YAAsB;AAEtB,QAAM,mBAAuB,MAAU,MAAM,YAAY,CAAC,CAAC;AAE3D,QAAM,CACJ,SACA,OACA,sBACA,cACA,KACAA,KACA,OACA,MACA,YACA,SACA,GACA,CAAC,IACC;AAEJ,MAAI,EAAE,iBAAiB,WAAW,KAAK,iBAAiB,WAAW;AACjE,UAAM,IAAwB,uBAAuB;MACnD,YAAY;QACV;QACA;QACA;QACA;QACA;QACA,IAAAA;QACA;QACA;QACA;QACA,GAAI,iBAAiB,SAAS,IAC1B;UACE;UACA;UACA;YAEF,CAAA;;MAEN;MACA;KACD;AAEH,MAAI,cAAc;IAChB,SAAS,OAAO,OAAO;IACvB;;AAEF,MAAQ,SAASA,GAAE,KAAKA,QAAO;AAAM,gBAAY,KAAKA;AACtD,MAAQ,SAAS,GAAG,KAAK,QAAQ;AAAM,gBAAY,MAAM,OAAO,GAAG;AACnE,MAAQ,SAAS,IAAI,KAAK,SAAS;AAAM,gBAAY,OAAO;AAC5D,MAAQ,SAAS,KAAK,KAAK,UAAU;AAAM,gBAAY,QAAQ,OAAO,KAAK;AAC3E,MAAQ,SAAS,KAAK,KAAK,UAAU;AAAM,gBAAY,QAAQ,OAAO,KAAK;AAC3E,MAAQ,SAAS,YAAY,KAAK,iBAAiB;AACjD,gBAAY,eAAe,OAAO,YAAY;AAChD,MAAQ,SAAS,oBAAoB,KAAK,yBAAyB;AACjE,gBAAY,uBAAuB,OAAO,oBAAoB;AAChE,MAAI,WAAY,WAAW,KAAK,eAAe;AAC7C,gBAAY,aAAwB,cAAc,UAAiB;AAErE,QAAM,YACJ,KAAK,KAAK,UAAoB,UAAU,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI;AAC7D,MAAI;AACF,kBAAc;MACZ,GAAG;MACH,GAAG;;AAGP,EAAAD,QAAO,WAAW;AAElB,SAAO;AACT;AAkFM,SAAUE,MAMd,UAIA,UAAmC,CAAA,GAAE;AAErC,QAAM,EAAE,UAAS,IAAK;AAEtB,QAAM,YACJ,OAAO,aAAa,WAAW,YAAY,QAAQ,IAAI;AAGzD,EAAAF,QAAO,SAAS;AAEhB,SAAO;IACL,GAAG;IACH,GAAI,YAAsB,KAAK,SAAS,IAAI,CAAA;IAC5C,MAAM;;AAEV;AA0DM,SAAU,eACd,UAAoC;AAEpC,SAAO,KAAK,UAAU,EAAE,SAAS,KAAI,CAAE;AACzC;AAsCM,SAAU,KACd,UACA,UAAiC,CAAA,GAAE;AAEnC,QAAM,EAAE,QAAO,IAAK;AACpB,SAAY,UACV,UAAU;IACR,GAAG;IACH,GAAI,UACA;MACE,GAAG;MACH,GAAG;MACH,SAAS;MACT,GAAG;QAEL,CAAA;GACL,CAAC;AAEN;AAkEM,SAAU,UACd,UACA,UAA6B,CAAA,GAAE;AAE/B,QAAM,EACJ,SACA,KACA,OACA,IAAAC,KACA,OACA,cACA,sBACA,YACA,MACA,MAAK,IACH;AAEJ,EAAAD,QAAO,QAAQ;AAEf,QAAM,kBAA6B,YAAY,UAAU;AAEzD,QAAM,YAAsB,QAAQ,QAAQ,aAAa,QAAQ;AAEjE,QAAM,aAAa;IACb,WAAW,OAAO;IACtB,QAAY,WAAW,KAAK,IAAI;IAChC,uBAA2B,WAAW,oBAAoB,IAAI;IAC9D,eAAmB,WAAW,YAAY,IAAI;IAC9C,MAAU,WAAW,GAAG,IAAI;IAC5BC,OAAM;IACN,QAAY,WAAW,KAAK,IAAI;IAChC,QAAQ,SAAS;IACjB;IACA,GAAI,YAAsB,QAAQ,SAAS,IAAI,CAAA;;AAGjD,SAAW,OAAO,gBAAoBE,SAAQ,UAAU,CAAC;AAC3D;AA4CM,SAAUC,OAAM,UAAkD;AACtE,QAAM,YAAsB,QAAQ,QAAQ;AAE5C,SAAO;IACL,GAAG;IACH,SAAa,WAAW,SAAS,OAAO;IACxC,MAAM,SAAS,QAAQ,SAAS;IAChC,MAAM;IACN,GAAI,OAAO,SAAS,QAAQ,WACxB,EAAE,KAAS,WAAW,SAAS,GAAG,EAAC,IACnC,CAAA;IACJ,GAAI,OAAO,SAAS,UAAU,WAC1B,EAAE,OAAW,WAAW,SAAS,KAAK,EAAC,IACvC,CAAA;IACJ,GAAI,OAAO,SAAS,UAAU,WAC1B,EAAE,OAAW,WAAW,SAAS,KAAK,EAAC,IACvC,CAAA;IACJ,GAAI,OAAO,SAAS,iBAAiB,WACjC,EAAE,cAAkB,WAAW,SAAS,YAAY,EAAC,IACrD,CAAA;IACJ,GAAI,OAAO,SAAS,yBAAyB,WACzC;MACE,sBAA0B,WAAW,SAAS,oBAAoB;QAEpE,CAAA;IACJ,GAAI,YAAsB,MAAM,SAAS,IAAI,CAAA;;AAEjD;AAwBM,SAAUC,UACd,UAAuD;AAEvD,MAAI;AACF,IAAAL,QAAO,QAAQ;AACf,WAAO;EACT,QAAQ;AACN,WAAO;EACT;AACF;;;AI7mBA;;gBAAAM;EAAA,mBAAAC;EAAA,YAAAC;EAAA,sBAAAC;EAAA,YAAAC;EAAA,iBAAAC;EAAA,sBAAAC;EAAA,aAAAC;EAAA,YAAAC;EAAA,gBAAAC;;AAsCO,IAAMC,kBAAiB;AAKvB,IAAMC,QAAO;AAuBd,SAAUC,QACd,UAAuD;AAEvD,QAAM,EAAE,SAAS,UAAU,IAAAC,IAAE,IAAK;AAClC,MAAI,WAAW;AACb,UAAM,IAAwB,oBAAoB,EAAE,QAAO,CAAE;AAC/D,MAAIA;AAAI,IAAQ,OAAOA,KAAI,EAAE,QAAQ,MAAK,CAAE;AAC5C,MAAI,YAAY,OAAO,QAAQ,IAAI,MAAM,OAAO;AAC9C,UAAM,IAAwB,qBAAqB,EAAE,SAAQ,CAAE;AACnE;AA+BM,SAAUC,aACd,YAAsB;AAEtB,QAAM,mBAAuB,MAAU,MAAM,YAAY,CAAC,CAAC;AAE3D,QAAM,CACJ,SACA,OACA,UACA,KACAD,KACA,OACA,MACA,YACA,SACA,GACA,CAAC,IACC;AAEJ,MAAI,EAAE,iBAAiB,WAAW,KAAK,iBAAiB,WAAW;AACjE,UAAM,IAAwB,uBAAuB;MACnD,YAAY;QACV;QACA;QACA;QACA;QACA,IAAAA;QACA;QACA;QACA;QACA,GAAI,iBAAiB,SAAS,IAC1B;UACE;UACA;UACA;YAEF,CAAA;;MAEN;MACA,MAAAF;KACD;AAEH,MAAI,cAAc;IAChB,SAAS,OAAO,OAAkB;IAClC,MAAAA;;AAEF,MAAQ,SAASE,GAAE,KAAKA,QAAO;AAAM,gBAAY,KAAKA;AACtD,MAAQ,SAAS,GAAG,KAAK,QAAQ;AAAM,gBAAY,MAAM,OAAO,GAAG;AACnE,MAAQ,SAAS,IAAI,KAAK,SAAS;AAAM,gBAAY,OAAO;AAC5D,MAAQ,SAAS,KAAK,KAAK,UAAU;AAAM,gBAAY,QAAQ,OAAO,KAAK;AAC3E,MAAQ,SAAS,KAAK,KAAK,UAAU;AAAM,gBAAY,QAAQ,OAAO,KAAK;AAC3E,MAAQ,SAAS,QAAQ,KAAK,aAAa;AACzC,gBAAY,WAAW,OAAO,QAAQ;AACxC,MAAI,WAAY,WAAW,KAAK,eAAe;AAC7C,gBAAY,aAAwB,cAAc,UAAiB;AAErE,QAAM,YACJ,KAAK,KAAK,UAAoB,UAAU,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI;AAC7D,MAAI;AACF,kBAAc;MACZ,GAAG;MACH,GAAG;;AAGP,EAAAD,QAAO,WAAW;AAElB,SAAO;AACT;AAgFM,SAAUG,MAMd,UAIA,UAAmC,CAAA,GAAE;AAErC,QAAM,EAAE,UAAS,IAAK;AAEtB,QAAM,YACJ,OAAO,aAAa,WAAWD,aAAY,QAAQ,IAAI;AAGzD,EAAAF,QAAO,SAAS;AAEhB,SAAO;IACL,GAAG;IACH,GAAI,YAAsB,KAAK,SAAS,IAAI,CAAA;IAC5C,MAAM;;AAEV;AA0DM,SAAUI,gBACd,UAAoC;AAEpC,SAAOC,MAAK,UAAU,EAAE,SAAS,KAAI,CAAE;AACzC;AAwCM,SAAUA,MACd,UACA,UAAiC,CAAA,GAAE;AAEnC,QAAM,EAAE,QAAO,IAAK;AACpB,SAAY,UACVC,WAAU;IACR,GAAG;IACH,GAAI,UACA;MACE,GAAG;MACH,GAAG;MACH,SAAS;MACT,GAAG;QAEL,CAAA;GACL,CAAC;AAEN;AAgEM,SAAUA,WACd,UACA,UAA6B,CAAA,GAAE;AAE/B,QAAM,EAAE,SAAS,KAAK,MAAM,OAAO,OAAO,IAAAL,KAAI,OAAO,YAAY,SAAQ,IACvE;AAEF,EAAAD,QAAO,QAAQ;AAEf,QAAM,kBAA6B,YAAY,UAAU;AAEzD,QAAM,YAAsB,QAAQ,QAAQ,aAAc,QAAgB;AAE1E,QAAM,aAAa;IACb,WAAW,OAAO;IACtB,QAAY,WAAW,KAAK,IAAI;IAChC,WAAe,WAAW,QAAQ,IAAI;IACtC,MAAU,WAAW,GAAG,IAAI;IAC5BC,OAAM;IACN,QAAY,WAAW,KAAK,IAAI;IAChC,QAAQ,SAAS;IACjB;IACA,GAAI,YAAsB,QAAQ,SAAS,IAAI,CAAA;;AAGjD,SAAW,OAAO,QAAYM,SAAQ,UAAU,CAAC;AACnD;AA6CM,SAAUC,OAAM,UAAkD;AACtE,QAAM,YAAsB,QAAQ,QAAQ;AAE5C,SAAO;IACL,GAAG;IACH,SAAa,WAAW,SAAS,OAAO;IACxC,MAAM,SAAS,QAAQ,SAAS;IAChC,GAAI,OAAO,SAAS,QAAQ,WACxB,EAAE,KAAS,WAAW,SAAS,GAAG,EAAC,IACnC,CAAA;IACJ,GAAI,OAAO,SAAS,UAAU,WAC1B,EAAE,OAAW,WAAW,SAAS,KAAK,EAAC,IACvC,CAAA;IACJ,GAAI,OAAO,SAAS,UAAU,WAC1B,EAAE,OAAW,WAAW,SAAS,KAAK,EAAC,IACvC,CAAA;IACJ,GAAI,OAAO,SAAS,aAAa,WAC7B,EAAE,UAAc,WAAW,SAAS,QAAQ,EAAC,IAC7C,CAAA;IACJ,MAAM;IACN,GAAI,YAAsB,MAAM,SAAS,IAAI,CAAA;;AAEjD;AAwBM,SAAUC,UACd,UAAuD;AAEvD,MAAI;AACF,IAAAT,QAAO,QAAQ;AACf,WAAO;EACT,QAAQ;AACN,WAAO;EACT;AACF;;;AC1kBA;;cAAAU;EAAA;;mBAAAC;EAAA,qBAAAC;EAAA,sBAAAC;EAAA,YAAAC;EAAA,aAAAC;EAAA;iBAAAC;EAAA,mBAAAC;;AAoHM,SAAUC,MAId,eACA,UAAmC,CAAA,GAAE;AAErC,MAAI,OAAO,cAAc,YAAY;AACnC,WAAO,QAAQ,aAAa;AAC9B,SAAO,EAAE,GAAG,eAAe,GAAG,QAAQ,UAAS;AACjD;AA+CM,SAAU,QAAQ,eAAkB;AACxC,QAAM,EAAE,SAAS,SAAS,MAAK,IAAK;AACpC,QAAM,YAAsB,QAAQ,aAAa;AAEjD,SAAO;IACL;IACA,SAAS,OAAO,OAAO;IACvB,OAAO,OAAO,KAAK;IACnB,GAAG;;AAEP;AA0BM,SAAU,YAAY,mBAA0B;AACpD,SAAO,kBAAkB,IAAI,OAAO;AACtC;AAoDM,SAAUC,WACd,OAAY;AAEZ,QAAM,CAAC,SAAS,SAAS,OAAO,SAAS,GAAG,CAAC,IAAI;AACjD,QAAM,YACJ,WAAW,KAAK,IAAc,UAAU,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI;AAC7D,SAAOD,MAAK;IACV;IACA,SAAS,OAAO,OAAO;IACvB,OAAO,OAAO,KAAK;IACnB,GAAG;GACJ;AACH;AAoEM,SAAUE,eACd,WAAoB;AAEpB,QAAM,OAAsB,CAAA;AAC5B,aAAW,SAAS;AAAW,SAAK,KAAKD,WAAU,KAAK,CAAC;AACzD,SAAO;AACT;AAqCM,SAAUE,gBAAe,eAA4B;AACzD,SAAOC,MAAK,aAAa;AAC3B;AAyBM,SAAUA,MAAK,eAA4B;AAC/C,SAAY,UAAc,OAAO,QAAYC,SAAQC,SAAQ,aAAa,CAAC,CAAC,CAAC;AAC/E;AA+BM,SAAUC,OAAM,eAAqB;AACzC,QAAM,EAAE,SAAS,SAAS,OAAO,GAAG,UAAS,IAAK;AAElD,SAAO;IACL;IACA,SAAa,WAAW,OAAO;IAC/B,OAAW,WAAW,KAAK;IAC3B,GAAa,MAAM,SAAS;;AAEhC;AA0BM,SAAU,UAAU,mBAA6B;AACrD,SAAO,kBAAkB,IAAIA,MAAK;AACpC;AA8BM,SAAUD,SACd,eAA4B;AAE5B,QAAM,EAAE,SAAS,SAAS,MAAK,IAAK;AACpC,QAAM,YAAsB,QAAQ,aAAa;AACjD,SAAO;IACL,UAAc,WAAW,OAAO,IAAI;IACpC;IACA,QAAY,WAAW,KAAK,IAAI;IAChC,GAAI,YAAsB,QAAQ,SAAS,IAAI,CAAA;;AAEnD;AA6CM,SAAUE,aAId,MAAuB;AACvB,MAAI,CAAC,QAAQ,KAAK,WAAW;AAAG,WAAO,CAAA;AAEvC,QAAM,YAAgC,CAAA;AACtC,aAAW,iBAAiB;AAAM,cAAU,KAAKF,SAAQ,aAAa,CAAC;AAEvE,SAAO;AACT;;;AClkBA;;gBAAAG;EAAA,mBAAAC;EAAA,YAAAC;EAAA,sBAAAC;EAAA,YAAAC;EAAA,iBAAAC;EAAA,sBAAAC;EAAA,YAAAC;EAAA,gBAAAC;;AAyCO,IAAMC,kBAAiB;AAGvB,IAAMC,QAAO;AAwBd,SAAUC,QACd,UAAuD;AAEvD,QAAM,EAAE,kBAAiB,IAAK;AAC9B,MAAI,mBAAmB;AACrB,eAAW,iBAAiB,mBAAmB;AAC7C,YAAM,EAAE,SAAS,QAAO,IAAK;AAC7B,UAAI;AAAS,QAAQ,OAAO,SAAS,EAAE,QAAQ,MAAK,CAAE;AACtD,UAAI,OAAO,OAAO,IAAI;AACpB,cAAM,IAAwB,oBAAoB,EAAE,QAAO,CAAE;IACjE;EACF;AACA,EAA2BA,QACzB,QAAuE;AAE3E;AA+BM,SAAUC,aACd,YAAsB;AAEtB,QAAM,mBAAuB,MAAU,MAAM,YAAY,CAAC,CAAC;AAE3D,QAAM,CACJ,SACA,OACA,sBACA,cACA,KACAC,KACA,OACA,MACA,YACA,mBACA,SACA,GACA,CAAC,IACC;AAEJ,MAAI,EAAE,iBAAiB,WAAW,MAAM,iBAAiB,WAAW;AAClE,UAAM,IAAwB,uBAAuB;MACnD,YAAY;QACV;QACA;QACA;QACA;QACA;QACA,IAAAA;QACA;QACA;QACA;QACA;QACA,GAAI,iBAAiB,SAAS,IAC1B;UACE;UACA;UACA;YAEF,CAAA;;MAEN;MACA,MAAAH;KACD;AAEH,MAAI,cAAc;IAChB,SAAS,OAAO,OAAO;IACvB,MAAAA;;AAEF,MAAQ,SAASG,GAAE,KAAKA,QAAO;AAAM,gBAAY,KAAKA;AACtD,MAAQ,SAAS,GAAG,KAAK,QAAQ;AAAM,gBAAY,MAAM,OAAO,GAAG;AACnE,MAAQ,SAAS,IAAI,KAAK,SAAS;AAAM,gBAAY,OAAO;AAC5D,MAAQ,SAAS,KAAK,KAAK,UAAU;AAAM,gBAAY,QAAQ,OAAO,KAAK;AAC3E,MAAQ,SAAS,KAAK,KAAK,UAAU;AAAM,gBAAY,QAAQ,OAAO,KAAK;AAC3E,MAAQ,SAAS,YAAY,KAAK,iBAAiB;AACjD,gBAAY,eAAe,OAAO,YAAY;AAChD,MAAQ,SAAS,oBAAoB,KAAK,yBAAyB;AACjE,gBAAY,uBAAuB,OAAO,oBAAoB;AAChE,MAAI,WAAY,WAAW,KAAK,eAAe;AAC7C,gBAAY,aAAwB,cAAc,UAAmB;AACvE,MAAI,sBAAsB;AACxB,gBAAY,oBAAkCC,eAC5C,iBAA0B;AAG9B,QAAM,YACJ,KAAK,KAAK,UAAoB,UAAU,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI;AAC7D,MAAI;AACF,kBAAc;MACZ,GAAG;MACH,GAAG;;AAGP,EAAAH,QAAO,WAAW;AAElB,SAAO;AACT;AAmGM,SAAUI,MAMd,UAIA,UAAmC,CAAA,GAAE;AAErC,QAAM,EAAE,UAAS,IAAK;AAEtB,QAAM,YACJ,OAAO,aAAa,WAAWH,aAAY,QAAQ,IAAI;AAGzD,EAAAD,QAAO,SAAS;AAEhB,SAAO;IACL,GAAG;IACH,GAAI,YAAsB,KAAK,SAAS,IAAI,CAAA;IAC5C,MAAM;;AAEV;AA4DM,SAAUK,gBACd,UAAoC;AAEpC,SAAOC,MAAK,UAAU,EAAE,SAAS,KAAI,CAAE;AACzC;AAwCM,SAAUA,MACd,UACA,UAAiC,CAAA,GAAE;AAEnC,QAAM,EAAE,QAAO,IAAK;AACpB,SAAY,UACVC,WAAU;IACR,GAAG;IACH,GAAI,UACA;MACE,GAAG;MACH,GAAG;MACH,SAAS;QAEX,CAAA;GACL,CAAC;AAEN;AAiFM,SAAUA,WACd,UACA,UAA6B,CAAA,GAAE;AAE/B,QAAM,EACJ,mBACA,SACA,KACA,OACA,IAAAL,KACA,OACA,cACA,sBACA,YACA,MACA,MAAK,IACH;AAEJ,EAAAF,QAAO,QAAQ;AAEf,QAAM,kBAA6B,YAAY,UAAU;AACzD,QAAM,yBAAuCQ,aAAY,iBAAiB;AAE1E,QAAM,YAAsB,QAAQ,QAAQ,aAAa,QAAQ;AAEjE,QAAM,aAAa;IACb,WAAW,OAAO;IACtB,QAAY,WAAW,KAAK,IAAI;IAChC,uBAA2B,WAAW,oBAAoB,IAAI;IAC9D,eAAmB,WAAW,YAAY,IAAI;IAC9C,MAAU,WAAW,GAAG,IAAI;IAC5BN,OAAM;IACN,QAAY,WAAW,KAAK,IAAI;IAChC,QAAQ,SAAS;IACjB;IACA;IACA,GAAI,YAAsB,QAAQ,SAAS,IAAI,CAAA;;AAGjD,SAAW,OAAOJ,iBAAoBW,SAAQ,UAAU,CAAC;AAC3D;AAoCM,SAAUC,UACd,UAAuD;AAEvD,MAAI;AACF,IAAAV,QAAO,QAAQ;AACf,WAAO;EACT,QAAQ;AACN,WAAO;EACT;AACF;;;AC9lBA;;gBAAAW;EAAA,mBAAAC;EAAA,YAAAC;EAAA,sBAAAC;EAAA,YAAAC;EAAA,iBAAAC;EAAA,aAAAC;EAAA,YAAAC;EAAA,gBAAAC;;AAyCO,IAAMC,QAAO;AAuBd,SAAUC,QAAO,UAAsD;AAC3E,QAAM,EAAE,SAAS,UAAU,IAAAC,IAAE,IAAK;AAClC,MAAIA;AAAI,IAAQ,OAAOA,KAAI,EAAE,QAAQ,MAAK,CAAE;AAC5C,MAAI,OAAO,YAAY,eAAe,WAAW;AAC/C,UAAM,IAAwB,oBAAoB,EAAE,QAAO,CAAE;AAC/D,MAAI,YAAY,OAAO,QAAQ,IAAI,MAAM,OAAO;AAC9C,UAAM,IAAwB,qBAAqB,EAAE,SAAQ,CAAE;AACnE;AA+BM,SAAUC,aACd,YAAmB;AAEnB,QAAM,QAAY,MAAM,UAAU;AAElC,QAAM,CAAC,OAAO,UAAU,KAAKD,KAAI,OAAO,MAAM,aAAa,GAAG,CAAC,IAC7D;AAEF,MAAI,EAAE,MAAM,WAAW,KAAK,MAAM,WAAW;AAC3C,UAAM,IAAwB,uBAAuB;MACnD,YAAY;QACV;QACA;QACA;QACA,IAAAA;QACA;QACA;QACA,GAAI,MAAM,SAAS,IACf;UACE,GAAG;UACH;UACA;YAEF,CAAA;;MAEN;MACA,MAAAF;KACD;AAEH,QAAM,cAAc;IAClB,MAAAA;;AAEF,MAAQ,SAASE,GAAE,KAAKA,QAAO;AAAM,gBAAY,KAAKA;AACtD,MAAQ,SAAS,GAAG,KAAK,QAAQ;AAAM,gBAAY,MAAM,OAAO,GAAG;AACnE,MAAQ,SAAS,IAAI,KAAK,SAAS;AAAM,gBAAY,OAAO;AAC5D,MAAQ,SAAS,KAAK,KAAK,UAAU;AAAM,gBAAY,QAAQ,OAAO,KAAK;AAC3E,MAAQ,SAAS,KAAK,KAAK,UAAU;AAAM,gBAAY,QAAQ,OAAO,KAAK;AAC3E,MAAQ,SAAS,QAAQ,KAAK,aAAa;AACzC,gBAAY,WAAW,OAAO,QAAQ;AAExC,MAAI,MAAM,WAAW;AAAG,WAAO;AAE/B,QAAM,aACA,SAAS,WAAW,KAAK,gBAAgB,OACzC,OAAO,WAAsB,IAC7B;AAEN,MAAI,MAAM,QAAQ,MAAM,MAAM;AAC5B,QAAI,aAAa;AAAG,kBAAY,UAAU,OAAO,UAAU;AAC3D,WAAO;EACT;AAEA,QAAM,IAAI;AACV,QAAM,UAA8B,KAAK,OAAO,IAAI,MAAM,CAAC;AAC3D,MAAI,UAAU;AAAG,gBAAY,UAAU;WAC9B,MAAM,MAAM,MAAM;AAAI,UAAM,IAAc,cAAc,EAAE,OAAO,EAAC,CAAE;AAE7E,cAAY,UAAoB,WAAW,CAAC;AAC5C,cAAY,IAAI;AAChB,cAAY,IAAI,MAAM,OAAO,KAAK,OAAO,CAAE;AAC3C,cAAY,IAAI,MAAM,OAAO,KAAK,OAAO,CAAE;AAE3C,EAAAD,QAAO,WAAW;AAElB,SAAO;AACT;AA8EM,SAAUG,MAMd,UAIA,UAAmC,CAAA,GAAE;AAErC,QAAM,EAAE,UAAS,IAAK;AAEtB,QAAM,YACJ,OAAO,aAAa,WAAWD,aAAY,QAAQ,IAAI;AAGzD,EAAAF,QAAO,SAAS;AAEhB,QAAM,cAAc,MAAK;AACvB,QAAI,CAAC;AAAW,aAAO,CAAA;AACvB,UAAM,IAAc,KAAK,SAAS;AAClC,MAAE,IAAc,WAAW,EAAE,OAAO;AACpC,WAAO;EACT,GAAE;AAEF,SAAO;IACL,GAAG;IACH,GAAG;IACH,MAAM;;AAEV;AAgEM,SAAUI,gBACd,UAA0C;AAE1C,SAAOC,MAAK,UAAU,EAAE,SAAS,KAAI,CAAE;AACzC;AAsCM,SAAUA,MACd,UACA,UAAiC,CAAA,GAAE;AAEnC,QAAM,EAAE,QAAO,IAAK;AACpB,SAAY,UACVC,WAAU;IACR,GAAG;IACH,GAAI,UACA;MACE,GAAG;MACH,GAAG;MACH,SAAS;MACT,GAAG;QAEL,CAAA;GACL,CAAC;AAEN;AAkEM,SAAUA,WACd,UACA,UAA6B,CAAA,GAAE;AAE/B,QAAM,EAAE,UAAU,GAAG,KAAK,MAAM,OAAO,OAAO,IAAAL,KAAI,OAAO,SAAQ,IAAK;AAEtE,EAAAD,QAAO,QAAQ;AAEf,MAAI,aAAa;IACf,QAAY,WAAW,KAAK,IAAI;IAChC,WAAe,WAAW,QAAQ,IAAI;IACtC,MAAU,WAAW,GAAG,IAAI;IAC5BC,OAAM;IACN,QAAY,WAAW,KAAK,IAAI;IAChC,QAAQ,SAAS;;AAGnB,QAAM,aAAa,MAAK;AACtB,QAAI,QAAQ;AACV,aAAO;QACL,GAAG,QAAQ,UAAU;QACrB,GAAG,QAAQ,UAAU;QACrB,GAAa,WAAW,QAAQ,UAAU,OAAO;;AAGrD,QAAI,OAAO,SAAS,MAAM,eAAe,OAAO,SAAS,MAAM;AAC7D,aAAO;AACT,WAAO;MACL,GAAG,SAAS;MACZ,GAAG,SAAS;MACZ,GAAG,SAAS;;EAEhB,GAAE;AAEF,MAAI,WAAW;AACb,UAAM,KAAK,MAAK;AAEd,UAAI,UAAU,KAAK,IAAI;AACrB,cAAM,kBAAkB,KAAK,OAAO,UAAU,IAAI,MAAM,CAAC;AACzD,YAAI,kBAAkB;AAAG,iBAAO,UAAU;AAC1C,eAAO,MAAM,UAAU,MAAM,KAAK,IAAI;MACxC;AAGA,UAAI,UAAU;AAAG,eAAO,UAAU,IAAI,KAAK,UAAU,IAAI;AAGzD,YAAMM,KAAI,MAAM,UAAU,MAAM,KAAK,IAAI;AACzC,UAAI,UAAU,MAAMA;AAClB,cAAM,IAAc,cAAc,EAAE,OAAO,UAAU,EAAC,CAAE;AAC1D,aAAOA;IACT,GAAE;AAEF,iBAAa;MACX,GAAG;MACC,WAAW,CAAC;MAChB,UAAU,MAAM,KAAK,OAAW,SAAa,WAAW,UAAU,CAAC,CAAC;MACpE,UAAU,MAAM,KAAK,OAAW,SAAa,WAAW,UAAU,CAAC,CAAC;;EAExE,WAAW,UAAU;AACnB,iBAAa,CAAC,GAAG,YAAgB,WAAW,OAAO,GAAG,MAAM,IAAI;AAElE,SAAWC,SAAQ,UAAU;AAC/B;AA4CM,SAAUC,OAAM,UAAiD;AACrE,QAAM,YAAsB,QAAQ,QAAQ;AAE5C,SAAO;IACL,GAAG;IACH,SACE,OAAO,SAAS,YAAY,WACpB,WAAW,SAAS,OAAO,IAC/B;IACN,MAAM,SAAS,QAAQ,SAAS;IAChC,MAAM;IACN,GAAI,OAAO,SAAS,QAAQ,WACxB,EAAE,KAAS,WAAW,SAAS,GAAG,EAAC,IACnC,CAAA;IACJ,GAAI,OAAO,SAAS,UAAU,WAC1B,EAAE,OAAW,WAAW,SAAS,KAAK,EAAC,IACvC,CAAA;IACJ,GAAI,OAAO,SAAS,UAAU,WAC1B,EAAE,OAAW,WAAW,SAAS,KAAK,EAAC,IACvC,CAAA;IACJ,GAAI,OAAO,SAAS,aAAa,WAC7B,EAAE,UAAc,WAAW,SAAS,QAAQ,EAAC,IAC7C,CAAA;IACJ,GAAI,YACA;MACE,GAAa,MAAM,SAAS;MAC5B,GAAG,UAAU,YAAY,IAAI,SAAS;QAExC,CAAA;;AAER;AAwBM,SAAUC,UACd,UAAsD;AAEtD,MAAI;AACF,IAAAV,QAAO,QAAQ;AACf,WAAO;EACT,QAAQ;AACN,WAAO;EACT;AACF;", "names": ["from", "from", "assert", "from", "toRpc", "validate", "validate", "from", "fromBytes", "fromHex", "to", "length", "from", "fromBytes", "fromHex", "type", "assert", "to", "from", "fromHex", "toRpc", "validate", "assert", "deserialize", "from", "getSignPayload", "hash", "serialize", "serializedType", "toRpc", "type", "validate", "serializedType", "type", "assert", "to", "deserialize", "from", "getSignPayload", "hash", "serialize", "fromHex", "toRpc", "validate", "from", "fromTuple", "fromTupleList", "getSignPayload", "hash", "toRpc", "toTuple", "toTupleList", "from", "fromTuple", "fromTupleList", "getSignPayload", "hash", "fromHex", "toTuple", "toRpc", "toTupleList", "assert", "deserialize", "from", "getSignPayload", "hash", "serialize", "serializedType", "type", "validate", "serializedType", "type", "assert", "deserialize", "to", "fromTupleList", "from", "getSignPayload", "hash", "serialize", "toTupleList", "fromHex", "validate", "assert", "deserialize", "from", "getSignPayload", "hash", "serialize", "toRpc", "type", "validate", "type", "assert", "to", "deserialize", "from", "getSignPayload", "hash", "serialize", "v", "fromHex", "toRpc", "validate"]}