import {
  getEcosystemInfo
} from "./chunk-34X5PL56.js";
import {
  track
} from "./chunk-URRAJUJZ.js";
import {
  stringify
} from "./chunk-2CIJO3V3.js";
import {
  getCached<PERSON>hain,
  getCached<PERSON>hainIfExists
} from "./chunk-ITZPY7G6.js";

// node_modules/thirdweb/dist/esm/analytics/track/connect.js
async function trackConnect(args) {
  const { client, ecosystem, walletType, walletAddress, chainId } = args;
  return track({
    client,
    ecosystem,
    data: {
      source: "connectWallet",
      action: "connect",
      walletType,
      walletAddress,
      chainId
    }
  });
}

// node_modules/thirdweb/dist/esm/utils/tiny-emitter.js
function createEmitter() {
  const subsribers = /* @__PURE__ */ new Map();
  return {
    subscribe(event, cb) {
      var _a;
      if (!subsribers.has(event)) {
        subsribers.set(event, /* @__PURE__ */ new Set([cb]));
      } else {
        (_a = subsribers.get(event)) == null ? void 0 : _a.add(cb);
      }
      return () => {
        const subscribers = subsribers.get(event);
        if (subscribers) {
          subscribers.delete(cb);
        }
      };
    },
    emit(event, data) {
      const subscribers = subsribers.get(event);
      if (subscribers) {
        for (const cb of subscribers) {
          cb(data);
        }
      }
    }
  };
}

// node_modules/thirdweb/dist/esm/wallets/wallet-emitter.js
function createWalletEmitter() {
  return createEmitter();
}

// node_modules/thirdweb/dist/esm/wallets/in-app/core/wallet/in-app-core.js
var connectorCache = /* @__PURE__ */ new Map();
async function getOrCreateInAppWalletConnector(client, connectorFactory, ecosystem) {
  var _a;
  const key = stringify({
    clientId: client.clientId,
    ecosystem,
    partialSecretKey: (_a = client.secretKey) == null ? void 0 : _a.slice(0, 5)
  });
  if (connectorCache.has(key)) {
    return connectorCache.get(key);
  }
  const connector = await connectorFactory(client);
  connectorCache.set(key, connector);
  return connector;
}
function createInAppWallet(args) {
  const { createOptions: _createOptions, connectorFactory, ecosystem } = args;
  const walletId = ecosystem ? ecosystem.id : "inApp";
  const emitter = createWalletEmitter();
  let createOptions = _createOptions;
  let account = void 0;
  let adminAccount = void 0;
  let chain = void 0;
  let client;
  return {
    id: walletId,
    subscribe: emitter.subscribe,
    getChain() {
      if (!chain) {
        return void 0;
      }
      chain = getCachedChainIfExists(chain.id) || chain;
      return chain;
    },
    getConfig: () => createOptions,
    getAccount: () => account,
    autoConnect: async (options) => {
      const { autoConnectInAppWallet } = await import("./wallet-SOTKY7ZB.js");
      const connector = await getOrCreateInAppWalletConnector(options.client, connectorFactory, ecosystem);
      if (ecosystem) {
        const ecosystemOptions = await getEcosystemInfo(ecosystem.id);
        const smartAccountOptions = ecosystemOptions == null ? void 0 : ecosystemOptions.smartAccountOptions;
        if (smartAccountOptions) {
          const { defaultChainId } = ecosystemOptions.smartAccountOptions;
          const preferredChain = options.chain ?? (defaultChainId ? getCachedChain(defaultChainId) : void 0);
          if (!preferredChain) {
            throw new Error(`A chain must be provided either via 'chain' in connect options or 'defaultChainId' in ecosystem configuration. Please pass it via connect() or update the ecosystem configuration.`);
          }
          createOptions = {
            ...createOptions,
            smartAccount: {
              chain: preferredChain,
              sponsorGas: smartAccountOptions.sponsorGas,
              factoryAddress: smartAccountOptions.accountFactoryAddress
            }
          };
        }
      }
      const { account: connectedAccount, chain: connectedChain, adminAccount: _adminAccount } = await autoConnectInAppWallet(options, createOptions, connector);
      client = options.client;
      account = connectedAccount;
      adminAccount = _adminAccount;
      chain = connectedChain;
      trackConnect({
        client: options.client,
        ecosystem,
        walletType: walletId,
        walletAddress: account.address,
        chainId: chain.id
      });
      return account;
    },
    connect: async (options) => {
      const { connectInAppWallet } = await import("./wallet-SOTKY7ZB.js");
      const connector = await getOrCreateInAppWalletConnector(options.client, connectorFactory, ecosystem);
      if (ecosystem) {
        const ecosystemOptions = await getEcosystemInfo(ecosystem.id);
        const smartAccountOptions = ecosystemOptions == null ? void 0 : ecosystemOptions.smartAccountOptions;
        if (smartAccountOptions) {
          const { defaultChainId } = ecosystemOptions.smartAccountOptions;
          const preferredChain = options.chain ?? (defaultChainId ? getCachedChain(defaultChainId) : void 0);
          if (!preferredChain) {
            throw new Error(`A chain must be provided either via 'chain' in connect options or 'defaultChainId' in ecosystem configuration. Please pass it via connect() or update the ecosystem configuration.`);
          }
          createOptions = {
            ...createOptions,
            smartAccount: {
              chain: preferredChain,
              sponsorGas: smartAccountOptions.sponsorGas,
              factoryAddress: smartAccountOptions.accountFactoryAddress
            }
          };
        }
      }
      const { account: connectedAccount, chain: connectedChain, adminAccount: _adminAccount } = await connectInAppWallet(options, createOptions, connector);
      client = options.client;
      account = connectedAccount;
      adminAccount = _adminAccount;
      chain = connectedChain;
      trackConnect({
        client: options.client,
        ecosystem,
        walletType: walletId,
        walletAddress: account.address,
        chainId: chain.id
      });
      return account;
    },
    disconnect: async () => {
      if (client) {
        const connector = await getOrCreateInAppWalletConnector(client, connectorFactory, ecosystem);
        const result = await connector.logout();
        if (!result.success) {
          throw new Error("Failed to logout");
        }
      }
      account = void 0;
      adminAccount = void 0;
      chain = void 0;
      emitter.emit("disconnect", void 0);
    },
    switchChain: async (newChain) => {
      if ((createOptions == null ? void 0 : createOptions.smartAccount) && client && account) {
        const { autoConnectInAppWallet } = await import("./wallet-SOTKY7ZB.js");
        const connector = await getOrCreateInAppWalletConnector(client, connectorFactory, ecosystem);
        if (ecosystem) {
          const ecosystemOptions = await getEcosystemInfo(ecosystem.id);
          const smartAccountOptions = ecosystemOptions == null ? void 0 : ecosystemOptions.smartAccountOptions;
          if (smartAccountOptions) {
            createOptions = {
              ...createOptions,
              smartAccount: {
                chain: newChain,
                sponsorGas: smartAccountOptions.sponsorGas,
                factoryAddress: smartAccountOptions.accountFactoryAddress
              }
            };
          }
        }
        const { account: connectedAccount, chain: connectedChain, adminAccount: _adminAccount } = await autoConnectInAppWallet({
          chain: newChain,
          client
        }, createOptions, connector);
        adminAccount = _adminAccount;
        account = connectedAccount;
        chain = connectedChain;
      } else {
        chain = newChain;
      }
      emitter.emit("chainChanged", newChain);
    },
    getAdminAccount: () => adminAccount
  };
}

export {
  trackConnect,
  createWalletEmitter,
  getOrCreateInAppWalletConnector,
  createInAppWallet
};
//# sourceMappingURL=chunk-Z74E7QB2.js.map
