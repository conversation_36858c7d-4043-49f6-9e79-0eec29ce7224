{"version": 3, "sources": ["../../thirdweb/src/utils/nft/parseNft.ts"], "sourcesContent": ["import { get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from \"../../chains/utils.js\";\nimport type { ThirdwebClient } from \"../../client/client.js\";\nimport { getContract } from \"../../contract/contract.js\";\nimport type { FileOrBufferOrString } from \"../../storage/upload/types.js\";\nimport { isAddress } from \"../address.js\";\nimport type { Prettify } from \"../type-utils.js\";\n\n/**\n * Represents the input data for creating an NFT (Non-Fungible Token).\n */\nexport type NFTInput = Prettify<\n  {\n    name?: string;\n    description?: string;\n    image?: FileOrBufferOrString;\n    animation_url?: FileOrBufferOrString;\n    external_url?: FileOrBufferOrString;\n    background_color?: string;\n    // TODO check if we truly need both of these?\n    properties?: Record<string, unknown> | Array<Record<string, unknown>>;\n  } & Record<string, unknown>\n>;\n\nexport type NFTMetadata = {\n  uri: string;\n  name?: string;\n  description?: string;\n  image?: string;\n  animation_url?: string;\n  external_url?: string;\n  background_color?: string;\n  properties?: Record<string, unknown>;\n  attributes?: Record<string, unknown>;\n  image_url?: string;\n} & Record<string, unknown>;\n\nexport type NFT =\n  | {\n      metadata: NFTMetadata;\n      owner: string | null;\n      id: bigint;\n      tokenURI: string;\n      type: \"ERC721\";\n      tokenAddress: string;\n      chainId: number;\n    }\n  | {\n      metadata: NFTMetadata;\n      owner: string | null;\n      id: bigint;\n      tokenURI: string;\n      type: \"ERC1155\";\n      supply: bigint;\n      tokenAddress: string;\n      chainId: number;\n    };\n\n/**\n * @internal\n */\nexport type ParseNFTOptions =\n  | {\n      tokenId: bigint;\n      tokenUri: string;\n      type: \"ERC721\";\n      owner?: string | null;\n      tokenAddress: string;\n      chainId: number;\n    }\n  | {\n      tokenId: bigint;\n      tokenUri: string;\n      type: \"ERC1155\";\n      owner?: string | null;\n      supply: bigint;\n      tokenAddress: string;\n      chainId: number;\n    };\n\n/**\n * Parses the NFT metadata and options to create an NFT object.\n * @param base - The base NFT metadata.\n * @param options - The options for parsing the NFT.\n * @returns The parsed NFT object.\n * @internal\n */\nexport function parseNFT(base: NFTMetadata, options: ParseNFTOptions): NFT {\n  switch (options.type) {\n    case \"ERC721\":\n      return {\n        metadata: base,\n        owner: options?.owner ?? null,\n        id: options.tokenId,\n        tokenURI: options.tokenUri,\n        type: options.type,\n        tokenAddress: options.tokenAddress,\n        chainId: options.chainId,\n      };\n    case \"ERC1155\":\n      return {\n        metadata: base,\n        owner: options?.owner ?? null,\n        id: options.tokenId,\n        tokenURI: options.tokenUri,\n        type: options.type,\n        supply: options.supply,\n        tokenAddress: options.tokenAddress,\n        chainId: options.chainId,\n      };\n    default:\n      throw new Error(\"Invalid NFT type\");\n  }\n}\n\n/**\n * Parses an NFT URI.\n * @param options - The options for parsing an NFT URI.\n * @param options.client - The Thirdweb client.\n * @param options.uri - The NFT URI to parse.\n * @returns A promise that resolves to the NFT URI, or null if the URI could not be parsed.\n *\n * @example\n * ```ts\n * import { parseNftUri } from \"thirdweb/utils/ens\";\n * const nftUri = await parseNftUri({\n *    client,\n *    uri: \"eip155:1/erc1155:0xb32979486938aa9694bfc898f35dbed459f44424/10063\",\n * });\n *\n * console.log(nftUri); // ipfs://bafybeiemxf5abjwjbikoz4mc3a3dla6ual3jsgpdr4cjr3oz3evfyavhwq/\n * ```\n *\n * @extension ENS\n *\n */\nexport async function parseNftUri(options: {\n  client: ThirdwebClient;\n  uri: string;\n}): Promise<string | null> {\n  let uri = options.uri;\n  // parse valid nft spec (CAIP-22/CAIP-29)\n  // @see: https://github.com/ChainAgnostic/CAIPs/tree/master/CAIPs\n  if (uri.startsWith(\"did:nft:\")) {\n    // convert DID to CAIP\n    uri = uri.replace(\"did:nft:\", \"\").replace(/_/g, \"/\");\n  }\n\n  const [reference = \"\", asset_namespace = \"\", tokenID = \"\"] = uri.split(\"/\");\n  const [eip_namespace, chainID] = reference.split(\":\");\n  const [erc_namespace, contractAddress] = asset_namespace.split(\":\");\n\n  if (!eip_namespace || eip_namespace.toLowerCase() !== \"eip155\") {\n    throw new Error(\n      `Invalid EIP namespace, expected EIP155, got: \"${eip_namespace}\"`,\n    );\n  }\n  if (!chainID) {\n    throw new Error(\"Chain ID not found\");\n  }\n  if (!contractAddress || !isAddress(contractAddress)) {\n    throw new Error(\"Contract address not found\");\n  }\n  if (!tokenID) {\n    throw new Error(\"Token ID not found\");\n  }\n  const chain = getCachedChain(Number(chainID));\n  const contract = getContract({\n    client: options.client,\n    chain,\n    address: contractAddress,\n  });\n  switch (erc_namespace) {\n    case \"erc721\": {\n      const { getNFT } = await import(\"../../extensions/erc721/read/getNFT.js\");\n      const nft = await getNFT({\n        contract,\n        tokenId: BigInt(tokenID),\n      });\n      return nft.metadata.image ?? null;\n    }\n    case \"erc1155\": {\n      const { getNFT } = await import(\n        \"../../extensions/erc1155/read/getNFT.js\"\n      );\n      const nft = await getNFT({\n        contract,\n        tokenId: BigInt(tokenID),\n      });\n      return nft.metadata.image ?? null;\n    }\n\n    default: {\n      throw new Error(\n        `Invalid ERC namespace, expected ERC721 or ERC1155, got: \"${erc_namespace}\"`,\n      );\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;AAsFM,SAAU,SAAS,MAAmB,SAAwB;AAClE,UAAQ,QAAQ,MAAM;IACpB,KAAK;AACH,aAAO;QACL,UAAU;QACV,QAAO,mCAAS,UAAS;QACzB,IAAI,QAAQ;QACZ,UAAU,QAAQ;QAClB,MAAM,QAAQ;QACd,cAAc,QAAQ;QACtB,SAAS,QAAQ;;IAErB,KAAK;AACH,aAAO;QACL,UAAU;QACV,QAAO,mCAAS,UAAS;QACzB,IAAI,QAAQ;QACZ,UAAU,QAAQ;QAClB,MAAM,QAAQ;QACd,QAAQ,QAAQ;QAChB,cAAc,QAAQ;QACtB,SAAS,QAAQ;;IAErB;AACE,YAAM,IAAI,MAAM,kBAAkB;EACtC;AACF;AAuBA,eAAsB,YAAY,SAGjC;AACC,MAAI,MAAM,QAAQ;AAGlB,MAAI,IAAI,WAAW,UAAU,GAAG;AAE9B,UAAM,IAAI,QAAQ,YAAY,EAAE,EAAE,QAAQ,MAAM,GAAG;EACrD;AAEA,QAAM,CAAC,YAAY,IAAI,kBAAkB,IAAI,UAAU,EAAE,IAAI,IAAI,MAAM,GAAG;AAC1E,QAAM,CAAC,eAAe,OAAO,IAAI,UAAU,MAAM,GAAG;AACpD,QAAM,CAAC,eAAe,eAAe,IAAI,gBAAgB,MAAM,GAAG;AAElE,MAAI,CAAC,iBAAiB,cAAc,YAAW,MAAO,UAAU;AAC9D,UAAM,IAAI,MACR,iDAAiD,aAAa,GAAG;EAErE;AACA,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,oBAAoB;EACtC;AACA,MAAI,CAAC,mBAAmB,CAAC,UAAU,eAAe,GAAG;AACnD,UAAM,IAAI,MAAM,4BAA4B;EAC9C;AACA,MAAI,CAAC,SAAS;AACZ,UAAM,IAAI,MAAM,oBAAoB;EACtC;AACA,QAAM,QAAQ,eAAe,OAAO,OAAO,CAAC;AAC5C,QAAM,WAAW,YAAY;IAC3B,QAAQ,QAAQ;IAChB;IACA,SAAS;GACV;AACD,UAAQ,eAAe;IACrB,KAAK,UAAU;AACb,YAAM,EAAE,OAAM,IAAK,MAAM,OAAO,sBAAwC;AACxE,YAAM,MAAM,MAAM,OAAO;QACvB;QACA,SAAS,OAAO,OAAO;OACxB;AACD,aAAO,IAAI,SAAS,SAAS;IAC/B;IACA,KAAK,WAAW;AACd,YAAM,EAAE,OAAM,IAAK,MAAM,OACvB,sBAAyC;AAE3C,YAAM,MAAM,MAAM,OAAO;QACvB;QACA,SAAS,OAAO,OAAO;OACxB;AACD,aAAO,IAAI,SAAS,SAAS;IAC/B;IAEA,SAAS;AACP,YAAM,IAAI,MACR,4DAA4D,aAAa,GAAG;IAEhF;EACF;AACF;", "names": []}