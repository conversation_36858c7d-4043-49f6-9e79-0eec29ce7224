import { createThirdwebClient } from "thirdweb";
import { createWallet, inAppWallet } from "thirdweb/wallets";
import {
  ethereum,
  polygon,
  sepolia,
  polygonAmoy,
  arbitrum,
  optimism,
  base,
  bsc,
  avalanche,
  fantom,
  arbitrumSepolia,
  optimismSepolia,
  baseSepolia,
  bscTestnet,
} from "thirdweb/chains";

// Get the client ID from environment variables
const clientId = import.meta.env.VITE_THIRDWEB_CLIENT_ID;

if (!clientId) {
  throw new Error(
    "VITE_THIRDWEB_CLIENT_ID is not set in environment variables"
  );
}

// Create the shared Thirdweb client
export const client = createThirdwebClient({
  clientId,
});

// Define supported wallets with error handling
export const wallets = [
  // In-app wallet with social login options - this will appear as "Social Login" section
  inAppWallet({
    auth: {
      options: [
        "google",
        "apple",
        "facebook",
        "github", // GitHub instead of Discord for better UX
        "email",
        "phone",
        "passkey",
      ],
      mode: "popup", // Use popup for better UX
    },
    metadata: {
      name: "Social Login",
      image: {
        src: "/logo.png", // You can add your app logo here
        alt: "Social Login",
        width: 100,
        height: 100,
      },
    },
  }),
  // Traditional wallet connectors
  createWallet("io.metamask"),
  createWallet("com.coinbase.wallet"),
  // createWallet("io.rabby"),
  // createWallet("me.rainbow"),
  // createWallet("io.zerion.wallet"),
  createWallet("walletConnect"),
];

// Recommended wallets for better ordering in the modal
export const recommendedWallets = [
  inAppWallet({
    auth: {
      options: [
        "google",
        "apple",
        "facebook",
        "github",
        "email",
        "phone",
        "passkey",
      ],
      mode: "popup",
    },
    metadata: {
      name: "Social Login",
    },
  }),
  createWallet("io.metamask"),
  createWallet("com.coinbase.wallet"),
  createWallet("io.rabby"),
  createWallet("me.rainbow"),
];

// Add wallet connection error handler
if (typeof window !== "undefined") {
  // Override console.error temporarily to catch and handle wallet extension errors
  const originalConsoleError = console.error;
  console.error = (...args) => {
    const message = args.join(" ");
    if (
      message.includes(
        "A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received"
      )
    ) {
      // Log as info instead of error for this specific case since it's harmless
      console.info(
        "🔧 Browser extension message channel (suppressed):",
        ...args
      );
      return;
    }
    // Call original console.error for other errors
    originalConsoleError.apply(console, args);
  };

  console.info("🔧 ThirdWeb wallet error handler initialized");
}

// Define supported chains (including testnet chains as per user preference)
export const chains = [
  // Ethereum and Layer 2s
  ethereum,
  arbitrum,
  optimism,
  base,

  // Alternative Layer 1s
  polygon,
  bsc,
  avalanche,
  fantom,

  // Testnets
  sepolia,
  arbitrumSepolia,
  optimismSepolia,
  baseSepolia,
  polygonAmoy,
  bscTestnet,
];

// Export individual chains for easy access
export {
  ethereum,
  polygon,
  sepolia,
  polygonAmoy,
  arbitrum,
  optimism,
  base,
  bsc,
  avalanche,
  fantom,
  arbitrumSepolia,
  optimismSepolia,
  baseSepolia,
  bscTestnet,
};
