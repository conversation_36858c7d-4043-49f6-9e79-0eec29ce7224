import { ConnectButton } from "thirdweb/react";
import { toast } from "@/hooks/use-toast";
import { useTheme } from "@/hooks/use-theme";
import {
  client,
  wallets,
  recommendedWallets,
  chains,
  ethereum,
  polygon,
  sepolia,
  polygonAmoy,
} from "@/lib/thirdweb";

/**
 * Enhanced Top Wallet Button Component
 * Uses thirdweb's native wallet modal with Send, Receive, Buy, View Assets, Manage Wallet, and Disconnect functionality
 */
const TopWalletButton = () => {
  const { theme } = useTheme();

  const handleConnect = () => {
    // Optional: Add connection success toast
  };

  const handleDisconnect = () => {
    toast({
      title: "Wallet disconnected",
      description: "Your wallet has been disconnected",
    });
  };

  return (
    <ConnectButton
      client={client}
      wallets={wallets}
      recommendedWallets={recommendedWallets}
      chains={chains}
      theme={theme}
      onConnect={handleConnect}
      onDisconnect={handleDisconnect}
      showAllWallets={true}
      accountAbstraction={{
        chain: sepolia, // Smart account chain
        sponsorGas: true, // Enable gasless transactions for all wallets
      }}
      connectButton={{
        label: "Sign In",
        className:
          "bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-none px-6 py-2 rounded-lg font-medium transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl",
        style: {
          minWidth: "auto !important",
          height: "auto !important",
          fontSize: "inherit !important",
        },
      }}
      connectModal={{
        title: "Sign In to Web3AI",
        size: "wide",
      }}
      detailsButton={{
        className:
          "flex items-center gap-3 px-4 py-2 h-auto bg-card/80 border-border hover:bg-primary/10 hover:border-primary/40 hover:shadow-[0_4px_12px_hsl(var(--primary)/0.2)] hover:-translate-y-px transition-all duration-200 backdrop-blur-sm",
      }}
      detailsModal={{
        assetTabs: ["token", "nft"],
        hideSendFunds: false,
        hideReceiveFunds: false,
        hideBuyFunds: false,
      }}
      supportedTokens={{
        [ethereum.id]: [
          {
            address: "******************************************",
            name: "USD Coin",
            symbol: "USDC",
            icon: "https://cryptologos.cc/logos/usd-coin-usdc-logo.png",
          },
        ],
        [polygon.id]: [
          {
            address: "******************************************",
            name: "USD Coin",
            symbol: "USDC",
            icon: "https://cryptologos.cc/logos/usd-coin-usdc-logo.png",
          },
        ],
        [sepolia.id]: [
          {
            address: "******************************************",
            name: "Test USDC",
            symbol: "USDC",
            icon: "https://cryptologos.cc/logos/usd-coin-usdc-logo.png",
          },
        ],
        [polygonAmoy.id]: [
          {
            address: "******************************************",
            name: "Test USDC",
            symbol: "USDC",
            icon: "https://cryptologos.cc/logos/usd-coin-usdc-logo.png",
          },
        ],
      }}
    />
  );
};

export default TopWalletButton;
