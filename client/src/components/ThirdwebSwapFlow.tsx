import { useState } from "react";
import ThirdwebApprovalBox from "./ThirdwebApprovalBox";
import ThirdwebSwapBox from "./ThirdwebSwapBox";

interface SwapFlowData {
  fromToken: {
    symbol: string;
    address: string;
    amount: string;
    decimals: number;
  };
  toToken: {
    symbol: string;
    address: string;
    amount: string;
    decimals: number;
  };
  chainId: number;
  routerAddress: string; // The contract that needs approval
  needsApproval?: boolean;
}

interface ThirdwebSwapFlowProps {
  swapData: SwapFlowData;
  onSwapComplete?: (txHash: string) => void;
  className?: string;
}

const ThirdwebSwapFlow = ({
  swapData,
  onSwapComplete,
  className = "",
}: ThirdwebSwapFlowProps) => {
  const [approvalTxHash, setApprovalTxHash] = useState<string | null>(null);
  const [swapTxHash, setSwapTxHash] = useState<string | null>(null);
  const [isApprovalComplete, setIsApprovalComplete] = useState(false);

  const handleApprovalComplete = (txHash: string) => {
    setApprovalTxHash(txHash);
    setIsApprovalComplete(true);
  };

  const handleSwapExecute = async (swapData: any) => {
    // This would integrate with your existing swap logic
    console.log("Executing swap:", swapData);

    // Simulate swap execution
    await new Promise((resolve) => setTimeout(resolve, 2000));
    const mockTxHash = "0x" + Math.random().toString(16).substr(2, 64);
    setSwapTxHash(mockTxHash);
    onSwapComplete?.(mockTxHash);
    return mockTxHash;
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Step 1: Approval (if needed) */}
      {swapData.needsApproval !== false && (
        <ThirdwebApprovalBox
          approvalData={{
            tokenAddress: swapData.fromToken.address,
            spenderAddress: swapData.routerAddress,
            amount: swapData.fromToken.amount,
            symbol: swapData.fromToken.symbol,
            chainId: swapData.chainId,
            toBuyAmount: swapData.toToken.amount,
            toBuySymbol: swapData.toToken.symbol,
          }}
          onApprovalComplete={handleApprovalComplete}
          transactionHash={approvalTxHash}
        />
      )}

      {/* Step 2: Swap */}
      <ThirdwebSwapBox
        title="Swap"
        swapData={{
          fromToken: swapData.fromToken,
          toToken: swapData.toToken,
          chainId: swapData.chainId,
        }}
        onExecute={handleSwapExecute}
        transactionHash={swapTxHash}
        usePayEmbed={false}
      />
    </div>
  );
};

export default ThirdwebSwapFlow;
