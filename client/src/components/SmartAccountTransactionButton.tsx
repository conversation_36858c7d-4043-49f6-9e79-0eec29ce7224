import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { 
  useActiveAccount, 
  useActiveWallet,
  useSendTransaction,
} from "thirdweb/react";
import { sendTransaction, waitForReceipt } from "thirdweb";
import type { PreparedTransaction } from "thirdweb";

interface SmartAccountTransactionButtonProps {
  transaction: PreparedTransaction;
  onTransactionSent?: (result: { transactionHash: string }) => void;
  onTransactionConfirmed?: () => void;
  onError?: (error: Error) => void;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
}

/**
 * Enhanced TransactionButton that properly handles smart account authorization
 * Fixes the 4100 error by implementing proper smart account flow
 */
const SmartAccountTransactionButton: React.FC<SmartAccountTransactionButtonProps> = ({
  transaction,
  onTransactionSent,
  onTransactionConfirmed,
  onError,
  children,
  className = "",
  disabled = false,
}) => {
  const { toast } = useToast();
  const account = useActiveAccount();
  const wallet = useActiveWallet();
  const [isExecuting, setIsExecuting] = useState(false);

  const handleExecute = async () => {
    if (!account || !wallet || !transaction) {
      const error = new Error("No active account, wallet, or transaction");
      onError?.(error);
      return;
    }

    setIsExecuting(true);

    try {
      // Check if this is a smart account
      const isSmartAccount = account.address !== wallet.getAccount()?.address;
      
      console.log("Account type:", {
        accountAddress: account.address,
        walletAddress: wallet.getAccount()?.address,
        isSmartAccount,
        walletId: wallet.id,
      });

      let txResult;

      if (isSmartAccount) {
        // Handle smart account transaction
        console.log("Executing smart account transaction...");
        
        // For smart accounts, we need to use the wallet's sendTransaction method
        // which handles the authorization internally
        txResult = await sendTransaction({
          transaction,
          account,
        });
      } else {
        // Handle regular EOA transaction
        console.log("Executing regular EOA transaction...");
        
        txResult = await sendTransaction({
          transaction,
          account,
        });
      }

      console.log("Transaction sent:", txResult);

      // Notify transaction sent
      onTransactionSent?.({ transactionHash: txResult.transactionHash });

      toast({
        title: "Transaction Submitted",
        description: `Transaction hash: ${txResult.transactionHash}`,
      });

      // Wait for confirmation
      const receipt = await waitForReceipt(txResult);
      console.log("Transaction confirmed:", receipt);

      // Notify transaction confirmed
      onTransactionConfirmed?.();

      toast({
        title: "Transaction Confirmed",
        description: "Your transaction has been confirmed on the blockchain",
      });

    } catch (error: any) {
      console.error("Transaction failed:", error);
      
      // Handle specific smart account errors
      if (error.code === 4100) {
        const smartAccountError = new Error(
          "Smart account authorization failed. Please try connecting your wallet again."
        );
        onError?.(smartAccountError);
        
        toast({
          title: "Authorization Error",
          description: "Smart account needs authorization. Please reconnect your wallet.",
          variant: "destructive",
        });
      } else {
        onError?.(error);
        
        toast({
          title: "Transaction Failed",
          description: error.message || "Failed to execute transaction",
          variant: "destructive",
        });
      }
    } finally {
      setIsExecuting(false);
    }
  };

  return (
    <Button
      onClick={handleExecute}
      disabled={!account || disabled || isExecuting}
      className={`${className} ${isExecuting ? 'opacity-50' : ''}`}
    >
      {isExecuting ? (
        <div className="flex items-center">
          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
          Executing...
        </div>
      ) : (
        children
      )}
    </Button>
  );
};

export default SmartAccountTransactionButton;
