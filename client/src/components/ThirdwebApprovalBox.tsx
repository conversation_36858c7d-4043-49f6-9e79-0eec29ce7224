import { useState, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { CheckCircle, AlertCircle, Check } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import {
  useActiveAccount,
  TransactionButton,
  useWaitForReceipt,
} from "thirdweb/react";
import {
  prepareTransaction,
  define<PERSON>hain,
  getContract,
  prepareContractCall,
} from "thirdweb";
import { approve } from "thirdweb/extensions/erc20";
import { client } from "@/lib/thirdweb";
import { getCachedChainMetadata } from "@/lib/chainConfig";

interface ApprovalData {
  tokenAddress: string;
  spenderAddress: string; // Router/contract that needs approval
  amount: string;
  symbol: string;
  chainId: number;
  toBuyAmount?: string;
  toBuySymbol?: string;
}

interface ThirdwebApprovalBoxProps {
  approvalData: ApprovalData;
  onApprovalComplete?: (txHash: string) => void;
  transactionHash?: string | null;
  className?: string;
}

const ThirdwebApprovalBox = ({
  approvalData,
  onApprovalComplete,
  transactionHash,
  className = "",
}: ThirdwebApprovalBoxProps) => {
  const { toast } = useToast();
  const activeAccount = useActiveAccount();
  const [txHash, setTxHash] = useState<string | null>(transactionHash);

  // Get chain metadata
  const chainMetadata = getCachedChainMetadata(approvalData.chainId);
  const chainName = chainMetadata?.name || `Chain ${approvalData.chainId}`;

  // Format chain name to be more concise like Nebula
  const formatChainName = (name: string) => {
    if (name.includes("Ethereum")) return "Chain 1";
    if (name.includes("Polygon")) return "Chain 137";
    if (name.includes("BSC") || name.includes("Binance")) return "Chain 56";
    return name.length > 15 ? `Chain ${approvalData.chainId}` : name;
  };

  const displayChainName = formatChainName(chainName);

  // Prepare the approval transaction
  const preparedTransaction = useMemo(() => {
    try {
      const chain = defineChain(approvalData.chainId);

      // Get the ERC20 contract
      const tokenContract = getContract({
        client,
        chain,
        address: approvalData.tokenAddress,
      });

      // Prepare approval for maximum amount (or specific amount)
      const approvalTx = approve({
        contract: tokenContract,
        spender: approvalData.spenderAddress,
        amount: BigInt(
          "115792089237316195423570985008687907853269984665640564039457584007913129639935"
        ), // Max uint256
      });

      return approvalTx;
    } catch (error) {
      console.error("Failed to prepare approval transaction:", error);
      return null;
    }
  }, [approvalData]);

  // Wait for receipt if we have a transaction hash
  const receiptQuery = useWaitForReceipt({
    client,
    chain: defineChain(approvalData.chainId),
    transactionHash: txHash as `0x${string}`,
  });

  const handleTransactionSent = (result: { transactionHash: string }) => {
    setTxHash(result.transactionHash);
    toast({
      title: "Approval Submitted",
      description: `Approving ${approvalData.symbol} spending`,
    });
  };

  const handleTransactionConfirmed = () => {
    toast({
      title: "Approval Confirmed",
      description: `${approvalData.symbol} spending approved successfully!`,
    });

    if (txHash) {
      onApprovalComplete?.(txHash);
    }
  };

  const handleError = (error: Error) => {
    console.error("Approval failed:", error);
    toast({
      title: "Approval Failed",
      description: error.message || "Failed to approve token spending",
      variant: "destructive",
    });
  };

  const isApproved = receiptQuery.data;

  return (
    <div className={`nebula-transaction-card max-w-lg w-full ${className}`}>
      {/* Header */}
      <div className="text-sm font-medium mb-1">Approve</div>
      <div className="text-xs text-muted-foreground mb-4">
        Approve spending to swap tokens on your behalf
      </div>

      {/* Approval Details */}
      <div className="space-y-0 mb-4">
        {/* Approve Spending Section */}
        <div className="flex items-center justify-between py-3 border-b border-border/30">
          <div>
            <div className="text-sm text-muted-foreground mb-1">
              Approve Spending
            </div>
            <div className="font-medium text-base">
              {approvalData.amount} {approvalData.symbol}
            </div>
          </div>
          <div className="flex items-center text-sm text-muted-foreground">
            <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
            <span>{displayChainName}</span>
          </div>
        </div>

        {/* To Buy Section */}
        {approvalData.toBuyAmount && approvalData.toBuySymbol && (
          <div className="flex items-center justify-between py-3">
            <div>
              <div className="text-sm text-muted-foreground mb-1">To Buy</div>
              <div className="font-medium text-base">
                {approvalData.toBuyAmount} {approvalData.toBuySymbol}
              </div>
            </div>
            <div className="flex items-center text-sm text-muted-foreground">
              <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
              <span>{displayChainName}</span>
            </div>
          </div>
        )}
      </div>

      {/* Status */}
      {txHash && (
        <div className="mb-4 p-3 bg-muted/20 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Status</span>
            <div className="flex items-center">
              {receiptQuery.isLoading ? (
                <>
                  <div className="w-3 h-3 border border-current border-t-transparent rounded-full animate-spin mr-2" />
                  <span className="text-yellow-500 text-sm">Pending</span>
                </>
              ) : receiptQuery.data ? (
                <>
                  <CheckCircle className="h-3 w-3 text-green-500 mr-2" />
                  <span className="text-green-500 text-sm">Approved</span>
                </>
              ) : receiptQuery.error ? (
                <>
                  <AlertCircle className="h-3 w-3 text-red-500 mr-2" />
                  <span className="text-red-500 text-sm">Failed</span>
                </>
              ) : null}
            </div>
          </div>
        </div>
      )}

      {/* Action Button */}
      <div className="pt-3 border-t border-border/50">
        {isApproved ? (
          <Button
            className="w-full bg-green-600 hover:bg-green-700 text-white"
            disabled
          >
            ✓ Approved
          </Button>
        ) : preparedTransaction ? (
          <TransactionButton
            transaction={preparedTransaction}
            onTransactionSent={handleTransactionSent}
            onTransactionConfirmed={handleTransactionConfirmed}
            onError={handleError}
            className="w-full nebula-action-button text-primary-foreground"
          >
            ✓ Approve
          </TransactionButton>
        ) : (
          <Button
            className="w-full nebula-action-button text-primary-foreground"
            disabled
          >
            ✓ Approve
          </Button>
        )}
      </div>
    </div>
  );
};

export default ThirdwebApprovalBox;
