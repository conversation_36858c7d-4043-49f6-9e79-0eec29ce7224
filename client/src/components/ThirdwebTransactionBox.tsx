import { useState, useEffect, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { ExternalLink, CheckCircle, AlertCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import {
  useActiveAccount,
  TransactionButton,
  useSendTransaction,
  useWaitForReceipt,
} from "thirdweb/react";
import { prepareTransaction, toHex, defineChain } from "thirdweb";
import { client } from "@/lib/thirdweb";
import {
  getExplorerUrl,
  getChainById,
  getCachedChainMetadata,
} from "@/lib/chainConfig";
import SmartAccountTransactionButton from "./SmartAccountTransactionButton";

interface TransactionData {
  from?: string;
  to?: string;
  value?: string | bigint;
  chainId?: number;
  data?: string;
  gasLimit?: string;
  gasPrice?: string;
  maxFeePerGas?: string;
  maxPriorityFeePerGas?: string;
  nonce?: number;
  description?: string;
  action?: string;
}

interface ThirdwebTransactionBoxProps {
  title?: string;
  transactionData: TransactionData;
  onExecute?: (txData: TransactionData) => Promise<string | void>;
  isExecuted?: boolean;
  transactionHash?: string | null;
  className?: string;
}

const ThirdwebTransactionBox = ({
  title = "Transaction",
  transactionData,
  onExecute,
  isExecuted = false,
  transactionHash,
  className = "",
}: ThirdwebTransactionBoxProps) => {
  const { toast } = useToast();
  const activeAccount = useActiveAccount();
  const [chainName, setChainName] = useState<string>("Unknown Chain");
  const [tokenSymbol, setTokenSymbol] = useState<string>("ETH");
  const [txHash, setTxHash] = useState<string | null>(transactionHash);

  // Prepare the transaction for thirdweb
  const preparedTransaction = useMemo(() => {
    if (!transactionData.chainId) return null;

    try {
      const chain = defineChain(transactionData.chainId);

      return prepareTransaction({
        to: transactionData.to as `0x${string}`,
        value: BigInt(transactionData.value || "0"),
        data: transactionData.data as `0x${string}` | undefined,
        gas: transactionData.gasLimit
          ? BigInt(transactionData.gasLimit)
          : undefined,
        gasPrice: transactionData.gasPrice
          ? BigInt(transactionData.gasPrice)
          : undefined,
        maxFeePerGas: transactionData.maxFeePerGas
          ? BigInt(transactionData.maxFeePerGas)
          : undefined,
        maxPriorityFeePerGas: transactionData.maxPriorityFeePerGas
          ? BigInt(transactionData.maxPriorityFeePerGas)
          : undefined,
        nonce: transactionData.nonce,
        chain,
        client,
      });
    } catch (error) {
      console.error("Failed to prepare transaction:", error);
      return null;
    }
  }, [transactionData]);

  // Wait for receipt if we have a transaction hash
  const receiptQuery = useWaitForReceipt({
    client,
    chain: transactionData.chainId
      ? defineChain(transactionData.chainId)
      : undefined,
    transactionHash: txHash as `0x${string}`,
  });

  useEffect(() => {
    if (transactionData.chainId) {
      const chainMetadata = getCachedChainMetadata(transactionData.chainId);
      if (chainMetadata) {
        setChainName(chainMetadata.name);
        setTokenSymbol(chainMetadata.nativeCurrency?.symbol || "ETH");
      }
    }
  }, [transactionData.chainId]);

  const formatAddress = (address?: string) => {
    if (!address) return "Not specified";
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const formatValue = (value?: string | bigint) => {
    if (!value) return "0";
    try {
      const valueInEth = Number(value) / 1e18;
      return valueInEth.toFixed(6);
    } catch {
      return "0";
    }
  };

  const handleViewOnExplorer = () => {
    if (txHash) {
      const explorerUrl = getExplorerUrl(txHash, transactionData.chainId);
      window.open(explorerUrl, "_blank");
    }
  };

  const handleTransactionSent = (result: { transactionHash: string }) => {
    setTxHash(result.transactionHash);
    toast({
      title: "Transaction Submitted",
      description: `Transaction hash: ${result.transactionHash}`,
    });

    // Call the original onExecute if provided
    if (onExecute) {
      onExecute(transactionData);
    }
  };

  const handleTransactionConfirmed = () => {
    toast({
      title: "Transaction Confirmed",
      description: "Your transaction has been confirmed on the blockchain",
    });
  };

  const handleError = (error: Error) => {
    console.error("Transaction failed:", error);
    toast({
      title: "Transaction Failed",
      description: error.message || "Failed to execute transaction",
      variant: "destructive",
    });
  };

  return (
    <div className={`nebula-transaction-card ${className}`}>
      {/* Header */}
      <div className="text-sm font-medium mb-3 flex items-center justify-between">
        <span>{title}</span>
        {receiptQuery.data && (
          <CheckCircle className="h-4 w-4 text-green-500" />
        )}
      </div>

      {/* Description */}
      {transactionData.description && (
        <div className="text-xs text-muted-foreground mb-3 p-2 bg-muted/30 rounded">
          {transactionData.description}
        </div>
      )}

      {/* Transaction Details */}
      <div className="space-y-2 mb-4">
        {/* From Address */}
        <div className="nebula-transaction-row">
          <div className="nebula-transaction-label">From</div>
          <div className="nebula-transaction-value flex items-center">
            <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
            <span>
              {formatAddress(transactionData.from || activeAccount?.address)}
            </span>
          </div>
        </div>

        {/* To Address */}
        <div className="nebula-transaction-row">
          <div className="nebula-transaction-label">To</div>
          <div className="nebula-transaction-value flex items-center">
            <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
            <span>{formatAddress(transactionData.to)}</span>
          </div>
        </div>

        {/* Value */}
        <div className="nebula-transaction-row">
          <div className="nebula-transaction-label">Value</div>
          <div className="nebula-transaction-value">
            {formatValue(transactionData.value)} {tokenSymbol}
          </div>
        </div>

        {/* Network */}
        <div className="nebula-transaction-row">
          <div className="nebula-transaction-label">Network</div>
          <div className="nebula-transaction-value flex items-center">
            <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
            <span>{chainName}</span>
          </div>
        </div>

        {/* Transaction Hash */}
        {txHash && (
          <div className="nebula-transaction-row">
            <div className="nebula-transaction-label">Transaction Hash</div>
            <div className="nebula-transaction-value">
              <button
                onClick={handleViewOnExplorer}
                className="text-blue-400 hover:text-blue-300 underline text-xs"
              >
                {formatAddress(txHash)} ↗
              </button>
            </div>
          </div>
        )}

        {/* Status */}
        {txHash && (
          <div className="nebula-transaction-row">
            <div className="nebula-transaction-label">Status</div>
            <div className="nebula-transaction-value flex items-center">
              {receiptQuery.isLoading ? (
                <>
                  <div className="w-3 h-3 border border-current border-t-transparent rounded-full animate-spin mr-2" />
                  <span className="text-yellow-500">Pending</span>
                </>
              ) : receiptQuery.data ? (
                <>
                  <CheckCircle className="h-3 w-3 text-green-500 mr-2" />
                  <span className="text-green-500">Confirmed</span>
                </>
              ) : receiptQuery.error ? (
                <>
                  <AlertCircle className="h-3 w-3 text-red-500 mr-2" />
                  <span className="text-red-500">Failed</span>
                </>
              ) : null}
            </div>
          </div>
        )}
      </div>

      {/* Action Button */}
      <div className="pt-3 border-t border-border/50">
        {txHash ? (
          <Button
            className="w-full bg-green-600 hover:bg-green-700 text-white"
            onClick={handleViewOnExplorer}
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            View on Explorer
          </Button>
        ) : preparedTransaction ? (
          <SmartAccountTransactionButton
            transaction={preparedTransaction}
            onTransactionSent={handleTransactionSent}
            onTransactionConfirmed={handleTransactionConfirmed}
            onError={handleError}
            className="w-full nebula-action-button text-primary-foreground"
          >
            {transactionData.action === "approval"
              ? "Approve Token"
              : transactionData.action === "sell"
              ? "Swap Tokens"
              : "Execute Transaction"}
          </SmartAccountTransactionButton>
        ) : (
          <Button
            className="w-full nebula-action-button text-primary-foreground"
            disabled
          >
            Invalid Transaction
          </Button>
        )}
      </div>
    </div>
  );
};

export default ThirdwebTransactionBox;
